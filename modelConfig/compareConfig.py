import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
import os

def load_and_prepare_data(file_path):
    """
    加载Excel文件并准备数据
    """
    df = pd.read_excel(file_path)
    # 确保CPV列为数值类型
    df['CPV'] = pd.to_numeric(df['CPV'], errors='coerce')
    # 确保年份列为数值类型
    df['年份'] = pd.to_numeric(df['年份'], errors='coerce')
    return df

def compare_cpv_by_year(df, market_id=None):
    """
    比较不同年份同一配置项在相同细分市场下的CPV值差异

    Parameters:
    -----------
    df : DataFrame
        ��含配置项数据的DataFrame
    market_id : float, optional
        指定细分市场ID，如果为None则分析所有市场

    Returns:
    --------
    DataFrame
        包含CPV差异分析结果的DataFrame
    """
    # 删除CPV和年份为空的行
    df = df.dropna(subset=['CPV', '年份'])

    # 如果指定了市场ID，则过滤数据
    if market_id is not None:
        df = df[df['细分市场ID'] == market_id]

    # 获取所有不同的年份并排序
    years = sorted(df['年份'].unique().tolist())
    if len(years) < 2:
        raise ValueError("数据中需要至少包含两个不同的年份才能进行比较")

    try:
        # 按配置项ID和年份分组，计算CPV值
        cpv_comparison = df.pivot_table(
            index=['配置项ID', '配置项全称', '细分市场全称'],
            columns='年份',
            values='CPV',
            aggfunc='first'
        ).reset_index()

        # 计算年份间的CPV差异
        for i in range(len(years)-1):
            year1, year2 = years[i], years[i+1]
            diff_col = f'CPV差异({int(year2)}-{int(year1)})'
            cpv_comparison[diff_col] = cpv_comparison[year2].astype(float) - cpv_comparison[year1].astype(float)
            cpv_comparison[f'变化率%({int(year2)}-{int(year1)})'] = (
                (cpv_comparison[year2].astype(float) - cpv_comparison[year1].astype(float)) /
                cpv_comparison[year1].astype(float) * 100
            ).round(2)

        return cpv_comparison
    except Exception as e:
        print(f"处理数据时出错: {str(e)}")
        print("数据示例:")
        print(df.head())
        print("\n年份列的唯一值:")
        print(years)
        raise e

def visualize_cpv_changes(comparison_df, top_n=20):
    """
    可视化CPV变化最大的配置项
    """
    # 获取最新的两个年份之间的差异列名
    diff_cols = [col for col in comparison_df.columns if isinstance(col, str) and 'CPV差异' in col]
    if not diff_cols:
        print("没有找到CPV差异列")
        return None

    latest_diff_col = diff_cols[-1]

    try:
        # 确保CPV差异列为数值类型
        comparison_df[latest_diff_col] = pd.to_numeric(comparison_df[latest_diff_col], errors='coerce')

        # 计算绝对值并排序
        abs_diff = comparison_df[latest_diff_col].abs()
        top_changes = comparison_df.iloc[abs_diff.nlargest(top_n).index]

        # 创建可视化
        plt.figure(figsize=(15, 8))
        bars = plt.barh(top_changes['配置项全称'], top_changes[latest_diff_col])
        plt.title(f'Top {top_n} CPV变化最大的配置项')
        plt.xlabel('CPV差异值')
        plt.ylabel('配置项')

        # 为每个条形添加数值标签
        for bar in bars:
            width = bar.get_width()
            plt.text(width, bar.get_y() + bar.get_height()/2,
                    f'{width:,.0f}',
                    ha='left' if width >= 0 else 'right',
                    va='center', fontsize=8)

        # 保存��表
        save_dir = r"D:\PycahrmProjects\LangChain\trainModel"
        output_file = os.path.join(save_dir, f'cpv_changes_{pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")}.png')
        plt.tight_layout()
        plt.savefig(output_file)
        plt.close()

        return output_file
    except Exception as e:
        print(f"创建可视化图表时出错: {str(e)}")
        print("\n数据示例:")
        print(comparison_df[['配置项全称', latest_diff_col]].head())
        print(f"\n{latest_diff_col}列的数据类型:", comparison_df[latest_diff_col].dtype)
        raise e

def create_cpv_difference_statistics(comparison_df):
    """
    创建CPV差值分布统计图表，分别统计不同年份间的差异
    """
    try:
        # 获取所有CPV差异列
        diff_cols = [col for col in comparison_df.columns if isinstance(col, str) and 'CPV差异' in col]
        if not diff_cols:
            print("没有找到CPV差异列")
            return None, None

        # 定义区间
        bins = [-float('inf'), 0, 50, 100, 200, 500, float('inf')]
        labels = ['无变化', '1-50', '51-100', '101-200', '200-500', '500以上']

        # 设置中文字体和图表样式
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
        plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号
        plt.rcParams['figure.autolayout'] = True      # 自动调整布局

        # 创建多个子图
        fig, axes = plt.subplots(len(diff_cols), 1, figsize=(12, 6*len(diff_cols)))
        if len(diff_cols) == 1:
            axes = [axes]

        stats_dfs = []  # 用于存储各年份的统计结果

        # 处理每个年份差异
        for idx, diff_col in enumerate(diff_cols):
            # 确保CPV差异列为数值类型
            comparison_df[diff_col] = pd.to_numeric(comparison_df[diff_col], errors='coerce')

            # 计算绝对差值并进行分类统计
            abs_diff = comparison_df[diff_col].abs()
            categories = pd.cut(abs_diff, bins=bins, labels=labels, right=True)
            value_counts = categories.value_counts().sort_index()

            # 创建子图
            ax = axes[idx]
            bars = ax.bar(value_counts.index, value_counts.values, color='skyblue', alpha=0.7)
            ax.set_title(f'{diff_col}分布', pad=20)
            ax.set_xlabel('CPV差值区间')
            ax.set_ylabel('配置项数量')

            # 设置x轴标签旋转
            ax.tick_params(axis='x', rotation=45)

            # 为每个柱子添加数值标签
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                        f'{int(height)}',
                        ha='center', va='bottom')

            # 设置y轴从0开始
            ax.set_ylim(bottom=0)

            # 添加网格线
            ax.grid(True, axis='y', linestyle='--', alpha=0.3)

            # 创建统计结果DataFrame
            stats_df = pd.DataFrame({
                '年份': diff_col,
                'CPV差值区间': value_counts.index,
                '配置项数量': value_counts.values,
                '占比': (value_counts.values / len(comparison_df) * 100).round(2)
            })
            stats_dfs.append(stats_df)

            # 打印统计表格
            print("\n" + "="*50)
            print(f"{diff_col}分布统计表")
            print("="*50)
            print(f"{'CPV差值区间':<12}{'配置项数量':<12}{'占比(%)':<10}")
            print("-"*50)
            for _, row in stats_df.iterrows():
                print(f"{row['CPV差值区间']:<12}{row['配置项数量']:<12}{row['占比']:>6.2f}%")
            print("="*50)
            print(f"总配置项数量: {len(comparison_df)}")
            print("="*50)

        # 设置总标题
        fig.suptitle('各年份配置项CPV差值分布统计对比', fontsize=16, y=1.02)

        # 调整子图之间的间距
        plt.tight_layout()

        # 设置保存路径
        save_dir = r"D:\PycahrmProjects\LangChain\trainModel"

        # 保存图表
        output_file = os.path.join(save_dir, f'cpv_difference_stats_{pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")}.png')
        plt.savefig(output_file, bbox_inches='tight', dpi=300)
        plt.close()

        # 合并所有统计结果并保存到Excel
        stats_df_combined = pd.concat(stats_dfs, ignore_index=True)
        stats_output_file = os.path.join(save_dir, f'cpv_stats_{pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")}.xlsx')
        stats_df_combined.to_excel(stats_output_file, index=False)

        return output_file, stats_output_file

    except Exception as e:
        print(f"创建统计图表时出错: {str(e)}")
        print("\n数据示例:")
        print(comparison_df.head())
        print("\n列名:")
        print(comparison_df.columns.tolist())
        raise e

def main():
    # 文件路径
    file_path = r"C:\Users\<USER>\Desktop\配置明细-对比.xlsx"
    save_dir = r"D:\PycahrmProjects\LangChain\trainModel"

    try:
        # 加载数据
        df = load_and_prepare_data(file_path)

        # 对比分析
        comparison_results = compare_cpv_by_year(df)

        # 保存结果到Excel
        output_file = os.path.join(save_dir, f'cpv_comparison_{pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")}.xlsx')
        comparison_results.to_excel(output_file, index=False)
        print(f"分析结果��保存到: {output_file}")

        # 创建CPV变化趋势图
        plot_file = os.path.join(save_dir, f'cpv_changes_{pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")}.png')
        visualize_cpv_changes(comparison_results)
        print(f"CPV变化趋势图已保存到: {plot_file}")

        # 创建CPV差值分布统计图
        stats_plot_file, stats_excel_file = create_cpv_difference_statistics(comparison_results)
        print(f"CPV差值分布统计图已保存到: {stats_plot_file}")
        print(f"CPV差值统计数据已保存到: {stats_excel_file}")

        # 显示���些基本统计信息
        print("\n基本统计信息:")
        print(f"总配置项数量: {len(comparison_results)}")

        # 获取最新的差异列
        diff_cols = [col for col in comparison_results.columns if 'CPV差异' in col]
        if diff_cols:
            latest_diff = diff_cols[-1]
            print(f"\n最大CPV增长的配置项:")
            print(comparison_results.nlargest(5, latest_diff)[['配置项全称', latest_diff]])
            print(f"\n最大CPV下降的配置项:")
            print(comparison_results.nsmallest(5, latest_diff)[['配置项全称', latest_diff]])

    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")
        raise e  # 抛出异常以显示详细的错误信息

if __name__ == "__main__":
    main()
