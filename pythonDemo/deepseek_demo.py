import os
from openai import OpenAI
from typing import List

# 配置API密钥和基础URL
os.environ["OPENAI_API_KEY"] = "sk-awbxpcoxjgcvswaupencngodsxuhbbuswjocamzcmgbkkelw"

# 初始化OpenAI客户端
client = OpenAI(
    api_key=os.getenv("OPENAI_API_KEY"),
    base_url="https://api.siliconflow.cn/v1"
)

def get_completion(
    prompt: str,
    model: str = "deepseek-ai/deepseek-v3",
    temperature: float = 0.7,
    max_tokens: int = 2000
) -> str:
    """
    使用DeepSeek-V3模型生成文本完成

    Args:
        prompt (str): 输入提示文本
        model (str): 模型名称
        temperature (float): 采样温度，控制输出的随机性
        max_tokens (int): 生成的最大标记数

    Returns:
        str: 模型生成的响应文本
    """
    try:
        response = client.chat.completions.create(
            model=model,
            messages=[{"role": "user", "content": prompt}],
            temperature=temperature,
            max_tokens=max_tokens
        )
        return response.choices[0].message.content
    except Exception as e:
        print(f"Error occurred: {str(e)}")
        return ""

def get_chat_completion(
    messages: List[dict],
    model: str = "deepseek-ai/deepseek-v3",
    temperature: float = 0.7,
    max_tokens: int = 2000
) -> str:
    """
    使用DeepSeek-V3模型进���多轮对话

    Args:
        messages (List[dict]): 对话历史记录列表
        model (str): 模型名称
        temperature (float): 采样温度，控制输出的随机性
        max_tokens (int): 生成的最大标记数

    Returns:
        str: 模型生成的响应文本
    """
    try:
        response = client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=temperature,
            max_tokens=max_tokens
        )
        return response.choices[0].message.content
    except Exception as e:
        print(f"Error occurred: {str(e)}")
        return ""

# 使用示例
if __name__ == "__main__":
    # 单次对话示例
    prompt = "请介绍一下你自己"
    response = get_completion(prompt)
    print("单次对话响应:", response)

    # 多轮对话示例
    messages = [
        {"role": "user", "content": "你好，请问你是谁？"},
        {"role": "assistant", "content": "我是 DeepSeek-V3 语言模型，很高兴为您服务。"},
        {"role": "user", "content": "你能做些什么？"}
    ]
    response = get_chat_completion(messages)
    print("\n多轮对话响应:", response)
