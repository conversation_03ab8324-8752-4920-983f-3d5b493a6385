"""一个用来表示汽车的类"""

class Car:
      """一次模拟汽车的简单尝试"""

      def __init__(self, make, model, year):
          """初始化描述汽车的属性"""
          self.make = make
          self.model = model
          self.year = year
          self.odometer_reading = 0

      def get_descriptive_name(self):
          """返回格式规范的描述性名称"""
          long_name = f"{self.year} {self.make} {self.model}"
          return long_name.title()

      def read_odometer(self):
          """打印一条消息，指出汽车的行驶里程"""
          print(f"This car has {self.odometer_reading} miles on it.")

      def update_odometer(self, mileage):
          """
          将里程表读数设置为指定的值
          拒绝将里程表往回调
          """
          if mileage >= self.odometer_reading:
              self.odometer_reading = mileage
          else:
              print("You can't roll back an odometer!")

      def increment_odometer(self, miles):
          """让里程表读数增加指定的量"""
          self.odometer_reading += miles