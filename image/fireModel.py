import requests
import base64
import os

# 配置信息
API_KEY = "6c0d07a9-3cc5-4ade-8fc5-c363c0174a82"
API_URL = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"  # 确认此URL是否正确，可能需要调整
IMAGE_PATH = r"C:\Users\<USER>\Desktop\车型图片1.png"

# 检查图片是否存在
if not os.path.exists(IMAGE_PATH):
    print(f"错误：图片文件不存在，路径：{IMAGE_PATH}")
    exit(1)


# 将图片转换为Base64
def image_to_base64(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')


# 获取文件扩展名以确定MIME类型
file_extension = os.path.splitext(IMAGE_PATH)[1].lower().replace(".", "")
mime_type = f"image/{file_extension}" if file_extension in ["png", "jpg", "jpeg"] else "image/png"

# 构建请求头
headers = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
}

# 构建请求体
base64_image = image_to_base64(IMAGE_PATH)
payload = {
    "model": "doubao-vision-pro-32k-241028",
    "messages": [
        {
            "role": "user",
            "content": [
                {
                    "image_url": {
                        "url": f"data:{mime_type};base64,{base64_image}"
                    },
                    "type": "image_url"
                },
                {
                    "text": "图片主要讲了什么?",
                    "type": "text"
                }
            ]
        }
    ]
}

print("正在发送请求到火山引擎API...")
try:
    # 发送请求
    response = requests.post(API_URL, json=payload, headers=headers)

    # 检查响应状态
    if response.status_code == 200:
        result = response.json()
        if "choices" in result and len(result["choices"]) > 0:
            print("识别结果:")
            print(result["choices"][0]["message"]["content"])
        else:
            print("响应中没有返回有效内容")
            print("完整响应:", response.json())
    else:
        print(f"请求失败，状态码: {response.status_code}")
        print("错误信息:", response.text)

except Exception as e:
    print(f"请求过程中发生错误: {str(e)}")