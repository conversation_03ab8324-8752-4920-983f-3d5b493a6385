import os
import base64
from openai import OpenAI

# 读取图片并转换为Base64
image_path = r'C:\Users\<USER>\Desktop\车型图片1.png'
with open(image_path, "rb") as image_file:
    base64_image = base64.b64encode(image_file.read()).decode('utf-8')

client = OpenAI(
    api_key="sk-7b9067ef70794d7286da8432be28f18c",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)

completion = client.chat.completions.create(
    model="qwen-vl-max-2025-04-08",
    messages=[
        {"role": "system", "content": "You are a helpful assistant."},
        {
            "role": "user",
            "content": [
                {"type": "text", "text": "提取图片上的内容，解释每个数字代表的含义，并用JSON格式输出"},
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{base64_image}"
                    }
                }
            ]
        },
    ],
    # 如果需要思考过程，取消下面一行的注释
    # extra_body={"enable_thinking": True}
)

print(completion.choices[0].message.content)