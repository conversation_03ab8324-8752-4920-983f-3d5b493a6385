import requests
import json
import base64
import os
from PIL import Image
from io import BytesIO
import math

def resize_and_compress_image(image_path, max_pixels=32000, max_size_mb=2):
    # 打开图片
    img = Image.open(image_path)

    # 如果图片是RGBA模式，转换为RGB
    if img.mode == 'RGBA':
        img = img.convert('RGB')

    # 计算当前像素总数
    current_pixels = img.width * img.height

    # 如果超过最大像素限制，计算新尺寸
    if current_pixels > max_pixels:
        scale = math.sqrt(max_pixels / current_pixels)
        new_width = int(img.width * scale)
        new_height = int(img.height * scale)
        img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
        print(f"调整图片尺寸: {img.width}x{img.height}, 总像素: {img.width * img.height}")

    # 压缩图片质量
    quality = 95
    buffer = BytesIO()

    # 强制使用JPEG格式保存
    while True:
        buffer.seek(0)
        buffer.truncate(0)
        img.save(buffer, format='JPEG', quality=quality)

        size_mb = buffer.tell() / (1024 * 1024)
        print(f"当前文件大小: {size_mb:.2f}MB, 质量: {quality}")

        if size_mb <= max_size_mb or quality <= 5:
            break

        quality -= 5

    buffer.seek(0)
    return buffer.getvalue()

def extract_json_content(content):
    # 在content中查找JSON内容（在```json和```之间的内容）
    import re
    json_match = re.search(r'```json\n(.*?)\n```', content, re.DOTALL)
    if json_match:
        json_str = json_match.group(1)
        # 解析JSON字符串
        return json.loads(json_str)
    return None

def main():
    # 图片路径
    image_path = r"C:\Users\<USER>\Desktop\车型图片1.png"

    if not os.path.exists(image_path):
        print(f"错误：图片文件不存在，路径：{image_path}")
        return

    # 压缩并读取图片
    processed_image = resize_and_compress_image(image_path)
    base64_image = base64.b64encode(processed_image).decode('utf-8')

    file_extension = os.path.splitext(image_path)[1].lower().replace(".", "")
    mime_type = f"image/{file_extension}" if file_extension in ["png", "jpg", "jpeg"] else "image/png"

    url = "https://qianfan.baidubce.com/v2/chat/completions"

    payload = {
        "model": "ernie-4.5-turbo-vl-32k",
        "stream": False,
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:{mime_type};base64,{base64_image}"
                        }
                    },
                    {
                        "type": "text",
                        "text": "提取图片上的内容，解释每个数字代表的含义，并用JSON格式输出"
                    }
                ]
            }
        ]
    }

    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer bce-v3/ALTAK-8bO7LRsxenUTDkUtcWQNK/fa479fc221a781a18f9c7b4049abf1d85fe45e7f'
    }

    try:
        response = requests.post(url, headers=headers, json=payload)

        if response.status_code == 200:
            result = response.json()
            if "choices" in result:
                content = result["choices"][0]["message"]["content"]
                # 提取JSON内容
                json_content = extract_json_content(content)
                if json_content:
                    print("解析后的JSON结果:")
                    print(json.dumps(json_content, indent=2, ensure_ascii=False))
                else:
                    print("原始内容:")
                    print(content)
            else:
                print("完整响应:")
                print(json.dumps(result, indent=2, ensure_ascii=False))

        else:
            print(f"请求失败，状态码: {response.status_code}")
            print("错误信息:", response.text)

    except Exception as e:
        print(f"请求过程中发生错误: {str(e)}")

if __name__ == '__main__':
    main()