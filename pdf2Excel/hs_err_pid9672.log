#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 264241152 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3603), pid=9672, tid=29148
#
# JRE version:  (21.0.6+8) (build )
# Java VM: Java HotSpot(TM) 64-Bit Server VM (21.0.6+8-LTS-188, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -Dfile.encoding=UTF8 D:\PycahrmProjects\LangChain\.venv\Lib\site-packages\tabula\tabula-1.0.5-jar-with-dependencies.jar --pages all --guess --format JSON C:\Users\<USER>\Desktop\Խ�� sales report May 2025 - Detail (1).pdf

Host: 11th Gen Intel(R) Core(TM) i5-1135G7 @ 2.40GHz, 8 cores, 15G,  Windows 10 , 64 bit Build 19041 (10.0.19041.3031)
Time: Tue Jul  8 16:41:11 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.3031) elapsed time: 0.013771 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x00000172e7bace20):  JavaThread "Unknown thread" [_thread_in_vm, id=29148, stack(0x000000316c200000,0x000000316c300000) (1024K)]

Stack: [0x000000316c200000,0x000000316c300000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ca6d9]
V  [jvm.dll+0x854f51]
V  [jvm.dll+0x85747e]
V  [jvm.dll+0x857b53]
V  [jvm.dll+0x27dcc6]
V  [jvm.dll+0x6c6e55]
V  [jvm.dll+0x6bb9da]
V  [jvm.dll+0x35393a]
V  [jvm.dll+0x35b596]
V  [jvm.dll+0x3adefe]
V  [jvm.dll+0x3ae1a8]
V  [jvm.dll+0x327cbc]
V  [jvm.dll+0x32881b]
V  [jvm.dll+0x81e309]
V  [jvm.dll+0x3babe1]
V  [jvm.dll+0x807658]
V  [jvm.dll+0x44fdae]
V  [jvm.dll+0x4519a1]
C  [jli.dll+0x52a3]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17614]
C  [ntdll.dll+0x526f1]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007fff8949cc08, length=0, elements={
}

Java Threads: ( => current thread )
Total: 0

Other Threads:
  0x00000172e9d08700 WorkerThread "GC Thread#0"                     [id=7432, stack(0x000000316c300000,0x000000316c400000) (1024K)]
  0x00000172e9d19420 ConcurrentGCThread "G1 Main Marker"            [id=29248, stack(0x000000316c400000,0x000000316c500000) (1024K)]
  0x00000172e9d1a730 WorkerThread "G1 Conc#0"                       [id=1548, stack(0x000000316c500000,0x000000316c600000) (1024K)]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007fff88c0a427]
VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007fff8950df98] Heap_lock - owner thread: 0x00000172e7bace20

Heap address: 0x0000000704600000, size: 4026 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000704600000, 0x0000000800000000)
  region size 2048K, 0 young (0K), 0 survivors (0K)

[error occurred during error reporting (printing heap information), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007fff88ff36a9]
GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.009 Loaded shared library D:\Program Files\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff66d830000 - 0x00007ff66d840000 	D:\Program Files\bin\java.exe
0x00007fffb28b0000 - 0x00007fffb2aa8000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007fffb1cf0000 - 0x00007fffb1daf000 	C:\Windows\System32\KERNEL32.DLL
0x00007fffb00c0000 - 0x00007fffb03b6000 	C:\Windows\System32\KERNELBASE.dll
0x00007fffaffc0000 - 0x00007fffb00c0000 	C:\Windows\System32\ucrtbase.dll
0x00007fff1be30000 - 0x00007fff1be4b000 	D:\Program Files\bin\VCRUNTIME140.dll
0x00007fff1bd60000 - 0x00007fff1bd79000 	D:\Program Files\bin\jli.dll
0x00007fffb24b0000 - 0x00007fffb255f000 	C:\Windows\System32\ADVAPI32.dll
0x00007fffb0d90000 - 0x00007fffb0e2e000 	C:\Windows\System32\msvcrt.dll
0x00007fffb0b00000 - 0x00007fffb0b9c000 	C:\Windows\System32\sechost.dll
0x00007fffb09d0000 - 0x00007fffb0af6000 	C:\Windows\System32\RPCRT4.dll
0x00007fffb1b50000 - 0x00007fffb1ced000 	C:\Windows\System32\USER32.dll
0x00007fffb07d0000 - 0x00007fffb07f2000 	C:\Windows\System32\win32u.dll
0x00007fffa8c10000 - 0x00007fffa8eaa000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.1110_none_60b5254171f9507e\COMCTL32.dll
0x00007fffb0ef0000 - 0x00007fffb0f1c000 	C:\Windows\System32\GDI32.dll
0x00007fffb03c0000 - 0x00007fffb04d5000 	C:\Windows\System32\gdi32full.dll
0x00007fffb0870000 - 0x00007fffb090d000 	C:\Windows\System32\msvcp_win.dll
0x00007fffabed0000 - 0x00007fffabeda000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007fffb0e30000 - 0x00007fffb0e60000 	C:\Windows\System32\IMM32.DLL
0x00007fff70b50000 - 0x00007fff70b5c000 	D:\Program Files\bin\vcruntime140_1.dll
0x00007fff1b9c0000 - 0x00007fff1ba4e000 	D:\Program Files\bin\msvcp140.dll
0x00007fff888d0000 - 0x00007fff895ea000 	D:\Program Files\bin\server\jvm.dll
0x00007fffb1390000 - 0x00007fffb13fb000 	C:\Windows\System32\WS2_32.dll
0x00007fffaf4c0000 - 0x00007fffaf50b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007fffa8eb0000 - 0x00007fffa8ed7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007fffaf380000 - 0x00007fffaf392000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007fffae6d0000 - 0x00007fffae6e2000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007fff8a570000 - 0x00007fff8a57a000 	D:\Program Files\bin\jimage.dll
0x00007fffae100000 - 0x00007fffae2e4000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007fff90b00000 - 0x00007fff90b34000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007fffb0740000 - 0x00007fffb07c2000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007fff8a0e0000 - 0x00007fff8a0ff000 	D:\Program Files\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Program Files\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.1110_none_60b5254171f9507e;D:\Program Files\bin\server

VM Arguments:
jvm_args: -Dfile.encoding=UTF8 
java_command: D:\PycahrmProjects\LangChain\.venv\Lib\site-packages\tabula\tabula-1.0.5-jar-with-dependencies.jar --pages all --guess --format JSON C:\Users\<USER>\Desktop\Խ�� sales report May 2025 - Detail (1).pdf
java_class_path (initial): D:\PycahrmProjects\LangChain\.venv\Lib\site-packages\tabula\tabula-1.0.5-jar-with-dependencies.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 264241152                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4221566976                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4221566976                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=D:\PycahrmProjects\LangChain\.venv\Scripts;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\TortoiseSVN\bin;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2024.3.2\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Program Files\nodejs;C:\Program Files\OpenSSL-Win64\bin;C:\Program Files\Git\cmd;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts;D:\GitHub\poppler-24.08.0\Library\bin;C:\Program Files\Docker\Docker\resources\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2025.1.1.1\bin;;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Program Files\nodejs;D:\Program Files\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=huzhiheng
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 140 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.3031)
OS uptime: 4 days 4:18 hours
Hyper-V role detected

CPU: total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 140 stepping 1 microcode 0x86, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, clwb, avx512_vbmi2, avx512_vbmi, hv, rdtscp, rdpid, fsrm, gfni, avx512_bitalg, f16c, cet_ibt, cet_ss, avx512_ifma
Processor Information for all 8 processors :
  Max Mhz: 2419, Current Mhz: 907, Mhz Limit: 895

Memory: 4k page, system-wide physical 16103M (2734M free)
TotalPageFile size 20103M (AvailPageFile size 232M)
current process WorkingSet (physical memory assigned to process): 11M, peak: 11M
current process commit charge ("private bytes"): 60M, peak: 312M

vm_info: Java HotSpot(TM) 64-Bit Server VM (21.0.6+8-LTS-188) for windows-amd64 JRE (21.0.6+8-LTS-188), built on 2024-12-03T10:50:54Z by "mach5one" with MS VC++ 17.6 (VS2022)

END.
