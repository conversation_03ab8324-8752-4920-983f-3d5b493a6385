import os
import pandas as pd
from PyPDF2 import PdfReader
import base64
import requests
import json

# Gemini API 配置
API_ENDPOINT = "https://yunwu.ai/v1/chat/completions"
API_KEY = "sk-Mjv9S6FE5oIC3k5F9fxXfXHXWOeaaQcH9qx14IRRGeGEFzzr"
HEADERS = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {API_KEY}"
}

def extract_text_from_pdf(pdf_path):
    """从PDF中提取文本内容"""
    try:
        reader = PdfReader(pdf_path)
        text = ""
        for page in reader.pages:
            text += page.extract_text() + "\n"
        return text
    except Exception as e:
        print(f"Error extracting text from PDF: {e}")
        return None

def extract_tables_with_gemini(text):
    """使用Gemini模型提取表格数据并保持原格式，输出HTML"""
    try:
        prompt = """请从以下文本中提取内容，并按照以下要求处理：

1. 保持原始的格式和结构
2. 输出格式为HTML，使用适当的HTML标签来保持格式
3. 对于表格内容，使用<table>、<tr>、<td>等标签
4. 对于普通文本，使用适当的段落<p>和换行<br>标签
5. 保持原始的缩进和对齐方式
6. 如果有标题，使用<h1>-<h6>标签
7. 添加基本的CSS样式以确保显示效果

以下是需要处理的文本：

{text}

请按照要求提取并格式化内容。"""

        payload = {
            "model": "gemini-2.5-pro-exp-03-25",
            "messages": [
                {
                    "role": "user",
                    "content": prompt.format(text=text)
                }
            ],
            "temperature": 0.3,
            "max_tokens": 4000
        }

        response = requests.post(API_ENDPOINT, headers=HEADERS, json=payload)
        if response.status_code == 200:
            return response.json()["choices"][0]["message"]["content"]
        else:
            print(f"API request failed with status code: {response.status_code}")
            return None
    except Exception as e:
        print(f"Error calling Gemini API: {e}")
        return None

def save_as_html(html_content, output_path):
    """将内容保存为HTML文件"""
    try:
        html_template = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        .container { max-width: 1200px; margin: 0 auto; }
    </style>
</head>
<body>
    <div class="container">
        {content}
    </div>
</body>
</html>
"""
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_template.format(content=html_content))
        print(f"HTML content has been saved to {output_path}")
    except Exception as e:
        print(f"Error saving HTML file: {e}")

def main():
    # 设置输入PDF路径和输出HTML路径
    pdf_path = r"C:\Users\<USER>\Desktop\越南 sales report May 2025 - Detail (1).pdf"
    output_html_path = os.path.join(os.path.dirname(pdf_path), "output_content.html")

    # 提取PDF文本内容
    print("Extracting text from PDF...")
    text_content = extract_text_from_pdf(pdf_path)

    if text_content:
        print("Using Gemini to extract and format content...")
        html_content = extract_tables_with_gemini(text_content)

        if html_content:
            print("Saving content as HTML...")
            save_as_html(html_content, output_html_path)
        else:
            print("Failed to get formatted content from Gemini")
    else:
        print("Failed to extract text from PDF")

if __name__ == "__main__":
    main()
