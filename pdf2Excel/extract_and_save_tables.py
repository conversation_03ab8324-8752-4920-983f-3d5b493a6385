
'''
This script extracts tables from a PDF file and saves them to an Excel file using pdfplumber.
'''
import os
import pdfplumber
import pandas as pd

# --- Configuration ---
PDF_FILE_PATH = r"C:\Users\<USER>\Desktop\越南 sales report May 2025 - Detail (1).pdf"
OUTPUT_EXCEL_NAME = "output.xlsx"

# --- Script ---

def main():
    '''
    Main function to extract tables using pdfplumber and save to Excel.
    '''
    script_dir = os.path.dirname(os.path.abspath(__file__))
    output_excel_path = os.path.join(script_dir, OUTPUT_EXCEL_NAME)

    if not os.path.exists(PDF_FILE_PATH):
        print(f"Error: Input PDF not found at: {PDF_FILE_PATH}")
        return

    print(f"Opening PDF: {PDF_FILE_PATH}")

    try:
        with pdfplumber.open(PDF_FILE_PATH) as pdf:
            # Use a single Excel writer to save all tables to different sheets
            with pd.ExcelWriter(output_excel_path) as writer:
                table_count = 0
                # Iterate through each page
                for i, page in enumerate(pdf.pages):
                    # Extract tables from the page
                    tables = page.extract_tables()
                    if tables:
                        # Iterate through each table found on the page
                        for table in tables:
                            table_count += 1
                            # Convert table data to a pandas DataFrame
                            df = pd.DataFrame(table[1:], columns=table[0])
                            # Write DataFrame to a new sheet in the Excel file
                            sheet_name = f"Page_{i+1}_Table_{table_count}"
                            df.to_excel(writer, sheet_name=sheet_name, index=False)
                            print(f"Processed table {table_count} from page {i+1}")
            
            if table_count > 0:
                print(f"\nSuccess! Found and extracted {table_count} tables.")
                print(f"Data saved to: {output_excel_path}")
            else:
                print("No tables were found in the document.")

    except Exception as e:
        print(f"An error occurred: {e}")
        print("\nPlease ensure you have the required libraries installed:")
        print("pip install pdfplumber pandas openpyxl")

if __name__ == "__main__":
    main()
