import os
import fitz  # PyMuPDF
import pdfplumber
import uuid
from paddleocr import PaddleOCR
from pymilvus import connections, FieldSchema, CollectionSchema, DataType, Collection
import openai
import time

# -------- 配置部分 --------
PDF_DIR = "/path/to/pdf_folder"
MILVUS_HOST = "127.0.0.1"
MILVUS_PORT = "19530"
OPENAI_API_KEY = "your_openai_api_key"
MAX_SLICE_LEN = 500
SLICE_OVERLAP = 0.5

openai.api_key = OPENAI_API_KEY
ocr_model = PaddleOCR(use_angle_cls=True, lang='ch')

# 连接 Milvus
connections.connect("default", host=MILVUS_HOST, port=MILVUS_PORT)

fields = [
    FieldSchema(name="slice_id", dtype=DataType.VARCHAR, is_primary=True, max_length=36),
    FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=1536),  # openai ada v2维度示例
    FieldSchema(name="doc_id", dtype=DataType.VARCHAR, max_length=255),
    FieldSchema(name="page_num", dtype=DataType.INT64),
    FieldSchema(name="slice_type", dtype=DataType.VARCHAR, max_length=20),
]

schema = CollectionSchema(fields, "pdf_slices", description="PDF切片向量库")
collection = Collection("pdf_slices", schema)
collection.create_index("embedding", {"index_type": "IVF_FLAT", "params": {"nlist": 128}}, "L2")
collection.load()

# -------- 功能函数 --------

def extract_pdf_content(pdf_path):
    doc = fitz.open(pdf_path)
    with pdfplumber.open(pdf_path) as plumber_pdf:
        pages_data = []
        for i, page in enumerate(doc):
            text = page.get_text("text")
            tables = plumber_pdf.pages[i].extract_tables()
            images = page.get_images(full=True)
            pages_data.append({
                "page_num": i + 1,
                "text": text,
                "tables": tables,
                "images": images,
                "page_obj": page
            })
    return pages_data

def expand_merged_cells(table):
    # 简单将 None 填充为上方非空内容（仅示例）
    for r, row in enumerate(table):
        for c, cell in enumerate(row):
            if cell is None and r > 0:
                table[r][c] = table[r-1][c]
    return table

def sliding_window(text, max_len=MAX_SLICE_LEN, overlap=SLICE_OVERLAP):
    step = int(max_len * (1 - overlap))
    slices = []
    start = 0
    while start < len(text):
        end = min(start + max_len, len(text))
        slices.append(text[start:end])
        if end == len(text):
            break
        start += step
    return slices

def ocr_image_from_page(page_obj, image_info):
    xref = image_info[0]
    base_image = page_obj.get_image_pixmap(xref)
    img_bytes = base_image.tobytes()
    # PaddleOCR 支持图片路径或 np.array，需要保存或转换
    # 这里简化，实际建议保存临时文件再ocr
    import tempfile
    from PIL import Image
    import io

    img = Image.open(io.BytesIO(img_bytes))
    with tempfile.NamedTemporaryFile(suffix=".png") as tmpf:
        img.save(tmpf.name)
        result = ocr_model.ocr(tmpf.name, cls=True)
    texts = [line[1][0] for line in result]
    return "\n".join(texts)

def get_openai_embedding(text):
    # 注意调整模型与维度
    response = openai.Embedding.create(
        input=text,
        model="text-embedding-ada-002"
    )
    return response['data'][0]['embedding']

def create_slice(doc_id, page_num, slice_type, content):
    return {
        "slice_id": str(uuid.uuid4()),
        "doc_id": doc_id,
        "page_num": page_num,
        "slice_type": slice_type,
        "content": content
    }

def insert_slices_to_milvus(slices):
    slice_ids = [s["slice_id"] for s in slices]
    embeddings = [s["embedding"] for s in slices]
    doc_ids = [s["doc_id"] for s in slices]
    page_nums = [s["page_num"] for s in slices]
    slice_types = [s["slice_type"] for s in slices]

    collection.insert([slice_ids, embeddings, doc_ids, page_nums, slice_types])

# -------- 主流程 --------

def process_single_pdf(pdf_path):
    doc_id = os.path.basename(pdf_path)
    pages_data = extract_pdf_content(pdf_path)
    all_slices = []

    for page_data in pages_data:
        page_num = page_data["page_num"]

        # 文本切片
        text_chunks = sliding_window(page_data["text"])
        for chunk in text_chunks:
            slice_obj = create_slice(doc_id, page_num, "text", chunk)
            all_slices.append(slice_obj)

        # 表格切片
        for table in page_data["tables"]:
            expanded_table = expand_merged_cells(table)
            # 转为字符串，示例用Markdown格式拼接
            md_table = "\n".join(["| " + " | ".join(map(str, row)) + " |" for row in expanded_table])
            slice_obj = create_slice(doc_id, page_num, "table", md_table)
            all_slices.append(slice_obj)

        # 图片OCR切片
        for img_info in page_data["images"]:
            try:
                ocr_text = ocr_image_from_page(page_data["page_obj"], img_info)
                if ocr_text.strip():
                    slice_obj = create_slice(doc_id, page_num, "image_ocr", ocr_text)
                    all_slices.append(slice_obj)
            except Exception as e:
                print(f"OCR failed for image on {doc_id} page {page_num}: {e}")

    # 批量生成embedding
    for s in all_slices:
        s["embedding"] = get_openai_embedding(s["content"])
        time.sleep(0.5)  # 防止API限速

    # 写入Milvus
    insert_slices_to_milvus(all_slices)
    print(f"Processed {doc_id}, slices: {len(all_slices)}")

def main():
    pdf_files = [os.path.join(PDF_DIR, f) for f in os.listdir(PDF_DIR) if f.endswith(".pdf")]
    for pdf_path in pdf_files:
        try:
            process_single_pdf(pdf_path)
        except Exception as e:
            print(f"Error processing {pdf_path}: {e}")

if __name__ == "__main__":
    main()
