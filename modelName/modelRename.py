import pandas as pd
import requests
import json
import time
from typing import Optional

class VehicleNameExtractor:
    def __init__(self, api_key: str, api_base: str, model: str):
        """
        初始化车型名称提取器
        
        Args:
            api_key: API密钥
            api_base: API基础URL
            model: 使用的模型名称
        """
        self.api_key = api_key
        self.api_base = api_base
        self.model = model
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def extract_vehicle_series(self, original_name: str) -> Optional[str]:
        """
        调用大模型提取车型系列名称
        
        Args:
            original_name: 原始海外型号名称
            
        Returns:
            提取后的系列名称
        """
        prompt = f"""
请从以下汽车型号名称中提取车型系列名称。

规则：
1. 去除品牌名称（如：Dacia、BMW、Mercedes等）
2. 去除子车型名称
3. 去除排量信息（如：1.0、2.0T、V6等）
4. 去除排挡信息（如：MT、AT、CVT等）
5. 去除驱动方式（如：4WD、AWD、FWD等）
6. 去除座位数（如：5座、7座等）
7. 去除燃料类型（如：汽油、柴油、混动等）
8. 去除年份信息（如：-24、2024等）
9. 仅保留该车型的核心系列名称

参考样例：
原始：Dacia Sandero 1.0 SCe 65ch Essential -24
提取：SCe 65ch Essential

请处理以下车型名称：
原始：{original_name}
提取：

注意：只提取车型系列名称，不要包含其他任何信息。
"""

        data = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.1,
            "max_tokens": 100
        }
        
        try:
            response = requests.post(
                self.api_base,
                headers=self.headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                extracted_name = result['choices'][0]['message']['content'].strip()
                
                # 清理结果，移除可能的前缀
                if '提取：' in extracted_name:
                    extracted_name = extracted_name.split('提取：')[-1].strip()
                
                return extracted_name
            else:
                print(f"API调用失败，状态码：{response.status_code}")
                print(f"错误信息：{response.text}")
                return None
                
        except Exception as e:
            print(f"调用API时发生错误：{e}")
            return None
    
    def process_excel_file(self, excel_path: str, original_column: str = "原始海外型号名称", 
                          output_column: str = "提取后车型名称"):
        """
        处理Excel文件中的车型名称数据
        
        Args:
            excel_path: Excel文件路径
            original_column: 原始数据列名
            output_column: 输出结果列名
        """
        try:
            # 读取Excel文件
            print(f"正在读取Excel文件：{excel_path}")
            df = pd.read_excel(excel_path)
            
            # 检查是否存在指定列
            if original_column not in df.columns:
                print(f"错误：Excel文件中未找到列 '{original_column}'")
                print(f"可用列名：{list(df.columns)}")
                return
            
            # 创建结果列
            if output_column not in df.columns:
                df[output_column] = ""
            
            # 获取需要处理的数据
            total_rows = len(df)
            print(f"共需处理 {total_rows} 行数据")
            
            # 逐行处理
            for index, row in df.iterrows():
                original_name = str(row[original_column])
                
                if pd.isna(row[original_column]) or original_name.strip() == "":
                    print(f"第 {index + 1} 行数据为空，跳过")
                    continue
                
                print(f"正在处理第 {index + 1}/{total_rows} 行：{original_name}")
                
                # 调用大模型提取
                extracted_name = self.extract_vehicle_series(original_name)
                
                if extracted_name:
                    df.at[index, output_column] = extracted_name
                    print(f"提取结果：{extracted_name}")
                else:
                    print("提取失败，保持原值")
                    df.at[index, output_column] = "提取失败"
                
                # 添加延时避免API限制
                time.sleep(1)
            
            # 保存结果
            output_path = excel_path.replace('.xlsx', '_处理结果.xlsx')
            df.to_excel(output_path, index=False)
            print(f"处理完成！结果已保存至：{output_path}")
            
            # 显示处理统计
            success_count = len(df[df[output_column] != "提取失败"])
            print(f"处理统计：成功 {success_count} 行，失败 {total_rows - success_count} 行")
            
        except Exception as e:
            print(f"处理Excel文件时发生错误：{e}")

def main():
    """主函数"""
    # 配置参数
    API_KEY = "sk-awbxpcoxjgcvswaupencngodsxuhbbuswjocamzcmgbkkelw"
    API_BASE = "https://api.siliconflow.cn/v1/chat/completions"
    MODEL = "deepseek-ai/DeepSeek-V3"
    EXCEL_PATH = r"C:\Users\<USER>\Desktop\车型名称提取.xlsx"
    
    # 创建提取器实例
    extractor = VehicleNameExtractor(API_KEY, API_BASE, MODEL)
    
    # 处理Excel文件
    extractor.process_excel_file(EXCEL_PATH)

if __name__ == "__main__":
    main()
