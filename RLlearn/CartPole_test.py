import gymnasium as gym
import numpy as np

def test_gymnasium():
    """Test that gymnasium works correctly with the new API"""
    print("🧪 Testing Gymnasium with CartPole-v1...")
    
    try:
        # Create environment without rendering
        env = gym.make('CartPole-v1')
        print("✅ Environment created successfully")
        
        # Reset environment
        observation, info = env.reset()
        print(f"✅ Environment reset successfully")
        print(f"   Initial observation shape: {observation.shape}")
        print(f"   Initial observation: {observation}")
        
        # Test a few steps
        total_reward = 0
        for step in range(10):
            action = env.action_space.sample()
            observation, reward, terminated, truncated, info = env.step(action)
            total_reward += reward
            
            print(f"   Step {step+1}: action={action}, reward={reward}, terminated={terminated}, truncated={truncated}")
            
            if terminated or truncated:
                print(f"   Episode ended at step {step+1}")
                observation, info = env.reset()
                break
        
        print(f"✅ Completed {step+1} steps successfully")
        print(f"   Total reward: {total_reward}")
        
        # Close environment
        env.close()
        print("✅ Environment closed successfully")
        
        print("\n🎉 All tests passed! Gymnasium is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False

def test_numpy_compatibility():
    """Test NumPy compatibility"""
    print("\n🔢 Testing NumPy compatibility...")
    
    try:
        print(f"   NumPy version: {np.__version__}")
        
        # Test basic operations
        arr = np.array([1, 2, 3, 4])
        print(f"   Array: {arr}")
        print(f"   Array dtype: {arr.dtype}")
        
        # Test boolean operations (this was the source of the original error)
        bool_arr = arr > 2
        print(f"   Boolean array: {bool_arr}")
        print(f"   Boolean dtype: {bool_arr.dtype}")
        
        print("✅ NumPy compatibility test passed")
        return True
        
    except Exception as e:
        print(f"❌ NumPy compatibility error: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🚀 Gymnasium & NumPy Compatibility Test")
    print("=" * 60)
    
    # Test NumPy first
    numpy_ok = test_numpy_compatibility()
    
    # Test Gymnasium
    gym_ok = test_gymnasium()
    
    print("\n" + "=" * 60)
    if numpy_ok and gym_ok:
        print("🎉 ALL TESTS PASSED! Your environment is ready to use.")
    else:
        print("❌ Some tests failed. Please check the error messages above.")
    print("=" * 60)
