import gymnasium as gym

# Create environment with proper render mode
env = gym.make('CartPole-v1', render_mode='human')
observation, info = env.reset()

for _ in range(1000):
    # Render the environment
    env.render()

    # Sample a random action
    action = env.action_space.sample()

    # Take the action and get the new state
    observation, reward, terminated, truncated, info = env.step(action)

    # Reset if episode is done
    if terminated or truncated:
        observation, info = env.reset()

env.close()