from neo4j import GraphDatabase


class VehicleKGQuery:
    def __init__(self, uri, user, password):
        # 初始化Neo4j数据库连接
        self.driver = GraphDatabase.driver(uri, auth=(user, password))

    def close(self):
        self.driver.close()

    def search_vehicle_id_by_name(self, vehicle_name):
        """根据车型名称模糊匹配并返回ID"""
        with self.driver.session() as session:
            result = session.execute_read(self._find_vehicle, vehicle_name)
            return result

    @staticmethod
    def _find_vehicle(tx, vehicle_name):
        # Cypher查询：匹配VehicleModel节点的名称（不区分大小写、模糊匹配）
        # query = (
        #     "MATCH (v:VehicleModel) "
        #     "WHERE toLower(v.name) CONTAINS toLower($name) "  # 模糊查询
        #     "RETURN v.vehicle_id AS id, v.name AS name "
        #     "LIMIT 10"  # 限制返回结果数量
        # )
        # 精确匹配优先 + 模糊兜底
        query = (
            "MATCH (v:VehicleModel) "
            "WITH v, CASE WHEN toLower(v.name) = toLower($name) THEN 1 ELSE 0 END AS exact_match "
            "WHERE toLower(v.name) CONTAINS toLower($name) "
            "   OR (v.alias_name IS NOT NULL AND any(alias IN v.alias_name WHERE toLower(alias) CONTAINS toLower($name))) "
            "RETURN v.vehicle_id AS id, v.name AS name, exact_match "
            "ORDER BY exact_match DESC "
            "LIMIT 10"
        )

        result = tx.run(query, name=vehicle_name)
        return [{"id": record["id"], "name": record["name"]} for record in result]


# 使用示例
if __name__ == "__main__":
    # 配置您的Neo4j数据库连接信息（需修改）
    NEO4J_URI = "neo4j://localhost"
    NEO4J_USER = "neo4j"
    NEO4J_PASSWORD = "ways.1234"

    kg_searcher = VehicleKGQuery(NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD)

    try:
        # 示例：搜索名称包含"宝马3系"的车型
        search_name = "SU7"
        vehicles = kg_searcher.search_vehicle_id_by_name(search_name)

        if vehicles:
            print(f"找到 {len(vehicles)} 条匹配车型：")
            for idx, vehicle in enumerate(vehicles, 1):
                print(f"{idx}. 车型名称：{vehicle['name']} → ID：{vehicle['id']}")
        else:
            print("未找到匹配车型")
    finally:
        kg_searcher.close()