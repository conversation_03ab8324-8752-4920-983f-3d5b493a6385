from py2neo import Graph, Node, Relationship
import pandas as pd

class Neo4jImporter:
    def __init__(self):
        self.graph = Graph("neo4j://localhost", auth=("neo4j", "ways.1234"))
        self.node_cache = {}  # 节点缓存防止重复创建

    def get_or_create_node(self, label, primary_key, **properties):
        # 生成唯一缓存键：标签 + 主键值
        cache_key = f"{label}_{properties[primary_key]}"
        if cache_key in self.node_cache:
            return self.node_cache[cache_key]

        node = Node(label, **properties)
        self.node_cache[cache_key] = node
        return node

    def process_excel(self, file_path):
        df = pd.read_excel(file_path)
        tx = self.graph.begin()

        for _, row in df.iterrows():
            # 处理品牌别名
            brand_aliases = row["品牌别称"]
            brand_alias_list = (
                [] if pd.isna(brand_aliases) or brand_aliases.strip() == ""
                else [alias.strip() for alias in brand_aliases.split(",")]
            )

            # 修改1：Brand节点按名称去重（假设name是唯一标识）
            brand = self.get_or_create_node("Brand", "name",  # 主键改为name
                name=row["品牌名称"],
                brand_id=row["品牌ID"],  # 保留ID字段但不作为主键
                brand_alias=brand_alias_list
            )

            # 修改2：其他节点统一使用get_or_create_node
            series = self.get_or_create_node("Series", "name",
                name=row["系别"],
                series_id=row["系别ID"]
            )

            manufacturer = self.get_or_create_node("Manufacturer", "name",
                name=row["厂商"],
                manufacturer_id=row["厂商ID"]
            )

            manufacturer_brand = self.get_or_create_node("ManufacturerBrand", "name",
                name=row["厂商品牌"],
                manufacturer_brand_id=row["厂商品牌ID"]
            )

            # 创建市场层级节点
            market_l1 = self.get_or_create_node("MarketSegment", "segment_id",
                                                segment_id=row["细分市场1级ID"],
                                                name=row["细分市场1级"],
                                                level=1)

            market_l2 = self.get_or_create_node("MarketSegment", "segment_id",
                                                segment_id=row["细分市场2级ID"],
                                                name=row["细分市场2级"],
                                                level=2)

            # 创建属性类节点
            market_prop = self.get_or_create_node("MarketProperty", "property_id",
                                                  property_id=row["市场属性ID"],
                                                  name=row["市场属性"])

            # 处理品牌属性别名
            # brand_prop_aliases = row["品牌属性别名"]
            # if pd.isna(brand_prop_aliases) or brand_prop_aliases.strip() == "":
            #     brand_prop_alias_list = []  # 如果为空，则设置为空列表
            # else:
            #     brand_prop_alias_list = [alias.strip() for alias in brand_prop_aliases.split(",")]  # 拆分并去除多余空格

            brand_prop = self.get_or_create_node("BrandProperty", "property_id",
                                                 property_id=row["品牌属性ID"],
                                                 name=row["品牌属性"])

            # 修改3：VehicleModel节点使用缓存
            vehicle = self.get_or_create_node("VehicleModel", "vehicle_id",
                vehicle_id=row["车型ID"],
                name=row["车型"],
                alias_name=[alias.strip() for alias in row["车型别名"].split(",")],
            )

            # 创建型号相关节点
            model_variant = self.get_or_create_node("ModelVariant", "model_id",
                                                    model_id=row["型号ID"],
                                                    model_code=row["型号编码"],
                                                    name=row["型号全称"],
                                                    short_name=row["型号简称"],
                                                    length=row["长"],
                                                    width=row["宽"],
                                                    height=row["高"],
                                                    wheelbase=row["轴距"],
                                                    life_cycle=row["变化描述"],
                                                    life_cycle_id=row["变化描述ID"],
                                                    seat_count=row["座位"],
                                                    model_year=row["年款"],
                                                    engine=row["发动机"],
                                                    engine_id=row["发动机ID"],
                                                    displacement=row["排量"],
                                                    displacement_id=row["排量ID"],
                                                    gear=row["排档"],
                                                    gear_id=row["排档ID"],
                                                    drive_type=row["驱动形式"],
                                                    drive_type_id=row["驱动形式ID"],
                                                    launch_date=row["上市日期"],
                                                    expiry_date=row["停产日期"],
                                                    stop_sale_date=row["停销日期"],
                                                    range=row["续航里程"]
                                                    )

            model_type = self.get_or_create_node("ModelType", "type_id",
                                                 type_id=row["汽车类型ID"],
                                                 name=row["汽车类型"]
                                                 )

            # 创建关系
            relationships = [
                Relationship(vehicle, "BELONGS_TO", brand),
                Relationship(brand, "BELONGS_TO", series),
                Relationship(vehicle, "PRODUCED_BY", manufacturer),
                Relationship(manufacturer, "OWNS_BRAND", manufacturer_brand),
                Relationship(brand, "HAS_MARKET_PROPERTY", market_prop),
                # Relationship(vehicle, "HAS_ORIGIN_PROPERTY", origin_prop),
                Relationship(brand, "HAS_BRAND_PROPERTY", brand_prop),
                Relationship(vehicle, "CLASSIFIED_L1", market_l1),
                Relationship(vehicle, "CLASSIFIED_L2", market_l2),
                Relationship(vehicle, "HAS_MODEL", model_variant),
                Relationship(model_variant, "MODEL_TYPE", model_type)
            ]

            # 处理燃料类型（两种燃料）
            for fuel_idx in [1, 2]:
                fuel_col = f"燃料类型-{fuel_idx}"
                if pd.notna(row[fuel_col]):
                    fuel_node = self.get_or_create_node("FuelType", "fuel_id",
                                                        fuel_id=row[f"{fuel_col}ID"],
                                                        name=row[fuel_col])
                    relationships.append(Relationship(vehicle, "USES_FUEL", fuel_node))

            # 处理车身形式
            body_style = self.get_or_create_node("BodyStyle", "style_id",
                                                 style_id=row["车身形式ID"],
                                                 name=row["车身形式"])
            relationships.append(Relationship(vehicle, "HAS_BODY_STYLE", body_style))

            # 批量提交
            tx.create(vehicle)
            for rel in relationships:
                tx.create(rel)

        self.graph.commit(tx)
        print(f"成功导入 {len(df)} 条记录")

if __name__ == "__main__":
    importer = Neo4jImporter()
    importer.process_excel(r"D:\06-AI 测试文件\型号20250312.xlsx")