#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新出行网站图片提取器
专门针对 https://www.xchuxing.com 网站优化
"""

import os
import sys
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import re
import json
from datetime import datetime

def print_with_time(msg):
    """带时间戳的打印"""
    print(f"[{datetime.now().strftime('%H:%M:%S')}] {msg}")

def get_xchuxing_html(url):
    """专门针对新出行网站的HTML获取"""
    print_with_time(f"🌐 访问新出行页面: {url}")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0',
        'Referer': 'https://www.xchuxing.com/'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=20)
        response.raise_for_status()
        
        # 新出行网站使用UTF-8编码
        response.encoding = 'utf-8'
        
        print_with_time(f"✅ 页面获取成功 (状态码: {response.status_code}, 大小: {len(response.text)} 字符)")
        return response.text
        
    except requests.RequestException as e:
        print_with_time(f"❌ 网络请求失败: {e}")
        raise
    except Exception as e:
        print_with_time(f"❌ 获取页面失败: {e}")
        raise

def extract_xchuxing_images(html, base_url):
    """从新出行页面提取图片链接"""
    print_with_time("🔍 开始分析页面结构...")
    
    soup = BeautifulSoup(html, 'html.parser')
    
    # 保存HTML用于调试
    debug_file = "debug_xchuxing.html"
    with open(debug_file, 'w', encoding='utf-8') as f:
        f.write(html)
    print_with_time(f"💾 页面内容已保存到 {debug_file}")
    
    # 获取页面基本信息
    title = soup.title.string.strip() if soup.title else "无标题"
    print_with_time(f"📄 页面标题: {title}")
    
    # 新出行网站的选择器策略
    selectors = [
        # 文章内容区域
        '.article-content',
        '.content-body', 
        '.detail-content',
        '.rich-content',
        '.post-content',
        '.main-content',
        # 通用容器
        '[class*="content"]',
        '[class*="article"]', 
        '[class*="detail"]',
        'article',
        'main',
        '.container',
        # 最后兜底
        'body'
    ]
    
    content_container = None
    for selector in selectors:
        container = soup.select_one(selector)
        if container:
            print_with_time(f"📦 找到内容容器: {selector}")
            content_container = container
            break
    
    if not content_container:
        print_with_time("⚠️  未找到特定容器，使用整个页面")
        content_container = soup
    
    # 查找所有图片
    img_tags = content_container.find_all('img')
    print_with_time(f"🖼️  找到 {len(img_tags)} 个img标签")
    
    image_urls = []
    
    for i, img in enumerate(img_tags):
        # 尝试多种src属性
        src_attrs = ['src', 'data-src', 'data-original', 'data-lazy', 'data-url', 'data-img']
        src = None
        
        for attr in src_attrs:
            if img.get(attr):
                src = img.get(attr).strip()
                break
        
        if not src:
            print_with_time(f"  图片 {i+1}: 无有效src属性")
            print_with_time(f"    所有属性: {dict(img.attrs)}")
            continue
        
        print_with_time(f"  图片 {i+1}: {src[:80]}...")
        
        # 过滤无效图片
        if (src.startswith('data:') or 
            len(src) < 10 or
            any(keyword in src.lower() for keyword in ['logo', 'avatar', 'icon', 'placeholder', 'blank']) or
            src.endswith('.svg')):
            print_with_time(f"    ⏭️ 跳过: 无效图片")
            continue
        
        # 补全绝对URL
        full_url = urljoin(base_url, src)
        image_urls.append(full_url)
        print_with_time(f"    ✅ 添加: {full_url}")
    
    # 如果没找到图片，尝试查找CSS背景图
    if not image_urls:
        print_with_time("🔍 尝试查找CSS背景图片...")
        
        # 查找style属性中的background-image
        elements_with_bg = content_container.find_all(attrs={"style": re.compile(r"background-image")})
        for elem in elements_with_bg:
            style = elem.get('style', '')
            matches = re.findall(r'background-image:\s*url\(["\']?([^"\'()]+)["\']?\)', style)
            for bg_url in matches:
                full_url = urljoin(base_url, bg_url)
                image_urls.append(full_url)
                print_with_time(f"    ✅ 找到背景图: {full_url}")
    
    # 如果还是没找到，查找可能的JSON数据
    if not image_urls:
        print_with_time("🔍 尝试查找页面中的JSON图片数据...")
        scripts = soup.find_all('script')
        for script in scripts:
            if script.string:
                # 查找可能包含图片URL的JSON
                text = script.string
                urls = re.findall(r'https?://[^\s"\'<>]+\.(?:jpg|jpeg|png|gif|webp|bmp)', text, re.I)
                for url in urls:
                    if url not in image_urls:
                        image_urls.append(url)
                        print_with_time(f"    ✅ 从JS中找到: {url}")
    
    print_with_time(f"📊 总共提取到 {len(image_urls)} 个图片链接")
    return image_urls

def download_images(image_urls, folder="xchuxing_images"):
    """下载图片到本地"""
    if not image_urls:
        print_with_time("❌ 没有图片可下载")
        return
    
    os.makedirs(folder, exist_ok=True)
    print_with_time(f"📁 创建下载文件夹: {folder}")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Referer': 'https://www.xchuxing.com/'
    }
    
    success_count = 0
    
    for i, url in enumerate(image_urls, 1):
        try:
            print_with_time(f"⬇️  下载第 {i}/{len(image_urls)} 张图片...")
            
            response = requests.get(url, headers=headers, timeout=15, stream=True)
            response.raise_for_status()
            
            # 确定文件扩展名
            content_type = response.headers.get('content-type', '').lower()
            if 'jpeg' in content_type or 'jpg' in content_type:
                ext = '.jpg'
            elif 'png' in content_type:
                ext = '.png' 
            elif 'gif' in content_type:
                ext = '.gif'
            elif 'webp' in content_type:
                ext = '.webp'
            else:
                # 从URL推断
                parsed_ext = os.path.splitext(urlparse(url).path)[1].lower()
                ext = parsed_ext if parsed_ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp'] else '.jpg'
            
            filename = f"xchuxing_{i:03d}{ext}"
            filepath = os.path.join(folder, filename)
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            file_size = os.path.getsize(filepath)
            print_with_time(f"    ✅ {filename} ({file_size:,} bytes)")
            success_count += 1
            
        except Exception as e:
            print_with_time(f"    ❌ 下载失败: {e}")
    
    print_with_time(f"🎉 下载完成! 成功: {success_count}/{len(image_urls)}")

def main():
    """主函数"""
    print_with_time("🚀 新出行图片提取器启动")
    
    if len(sys.argv) != 2:
        print("用法: python xchuxing_image_extractor.py <新出行文章URL>")
        print("示例: python xchuxing_image_extractor.py https://www.xchuxing.com/article/144526")
        sys.exit(1)
    
    url = sys.argv[1]
    
    # 验证URL
    if 'xchuxing.com' not in url:
        print_with_time("⚠️  警告: 此脚本专为新出行网站优化")
    
    try:
        # 获取页面内容
        html = get_xchuxing_html(url)
        
        # 提取图片链接
        image_urls = extract_xchuxing_images(html, url)
        
        if image_urls:
            # 下载图片
            download_images(image_urls)
        else:
            print_with_time("💡 提示: 该页面可能没有图片，或图片采用了特殊加载方式")
            print_with_time("    请检查生成的 debug_xchuxing.html 文件")
    
    except KeyboardInterrupt:
        print_with_time("\n❌ 用户取消操作")
    except Exception as e:
        print_with_time(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 