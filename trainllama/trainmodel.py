import torch
from datasets import load_dataset
from transformers import (
    AutoModelForCausalLM,
    AutoTokenizer,
    BitsAndBytesConfig,
    TrainingArguments,
)
from peft import LoraConfig, get_peft_model
from trl import SFTTrainer

# 1. 指定模型ID
model_name = "meta-llama/Meta-Llama-3-8B-Instruct"

# 2. 加载数据集
# 确保你的 my_dataset.jsonl 文件在同一个目录下
dataset = load_dataset("json", data_files="my_dataset.jsonl", split="train")

# 3. 配置 QLoRA (4-bit 量化)
bnb_config = BitsAndBytesConfig(
    load_in_4bit=True,
    bnb_4bit_quant_type="nf4",
    bnb_4bit_compute_dtype=torch.bfloat16, # 使用 bfloat16 计算，这是推荐的设置
    bnb_4bit_use_double_quant=True,
)

# 4. 配置 LoRA
lora_config = LoraConfig(
    r=16,  # LoRA 的秩，可以设为 8, 16, 32, 64。越大参数越多，但也可能过拟合
    lora_alpha=32, # LoRA 的 alpha 参数
    target_modules=["q_proj", "k_proj", "v_proj", "o_proj"], # 指定要应用 LoRA 的模块
    lora_dropout=0.05,
    bias="none",
    task_type="CAUSAL_LM",
)

# 5. 加载模型和 Tokenizer
tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
# Llama 3 没有 pad_token，我们需要设置一个
tokenizer.pad_token = tokenizer.eos_token
tokenizer.padding_side = "right" # 设置 padding 在右边，以避免问题

model = AutoModelForCausalLM.from_pretrained(
    model_name,
    quantization_config=bnb_config,
    torch_dtype=torch.bfloat16,
    device_map="auto", # 自动将模型分配到可用设备（如 GPU）
    trust_remote_code=True,
    # use_flash_attention_2=True # 如果你的硬件支持，可以开启 Flash Attention 2
)

# 6. 配置训练参数
training_arguments = TrainingArguments(
    output_dir="./llama3-8b-nl2json-finetuned", # 训练输出目录
    per_device_train_batch_size=2, # 根据你的显存调整
    gradient_accumulation_steps=4, # 梯度累积，模拟更大的 batch size
    optim="paged_adamw_32bit", # 使用 paged 优化器节省显存
    save_steps=100, # 每 100 步保存一次 checkpoint
    logging_steps=10, # 每 10 步记录一次日志
    learning_rate=2e-4, # 学习率
    num_train_epochs=1, # 训练轮次
    max_steps=-1, # 如果设置了，会覆盖 num_train_epochs
    fp16=False, # 如果你的GPU支持，bf16 效果更好
    bf16=True, # 开启 bf16 训练
    max_grad_norm=0.3,
    warmup_ratio=0.03,
    lr_scheduler_type="constant",
)

# 7. 创建并开始训练
trainer = SFTTrainer(
    model=model,
    train_dataset=dataset,
    peft_config=lora_config,
    dataset_text_field="text", # 指定数据集中包含文本的字段
    max_seq_length=1024, # 设置最大序列长度，根据你的数据调整
    tokenizer=tokenizer,
    args=training_arguments,
    packing=False, # 是否将多个短序列打包成一个长序列，这里设为 False
)

# 开始训练
trainer.train()

# 保存最终的模型
trainer.save_model("./llama3-8b-nl2json-finetuned/final_model")
print("训练完成！模型已保存。")