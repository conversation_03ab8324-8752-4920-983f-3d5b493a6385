import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
from peft import PeftModel

# 基础模型ID
base_model_name = "meta-llama/Meta-Llama-3-8B-Instruct"
# 你的 LoRA 适配器路径
adapter_path = "./llama3-8b-nl2json-finetuned/final_model"

# 加载基础模型的 tokenizer
tokenizer = AutoTokenizer.from_pretrained(base_model_name)

# 以 4-bit 加载基础模型
base_model = AutoModelForCausalLM.from_pretrained(
    base_model_name,
    load_in_4bit=True,
    torch_dtype=torch.bfloat16,
    device_map="auto"
)

# 将 LoRA 适配器合并到基础模型
model = PeftModel.from_pretrained(base_model, adapter_path)

# 准备推理
model.eval()


# 创建一个推理函数
def generate_json(instruction):
    prompt = f"""<|begin_of_text|><|start_header_id|>user<|end_header_id|>

{instruction}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
    inputs = tokenizer(prompt, return_tensors="pt").to("cuda")

    outputs = model.generate(
        **inputs,
        max_new_tokens=256,
        do_sample=False,  # 为了稳定的JSON输出，通常不使用采样
        eos_token_id=tokenizer.eos_token_id,
    )

    response_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
    # 提取 assistant 的回复
    json_part = response_text.split("<|start_header_id|>assistant<|end_header_id|>")[1].strip()

    print(f"指令: {instruction}")
    print(f"生成的JSON: {json_part}")
    return json_part


# --- 开始测试 ---
instruction1 = "我想买一部价格不超过3000元的华为手机"
generate_json(instruction1)

instruction2 = "搜索一下关于人工智能的最新研究论文"
generate_json(instruction2)