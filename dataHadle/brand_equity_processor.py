import pandas as pd
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN
import os

def process_brand_equity_data(excel_path):
    """
    处理品牌权益数据，按品牌维度重新组织数据
    
    Args:
        excel_path (str): Excel文件路径
    
    Returns:
        dict: 按品牌分组的权益数据
    """
    try:
        # 读取Excel数据
        df = pd.read_excel(excel_path)
        print(f"成功读取Excel文件: {excel_path}")
        print(f"数据形状: {df.shape}")
        
        # 品牌列
        brand_columns = [
            '一汽-大众-奥迪', '华晨宝马-宝马', '北京奔驰-奔驰', 
            '上汽通用-凯迪拉克', '沃尔沃亚太', 'smart'
        ]
        
        # 简化品牌名称映射
        brand_name_mapping = {
            '一汽-大众-奥迪': '奥迪',
            '华晨宝马-宝马': '宝马',
            '北京奔驰-奔驰': '奔驰',
            '上汽通用-凯迪拉克': '凯迪拉克',
            '沃尔沃亚太': '沃尔沃',
            'smart': 'smart'
        }
        
        # 按品牌重新组织数据
        brand_data = {}
        
        for brand_col in brand_columns:
            brand_name = brand_name_mapping[brand_col]
            
            # 筛选该品牌的权益数据（标记为√的行）
            brand_rows = df[df[brand_col] == '√'].copy()
            
            if not brand_rows.empty:
                # 选择需要显示的列
                display_columns = [
                    '权益类别', '权益子类', '权益细类', '权益项', '权益内容', 
                    '类型', '适用型号', '购车类型', '购车方式', '购车阶段',
                    '权益项最低参考价（元）', '权益项最高参考价（元）',
                    '生效开始时间', '生效结束时间', '生效月份'
                ]
                
                # 过滤存在的列
                available_columns = [col for col in display_columns if col in brand_rows.columns]
                brand_equity_data = brand_rows[available_columns].copy()
                
                # 清理数据，去除全为NaN的行
                brand_equity_data = brand_equity_data.dropna(how='all')
                
                brand_data[brand_name] = brand_equity_data
                
                print(f"{brand_name}: {len(brand_equity_data)} 条权益记录")
            else:
                print(f"{brand_name}: 无权益记录")
        
        return brand_data
        
    except Exception as e:
        print(f"处理数据时出错: {str(e)}")
        return {}

def create_brand_equity_ppt(brand_data, output_path):
    """
    创建品牌权益PPT报告
    
    Args:
        brand_data (dict): 按品牌分组的权益数据
        output_path (str): 输出PPT文件路径
    """
    try:
        # 创建PPT演示文稿
        prs = Presentation()
        
        # 添加标题页
        title_slide_layout = prs.slide_layouts[0]
        title_slide = prs.slides.add_slide(title_slide_layout)
        title = title_slide.shapes.title
        subtitle = title_slide.placeholders[1]
        
        title.text = "品牌权益对比报告"
        subtitle.text = "各品牌权益信息详细对比分析"
        
        # 为每个品牌创建幻灯片
        for brand_name, equity_df in brand_data.items():
            if equity_df.empty:
                continue
                
            # 创建品牌页面
            slide_layout = prs.slide_layouts[5]  # 空白布局
            slide = prs.slides.add_slide(slide_layout)
            
            # 添加标题
            title_box = slide.shapes.add_textbox(Inches(0.5), Inches(0.2), Inches(9), Inches(0.8))
            title_frame = title_box.text_frame
            title_frame.text = f"{brand_name} 权益信息"
            
            # 设置标题格式
            title_paragraph = title_frame.paragraphs[0]
            title_paragraph.alignment = PP_ALIGN.CENTER
            title_run = title_paragraph.runs[0]
            title_run.font.size = Pt(24)
            title_run.font.bold = True
            title_run.font.color.rgb = RGBColor(0, 51, 102)
            
            # 创建权益信息表格
            rows = min(len(equity_df) + 1, 15)  # 限制最多显示14行数据+1行标题
            cols = min(len(equity_df.columns), 8)  # 限制最多显示8列
            
            # 表格位置和大小
            left = Inches(0.3)
            top = Inches(1.2)
            width = Inches(9.4)
            height = Inches(5.5)
            
            # 添加表格
            table_shape = slide.shapes.add_table(rows, cols, left, top, width, height)
            table = table_shape.table
            
            # 设置表格列标题
            selected_columns = list(equity_df.columns)[:cols]
            for col_idx, col_name in enumerate(selected_columns):
                cell = table.cell(0, col_idx)
                cell.text = str(col_name)
                
                # 设置标题行格式
                cell.fill.solid()
                cell.fill.fore_color.rgb = RGBColor(0, 51, 102)
                
                for paragraph in cell.text_frame.paragraphs:
                    for run in paragraph.runs:
                        run.font.color.rgb = RGBColor(255, 255, 255)
                        run.font.bold = True
                        run.font.size = Pt(10)
            
            # 填充数据行
            for row_idx in range(min(len(equity_df), rows-1)):
                equity_row = equity_df.iloc[row_idx]
                for col_idx, col_name in enumerate(selected_columns):
                    cell = table.cell(row_idx + 1, col_idx)
                    cell_value = equity_row[col_name]
                    
                    # 处理NaN值和格式化
                    if pd.isna(cell_value):
                        cell.text = ""
                    else:
                        cell.text = str(cell_value)[:100]  # 限制文本长度
                    
                    # 设置数据行格式
                    for paragraph in cell.text_frame.paragraphs:
                        for run in paragraph.runs:
                            run.font.size = Pt(9)
                            
                    # 交替行颜色
                    if row_idx % 2 == 0:
                        cell.fill.solid()
                        cell.fill.fore_color.rgb = RGBColor(240, 248, 255)
        
        # 保存PPT
        prs.save(output_path)
        print(f"PPT报告已生成: {output_path}")
        
    except Exception as e:
        print(f"生成PPT时出错: {str(e)}")

def main():
    """
    主函数
    """
    # 文件路径
    excel_file = "对比数据.xlsx"
    output_ppt = "品牌权益对比报告.pptx"
    
    print("开始处理品牌权益数据...")
    
    # 处理数据
    brand_data = process_brand_equity_data(excel_file)
    
    if brand_data:
        print("\n数据处理完成，开始生成PPT...")
        
        # 生成PPT
        create_brand_equity_ppt(brand_data, output_ppt)
        
        print(f"\n处理完成！")
        print(f"已生成PPT文件: {output_ppt}")
        
        # 显示处理结果统计
        print("\n各品牌权益统计:")
        for brand, df in brand_data.items():
            print(f"- {brand}: {len(df)} 条权益记录")
    else:
        print("数据处理失败，无法生成PPT")

if __name__ == "__main__":
    main() 