import pandas as pd
import numpy as np
from PIL import Image

def analyze_excel_data():
    """
    分析Excel文件的数据结构
    """
    try:
        # 读取Excel文件
        df = pd.read_excel('对比数据.xlsx')
        
        print("="*50)
        print("Excel数据分析")
        print("="*50)
        
        print(f"数据形状: {df.shape}")
        print(f"\n列名列表:")
        for i, col in enumerate(df.columns):
            print(f"{i+1}. {col}")
        
        print("\n数据类型:")
        print(df.dtypes)
        
        print("\n前5行数据:")
        print(df.head().to_string())
        
        print("\n缺失值统计:")
        print(df.isnull().sum())
        
        # 查找品牌相关的列
        print("\n品牌相关列分析:")
        brand_columns = []
        for col in df.columns:
            if any(keyword in str(col).lower() for keyword in ['品牌', 'brand', '牌', '奥迪', '宝马', '奔驰', '凯迪拉克', '沃尔沃', 'smart']):
                brand_columns.append(col)
                print(f"发现品牌相关列: {col}")
                if df[col].dtype == 'object':
                    unique_vals = df[col].unique()[:10]  # 只显示前10个唯一值
                    print(f"  唯一值示例: {unique_vals}")
                    print(f"  唯一值数量: {df[col].nunique()}")
        
        # 如果没有明确的品牌列，检查所有列
        if not brand_columns:
            print("\n检查所有列的唯一值情况:")
            for col in df.columns:
                unique_count = df[col].nunique()
                if 2 <= unique_count <= 20:  # 可能是分类变量
                    print(f"{col}: {unique_count}个唯一值")
                    print(f"  值: {df[col].unique()}")
        
        return df
        
    except Exception as e:
        print(f"分析数据时出错: {str(e)}")
        return None

def view_reference_image():
    """
    查看参考图片的信息
    """
    try:
        img = Image.open("权益图片.png")
        print(f"\n权益图片信息:")
        print(f"图片尺寸: {img.size}")
        print(f"图片模式: {img.mode}")
        print("请手动查看权益图片.png文件以了解所需的输出格式")
        return img
    except Exception as e:
        print(f"读取图片时出错: {str(e)}")
        return None

if __name__ == "__main__":
    df = analyze_excel_data()
    view_reference_image() 