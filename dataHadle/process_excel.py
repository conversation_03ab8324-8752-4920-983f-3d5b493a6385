
import pandas as pd
import json

def process_excel_data(excel_path, output_format='csv'):
    """
    Reads an Excel file, processes data by '品牌' (Brand) dimension,
    and outputs it as CSV or JSON.

    Args:
        excel_path (str): The absolute path to the Excel file.
        output_format (str): The desired output format ('csv' or 'json').
                             Defaults to 'csv'.
    """
    try:
        df = pd.read_excel(excel_path, engine='openpyxl')
        print(f"Successfully read Excel file: {excel_path}")

        # Define the exact brand columns based on user input
        brand_columns = [
            '一汽-大众-奥迪',
            '华晨宝马-宝马',
            '北京奔驰-奔驰',
            '上汽通用-凯迪拉克',
            '沃尔沃亚太',
            'smart'
        ]

        # Identify identifier columns (all columns not in brand_columns)
        id_vars = [col for col in df.columns if col not in brand_columns]

        # Check if all expected brand columns exist in the DataFrame
        missing_brand_cols = [col for col in brand_columns if col not in df.columns]
        if missing_brand_cols:
            print(f"Error: The following brand columns were not found in the Excel file: {missing_brand_cols}")
            print("Please ensure the column names are exact and match the Excel file.")
            return

        # Melt the DataFrame to transform brand columns into rows
        df_melted = df.melt(id_vars=id_vars, value_vars=brand_columns, var_name='品牌', value_name='是否适用')

        # Filter out rows where '是否适用' is NaN (empty cells in brand columns)
        df_processed = df_melted.dropna(subset=['是否适用'])

        # You might want to remove the '是否适用' column if it's just a checkmark
        # df_processed = df_processed.drop(columns=['是否适用'])

        if output_format == 'csv':
            output_path = excel_path.replace('.xlsx', '_processed.csv')
            df_processed.to_csv(output_path, index=False, encoding='utf-8-sig')
            print(f"Processed data saved to: {output_path}")
        elif output_format == 'json':
            output_path = excel_path.replace('.xlsx', '_processed.json')
            # Group by brand for JSON output
            grouped_data = {}
            for brand in df_processed['品牌'].unique():
                grouped_data[brand] = df_processed[df_processed['品牌'] == brand].drop(columns=['品牌']).to_dict(orient='records')

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(grouped_data, f, ensure_ascii=False, indent=4)
            print(f"Processed data saved to: {output_path}")
        else:
            print("Error: Invalid output_format. Choose 'csv' or 'json'.")

    except FileNotFoundError:
        print(f"Error: Excel file not found at {excel_path}")
    except KeyError as e:
        print(f"Error: Missing expected column: {e}. Please check Excel column names.")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    excel_file = "D:\PycahrmProjects\LangChain\dataHadle\对比数据.xlsx"
    process_excel_data(excel_file, output_format='csv')
