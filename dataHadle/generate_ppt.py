import pandas as pd
from pptx import Presentation
from pptx.util import Inches

def generate_equity_ppt(csv_path, output_ppt_path):
    """
    Generates a PowerPoint presentation from processed equity data.
    Each brand gets a slide with a table of its equities.

    Args:
        csv_path (str): Path to the processed CSV file.
        output_ppt_path (str): Path where the output PPT will be saved.
    """
    try:
        df = pd.read_csv(csv_path)
        print(f"Successfully read processed CSV file: {csv_path}")

        prs = Presentation()

        # Define columns to include in the PPT table based on user-provided exact names
        table_columns = [
            '权益类别', '权益子类', '权益细类', '权益包', '互斥权益包', '互斥规则包',
            '权益包最低参考价（元）', '权益包最高参考价（元）', '权益项', '权益内容', '类型',
            '互斥权益', '互斥规则', '活动条件', '适用型号', '购车类型', '购车方式', '购车阶段',
            '权益项最低参考价（元）', '权益项最高参考价（元）', '生效开始时间', '生效结束时间', '生效月份'
        ]

        # Get unique brands
        brands = df['品牌'].unique()

        for brand in brands:
            slide_layout = prs.slide_layouts[1]  # Title and Content layout
            slide = prs.slides.add_slide(slide_layout)
            title = slide.shapes.title
            title.text = f"{brand} 权益信息"

            # Filter data for the current brand
            brand_df = df[df['品牌'] == brand].copy()

            # Select only the columns for the table
            # Ensure all table_columns exist in brand_df before selecting
            existing_table_columns = [col for col in table_columns if col in brand_df.columns]
            brand_df_for_table = brand_df[existing_table_columns]

            # Add a table to the slide
            rows = len(brand_df_for_table) + 1  # +1 for header row
            cols = len(existing_table_columns)

            # Define table position and size (adjust as needed)
            left = Inches(0.5)
            top = Inches(1.5)
            width = Inches(9)
            height = Inches(5)

            # Only add table if there are rows to display
            if rows > 1 and cols > 0:
                shape = slide.shapes.add_table(rows, cols, left, top, width, height)
                table = shape.table

                # Set column headers
                for col_idx, col_name in enumerate(existing_table_columns):
                    cell = table.cell(0, col_idx)
                    cell.text = col_name

                # Populate table with data
                for row_idx, (_, row_data) in enumerate(brand_df_for_table.iterrows()):
                    for col_idx, col_name in enumerate(existing_table_columns):
                        cell = table.cell(row_idx + 1, col_idx)
                        cell.text = str(row_data[col_name]) if pd.notna(row_data[col_name]) else ''
            else:
                # Add a text box if no data for the brand
                tf = slide.shapes.add_textbox(left, top, width, height).text_frame
                tf.text = "无可用权益信息"

        prs.save(output_ppt_path)
        print(f"PPT generated successfully: {output_ppt_path}")

    except FileNotFoundError:
        print(f"Error: CSV file not found at {csv_path}")
    except KeyError as e:
        print(f"Error: Missing expected column in CSV: {e}. Please check CSV column names.")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    processed_csv_file = "D:\PycahrmProjects\LangChain\dataHadle\对比数据_processed.csv"
    output_ppt_file = "D:\PycahrmProjects\LangChain\dataHadle\品牌权益报告.pptx"
    generate_equity_ppt(processed_csv_file, output_ppt_file)