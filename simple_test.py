import requests
from bs4 import BeautifulSoup
import os
from urllib.parse import urljoin, urlparse
import time

url = "https://www.xchuxing.com/article/144526"
print("🚀 开始提取文章正文区域图片")
print("📄 目标URL:", url)
print("-" * 50)

try:
    # 获取网页内容
    print("🌐 获取网页内容...")
    response = requests.get(url, timeout=30)
    print("✅ 状态码:", response.status_code)
    
    # 解析HTML
    soup = BeautifulSoup(response.text, 'html.parser')
    
    # 寻找文章正文区域
    print("🔍 寻找文章正文区域...")
    
    # 尝试多种正文选择器
    content_selectors = [
        '.article-content',
        '.detail-content', 
        '.post-content',
        '.content-body',
        '.main-content',
        'article',
        '[class*="content"]'
    ]
    
    article_content = None
    max_text_length = 0
    
    for selector in content_selectors:
        try:
            elements = soup.select(selector)
            for element in elements:
                text_length = len(element.get_text().strip())
                if text_length > max_text_length and text_length > 500:
                    max_text_length = text_length
                    article_content = element
                    print(f"✅ 找到正文区域: {selector} (文本长度: {text_length})")
        except:
            continue
    
    if not article_content:
        print("⚠️  未找到明确正文区域，使用整个页面")
        article_content = soup
    
    # 在正文区域查找图片
    img_tags = article_content.find_all('img')
    print(f"🖼️  正文区域找到 {len(img_tags)} 个图片标签")
    
    # 过滤图片
    print("🔍 过滤正文图片...")
    content_images = []
    
    skip_keywords = ['logo', 'icon', 'avatar', 'header', 'footer', 'nav', 'menu', 'ad', 'banner']
    
    for i, img in enumerate(img_tags):
        src = img.get('src') or img.get('data-src')
        if src:
            full_url = urljoin(url, src)
            
            # 检查是否包含跳过的关键词
            should_skip = False
            url_lower = full_url.lower()
            for keyword in skip_keywords:
                if keyword in url_lower:
                    print(f"⏭️  跳过 ({keyword}): {os.path.basename(full_url)}")
                    should_skip = True
                    break
            
            if should_skip or src.startswith('data:'):
                continue
            
            content_images.append(full_url)
            print(f"✅ 保留图片 {len(content_images)}: {os.path.basename(full_url)}")
    
    print(f"📊 过滤结果: {len(content_images)}/{len(img_tags)} 张图片保留")
    
    # 下载图片
    if content_images:
        save_dir = "article_images"
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
            print(f"📁 创建目录: {save_dir}")
        
        print(f"⬇️  开始下载 {len(content_images)} 张图片...")
        
        headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        
        success_count = 0
        for i, img_url in enumerate(content_images, 1):
            try:
                print(f"下载第 {i}/{len(content_images)} 张图片...")
                
                img_response = requests.get(img_url, headers=headers, timeout=30)
                img_response.raise_for_status()
                
                # 确定文件扩展名
                ext = '.jpg'
                content_type = img_response.headers.get('content-type', '').lower()
                if 'png' in content_type:
                    ext = '.png'
                elif 'webp' in content_type:
                    ext = '.webp'
                
                filename = f"article_img_{i:03d}{ext}"
                filepath = os.path.join(save_dir, filename)
                
                with open(filepath, 'wb') as f:
                    f.write(img_response.content)
                
                file_size = os.path.getsize(filepath)
                print(f"  ✅ 保存成功: {filename} ({file_size:,} bytes)")
                success_count += 1
                
                time.sleep(1)  # 延时1秒
                
            except Exception as e:
                print(f"  ❌ 下载失败: {e}")
        
        print("\n" + "=" * 50)
        print(f"🎉 下载完成!")
        print(f"📊 成功下载: {success_count}/{len(content_images)} 张图片")
        print(f"📁 保存位置: {os.path.abspath(save_dir)}")
    else:
        print("❌ 没有找到符合条件的正文图片")
    
except Exception as e:
    print("❌ 错误:", str(e))
    import traceback
    traceback.print_exc() 