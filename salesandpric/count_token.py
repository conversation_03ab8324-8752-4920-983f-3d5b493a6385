import tiktoken


def count_tokens(text):
    """
    计算文本内容的token数量
    使用 cl100k_base 编码器，这个是适用于最新Claude模型的编码器
    """
    # 初始化编码器
    encoding = tiktoken.get_encoding("cl100k_base")

    # 编码文本并计算token数
    tokens = encoding.encode(text)
    token_count = len(tokens)

    return token_count


# 读取markdown文件
with open('sub_model.md', 'r', encoding='utf-8') as file:
    content = file.read()

# 计算token数
total_tokens = count_tokens(content)
print(f"总token数: {total_tokens}")

# 计算字符数用于参考
char_count = len(content)
print(f"总字符数: {char_count}")