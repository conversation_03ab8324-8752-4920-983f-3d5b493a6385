#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Milvus向量数据库使用示例
演示如何搜索和查询已加载的品牌车型知识库
"""

import requests
from pymilvus import connections, Collection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BrandKnowledgeSearcher:
    def __init__(self):
        # 嵌入模型配置
        self.api_base = "https://api.siliconflow.cn/v1/embeddings"
        self.api_key = "sk-eqxovpsvjjcbrxelpiuxpvdwjfsaurjpfipfvtgnzlsphlty"
        self.model_name = "BAAI/bge-m3"
        
        # Milvus配置
        self.milvus_host = "localhost"
        self.milvus_port = "19530"
        self.collection_name = "brand_knowledge"
        
    def get_query_embedding(self, query_text: str):
        """
        获取查询文本的嵌入向量
        """
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.model_name,
            "input": [query_text]
        }
        
        response = requests.post(self.api_base, headers=headers, json=data)
        response.raise_for_status()
        
        result = response.json()
        return result["data"][0]["embedding"]
    
    def search_knowledge(self, query: str, top_k: int = 5):
        """
        在知识库中搜索相关内容
        """
        # 连接Milvus
        connections.connect("default", host=self.milvus_host, port=self.milvus_port)
        
        # 获取集合
        collection = Collection(self.collection_name)
        
        # 获取查询向量
        query_embedding = self.get_query_embedding(query)
        
        # 搜索参数
        search_params = {
            "metric_type": "IP",  # 使用内积距离
            "params": {"nprobe": 10}
        }
        
        # 执行搜索
        results = collection.search(
            data=[query_embedding],
            anns_field="embedding",
            param=search_params,
            limit=top_k,
            output_fields=["text"]
        )
        
        # 处理结果
        search_results = []
        for hits in results:
            for hit in hits:
                search_results.append({
                    "text": hit.entity.get("text"),
                    "score": hit.score,
                    "id": hit.id
                })
        
        return search_results

def main():
    """
    使用示例
    """
    searcher = BrandKnowledgeSearcher()
    
    # 示例查询
    queries = [
        "奥迪A6L的价格政策",
        "比亚迪新车型",
        "汽车金融措施"
    ]
    
    for query in queries:
        print(f"\n查询: {query}")
        print("-" * 50)
        
        try:
            results = searcher.search_knowledge(query, top_k=3)
            
            for i, result in enumerate(results, 1):
                print(f"{i}. 相似度: {result['score']:.4f}")
                print(f"   内容: {result['text'][:100]}...")
                print()
                
        except Exception as e:
            print(f"搜索失败: {e}")

if __name__ == "__main__":
    main()
