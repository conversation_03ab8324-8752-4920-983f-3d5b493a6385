#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
品牌车型知识库数据加载到Milvus向量数据库
读取本地文件，使用BAAI/bge-m3嵌入模型，存储到Milvus中
"""

import os
import requests
import json
from typing import List, Dict, Any
from pymilvus import (
    connections,
    Collection,
    CollectionSchema,
    FieldSchema,
    DataType,
    utility
)
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BrandKnowledgeLoader:
    def __init__(self):
        # 嵌入模型配置
        self.api_base = "https://api.siliconflow.cn/v1/embeddings"
        self.api_key = "sk-eqxovpsvjjcbrxelpiuxpvdwjfsaurjpfipfvtgnzlsphlty"
        self.model_name = "BAAI/bge-m3"
        
        # Milvus配置
        self.milvus_host = "localhost"
        self.milvus_port = "19530"
        self.collection_name = "brand_knowledge"
        self.dimension = 1024  # bge-m3模型的向量维度
        
        # 文件路径
        self.file_path = r"C:\Users\<USER>\Desktop\品牌车型知识库.txt"
        
    def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        调用嵌入模型API获取文本向量
        """
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.model_name,
            "input": texts
        }
        
        try:
            response = requests.post(self.api_base, headers=headers, json=data)
            response.raise_for_status()
            
            result = response.json()
            embeddings = [item["embedding"] for item in result["data"]]
            logger.info(f"成功获取 {len(embeddings)} 个文本的嵌入向量")
            return embeddings
            
        except requests.exceptions.RequestException as e:
            logger.error(f"调用嵌入模型API失败: {e}")
            raise
        except KeyError as e:
            logger.error(f"解析API响应失败: {e}")
            raise
    
    def connect_milvus(self):
        """
        连接到Milvus数据库
        """
        try:
            connections.connect("default", host=self.milvus_host, port=self.milvus_port)
            logger.info(f"成功连接到Milvus: {self.milvus_host}:{self.milvus_port}")
        except Exception as e:
            logger.error(f"连接Milvus失败: {e}")
            raise
    
    def create_collection(self):
        """
        创建Milvus集合
        """
        # 定义字段
        fields = [
            FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
            FieldSchema(name="text", dtype=DataType.VARCHAR, max_length=65535),
            FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=self.dimension)
        ]
        
        # 创建集合schema
        schema = CollectionSchema(fields, "品牌车型知识库向量集合")
        
        # 如果集合已存在，先删除
        if utility.has_collection(self.collection_name):
            utility.drop_collection(self.collection_name)
            logger.info(f"删除已存在的集合: {self.collection_name}")
        
        # 创建新集合
        collection = Collection(self.collection_name, schema)
        logger.info(f"成功创建集合: {self.collection_name}")
        
        return collection
    
    def create_index(self, collection: Collection):
        """
        为向量字段创建索引
        """
        index_params = {
            "metric_type": "COSINE",
            "index_type": "IVF_FLAT",
            "params": {"nlist": 128}
        }
        
        collection.create_index("embedding", index_params)
        logger.info("成功创建向量索引")
    
    def read_file(self) -> List[str]:
        """
        读取本地文件，按行分割
        """
        if not os.path.exists(self.file_path):
            logger.error(f"文件不存在: {self.file_path}")
            raise FileNotFoundError(f"文件不存在: {self.file_path}")
        
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 清理空行和去除首尾空白
            lines = [line.strip() for line in lines if line.strip()]
            logger.info(f"成功读取文件，共 {len(lines)} 行")
            return lines
            
        except Exception as e:
            logger.error(f"读取文件失败: {e}")
            raise
    
    def batch_process(self, texts: List[str], batch_size: int = 10) -> List[Dict[str, Any]]:
        """
        批量处理文本，获取嵌入向量
        """
        all_data = []
        
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            logger.info(f"处理批次 {i//batch_size + 1}/{(len(texts) + batch_size - 1)//batch_size}")
            
            # 获取嵌入向量
            embeddings = self.get_embeddings(batch_texts)
            
            # 组装数据
            for text, embedding in zip(batch_texts, embeddings):
                all_data.append({
                    "text": text,
                    "embedding": embedding
                })
        
        return all_data
    
    def insert_data(self, collection: Collection, data: List[Dict[str, Any]]):
        """
        将数据插入到Milvus集合中
        """
        texts = [item["text"] for item in data]
        embeddings = [item["embedding"] for item in data]
        
        entities = [texts, embeddings]
        
        insert_result = collection.insert(entities)
        logger.info(f"成功插入 {len(data)} 条数据")
        
        # 刷新数据到磁盘
        collection.flush()
        logger.info("数据已刷新到磁盘")
        
        return insert_result
    
    def load_knowledge_base(self):
        """
        主函数：加载知识库到Milvus
        """
        try:
            # 1. 连接Milvus
            self.connect_milvus()
            
            # 2. 创建集合
            collection = self.create_collection()
            
            # 3. 创建索引
            self.create_index(collection)
            
            # 4. 读取文件
            texts = self.read_file()
            
            # 5. 批量处理文本
            data = self.batch_process(texts, batch_size=10)
            
            # 6. 插入数据
            self.insert_data(collection, data)
            
            # 7. 加载集合到内存
            collection.load()
            logger.info("集合已加载到内存，可以进行搜索")
            
            logger.info("知识库加载完成！")
            
        except Exception as e:
            logger.error(f"加载知识库失败: {e}")
            raise

def main():
    """
    主函数
    """
    loader = BrandKnowledgeLoader()
    loader.load_knowledge_base()

if __name__ == "__main__":
    main()
