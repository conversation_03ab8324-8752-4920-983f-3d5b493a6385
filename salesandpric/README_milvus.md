# 品牌车型知识库 - Milvus向量数据库

本项目用于将品牌车型知识库文本数据加载到Milvus向量数据库中，并提供语义搜索功能。

## 文件说明

- `load_to_milvus.py` - 主要脚本，用于读取本地文件并加载到Milvus
- `milvus_usage_example.py` - 使用示例，演示如何搜索知识库
- `requirements_milvus.txt` - 项目依赖包
- `README_milvus.md` - 本说明文档

## 环境准备

### 1. 安装依赖

```bash
pip install -r requirements_milvus.txt
```

### 2. 安装和启动Milvus

#### 使用Docker启动Milvus (推荐)

```bash
# 下载Milvus Docker Compose文件
wget https://github.com/milvus-io/milvus/releases/download/v2.3.0/milvus-standalone-docker-compose.yml -O docker-compose.yml

# 启动Mil<PERSON>s
docker-compose up -d
```

#### 或者使用Milvus Lite (轻量版)

```bash
pip install milvus-lite
```

## 配置说明

### 嵌入模型配置
- **模型**: BAAI/bge-m3
- **API地址**: https://api.siliconflow.cn/v1/embeddings
- **API密钥**: sk-eqxovpsvjjcbrxelpiuxpvdwjfsaurjpfipfvtgnzlsphlty
- **向量维度**: 1024

### Milvus配置
- **主机**: localhost
- **端口**: 19530
- **集合名称**: brand_knowledge

### 文件路径
- **知识库文件**: C:\Users\<USER>\Desktop\品牌车型知识库.txt

## 使用方法

### 1. 加载知识库到Milvus

```bash
python load_to_milvus.py
```

这个脚本会：
1. 连接到Milvus数据库
2. 创建名为 `brand_knowledge` 的集合
3. 读取本地知识库文件
4. 逐行处理文本，调用嵌入模型获取向量
5. 将文本和向量存储到Milvus中
6. 创建向量索引以提高搜索性能

### 2. 搜索知识库

```bash
python milvus_usage_example.py
```

或者在代码中使用：

```python
from milvus_usage_example import BrandKnowledgeSearcher

searcher = BrandKnowledgeSearcher()
results = searcher.search_knowledge("奥迪A6L价格", top_k=5)

for result in results:
    print(f"相似度: {result['score']:.4f}")
    print(f"内容: {result['text']}")
```

## 主要功能

### BrandKnowledgeLoader 类
- `connect_milvus()` - 连接Milvus数据库
- `create_collection()` - 创建向量集合
- `create_index()` - 创建向量索引
- `read_file()` - 读取本地文件
- `get_embeddings()` - 调用嵌入模型API
- `batch_process()` - 批量处理文本
- `insert_data()` - 插入数据到Milvus
- `load_knowledge_base()` - 主加载流程

### BrandKnowledgeSearcher 类
- `get_query_embedding()` - 获取查询文本向量
- `search_knowledge()` - 语义搜索知识库

## 注意事项

1. **文件路径**: 请确保知识库文件路径正确
2. **Milvus服务**: 确保Milvus服务正在运行
3. **API密钥**: 确保嵌入模型API密钥有效
4. **网络连接**: 需要网络连接来调用嵌入模型API
5. **内存使用**: 大文件可能需要较多内存，可以调整batch_size参数

## 错误处理

脚本包含完整的错误处理和日志记录：
- 文件读取错误
- API调用错误
- Milvus连接错误
- 数据插入错误

## 性能优化

- 使用批量处理减少API调用次数
- 创建向量索引提高搜索速度
- 支持余弦相似度搜索
- 可配置的搜索参数

## 扩展功能

可以根据需要扩展以下功能：
- 支持多种文件格式 (PDF, Word, Excel等)
- 添加文本预处理和清洗
- 支持增量更新
- 添加元数据字段
- 实现更复杂的搜索逻辑
