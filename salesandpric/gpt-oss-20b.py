import os
import json
import requests
import pandas as pd
from datetime import datetime
def read_system_prompt():
    try:
        with open('info_tagging.md', 'r', encoding='utf-8') as file:
            return file.read()
    except FileNotFoundError:
        print("Error: info_tagging.md file not found")
        return None

def call_gpt_oss_20b(system_prompt, user_input):
    api_base_url = "https://yunwu.ai/v1/chat/completions"
    api_key = "sk-vRFFqMG3ba5FpvxXJ689ZWUP78SKn5UYlsz8ifVF47wo86Nf"

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }

    data = {
        "model": "gpt-oss-120b",
        "messages": [
            {"role": "system", "content": system_prompt + "\n请开启thinking模式，在回答前先进行思考分析。"},
            {"role": "user", "content": user_input}
        ]
    }

    try:
        print("\n思考中...\n")
        response = requests.post(api_base_url, headers=headers, json=data)
        response.raise_for_status()
        result = response.json()

        if result and "choices" in result:
            content = result["choices"][0]["message"]["content"]
            print(content)  # 打印完整的回答
            print("\n")  # 打印换行
            return result
        return None

    except requests.exceptions.RequestException as e:
        print(f"Error making API call: {e}")
        return None

def process_excel_questions(file_path, system_prompt):
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)


        if "问题" not in df.columns:
            print("错误：Excel文件中没有找到'问题'列")
            return

        # 如果没有"模型回答"列，添加一个

        # 如果没有"模型回答"列，添加一个

        # 添加处理时间列

        # 添加处理时间列
        if "处理时间" not in df.columns:
            df["处理时间"] = ""

            question = str(row["问题"])
            if pd.isna(question) or not question.strip():
                continue

            print(f"\n处理第 {index + 1} 个问题: {question}")

            # 调用API获取回答

                df.at[index, "模型回答"] = answer
                df.at[index, "处理时间"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                print(f"处理第 {index + 1} 个问题时出错")

        print("\n所有问题处理完成！")

    except Exception as e:
        print(f"处理Excel文件时出错: {e}")


    system_prompt = read_system_prompt()
    if not system_prompt:
        return

    excel_path = r"C:\Users\<USER>\Desktop\问题列表.xlsx"

                print(f"已保存第 {index + 1} 个问题的回答")
    if not os.path.exists(excel_path):
        print(f"错误：找不到Excel文件: {excel_path}")
                print(f"处理第 {index + 1} 个问题时出错")

    print(f"开始处理Excel文件: {excel_path}")


if __name__ == "__main__":
    main()
