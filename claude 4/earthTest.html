<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>太阳系模拟器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #000;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
            user-select: none;
        }

        .solar-system {
            position: relative;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            cursor: grab;
        }

        .solar-system:active {
            cursor: grabbing;
        }

        .stars {
            position: absolute;
            width: 200%;
            height: 200%;
            background: transparent;
            z-index: 1;
        }

        .star {
            position: absolute;
            background: white;
            border-radius: 50%;
            animation: twinkle 3s infinite;
        }

        @keyframes twinkle {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 1; }
        }

        .solar-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            transform-origin: center;
            z-index: 2;
        }

        .sun {
            position: absolute;
            width: 60px;
            height: 60px;
            background: radial-gradient(circle, #ffeb3b 0%, #ff9800 50%, #ff5722 100%);
            border-radius: 50%;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 0 30px #ffeb3b, 0 0 60px #ff9800, 0 0 90px #ff5722;
            animation: sunPulse 3s ease-in-out infinite;
            z-index: 10;
        }

        @keyframes sunPulse {
            0%, 100% { transform: translate(-50%, -50%) scale(1); }
            50% { transform: translate(-50%, -50%) scale(1.1); }
        }

        .orbit {
            position: absolute;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            transition: opacity 0.3s ease;
        }

        .orbit.hidden {
            opacity: 0;
        }

        .planet-container {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            animation-timing-function: linear;
            animation-iteration-count: infinite;
            animation-fill-mode: forwards;
        }

        .planet {
            position: absolute;
            border-radius: 50%;
            cursor: pointer;
            transition: transform 0.3s ease;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
        }

        .planet:hover {
            transform: translateY(-50%) scale(1.2);
        }

        .moon-container {
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            animation-timing-function: linear;
            animation-iteration-count: infinite;
            animation-fill-mode: forwards;
        }

        .moon {
            position: absolute;
            border-radius: 50%;
            background: #ccc;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
        }

        .asteroid-belt {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
        }

        .asteroid {
            position: absolute;
            background: #8b4513;
            border-radius: 50%;
            animation-timing-function: linear;
            animation-iteration-count: infinite;
            animation-fill-mode: forwards;
        }

        .controls {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            color: white;
        }

        .control-group {
            margin-bottom: 10px;
        }

        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
        }

        .control-group input, .control-group button {
            width: 100%;
            padding: 5px;
            border: none;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .control-group button {
            cursor: pointer;
            background: rgba(255, 255, 255, 0.3);
            transition: background 0.3s ease;
        }

        .control-group button:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .info-panel {
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            max-width: 300px;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .info-panel.show {
            opacity: 1;
            visibility: visible;
        }

        .info-panel h3 {
            margin-bottom: 10px;
            color: #ffeb3b;
        }

        .info-panel p {
            margin-bottom: 8px;
            font-size: 14px;
        }

        .speed-control {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            padding: 10px 20px;
            border-radius: 20px;
            color: white;
            z-index: 1000;
        }

        /* 行星特定样式 */
        .mercury { background: #8c7853; width: 8px; height: 8px; }
        .venus { background: #ffc649; width: 12px; height: 12px; }
        .earth { background: radial-gradient(circle, #6b93d6 0%, #4a69bd 50%, #2c3e50 100%); width: 14px; height: 14px; }
        .mars { background: #cd5c5c; width: 10px; height: 10px; }
        .jupiter { background: radial-gradient(circle, #d2691e 0%, #8b4513 50%, #654321 100%); width: 30px; height: 30px; }
        .saturn { background: radial-gradient(circle, #fad5a5 0%, #deb887 50%, #d2691e 100%); width: 26px; height: 26px; }
        .uranus { background: #4fd0e3; width: 18px; height: 18px; }
        .neptune { background: #4169e1; width: 16px; height: 16px; }

        /* 土星环 */
        .saturn::after {
            content: '';
            position: absolute;
            width: 40px;
            height: 40px;
            border: 2px solid rgba(218, 165, 32, 0.6);
            border-radius: 50%;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            pointer-events: none;
        }

        /* 动画关键帧 */
        @keyframes orbit {
            from { transform: translate(-50%, -50%) rotate(0deg); }
            to { transform: translate(-50%, -50%) rotate(360deg); }
        }

        /* 行星轨道动画类 */
        .mercury-orbit { animation: orbit 8s linear infinite; }
        .venus-orbit { animation: orbit 12s linear infinite; }
        .earth-orbit { animation: orbit 16s linear infinite; }
        .mars-orbit { animation: orbit 24s linear infinite; }
        .jupiter-orbit { animation: orbit 48s linear infinite; }
        .saturn-orbit { animation: orbit 72s linear infinite; }
        .uranus-orbit { animation: orbit 96s linear infinite; }
        .neptune-orbit { animation: orbit 120s linear infinite; }

        /* 卫星轨道动画类 */
        .moon-orbit { animation: orbit 2s linear infinite; }
        .jupiter-moon1 { animation: orbit 3s linear infinite; }
        .jupiter-moon2 { animation: orbit 4s linear infinite; }
        .saturn-moon { animation: orbit 2.5s linear infinite; }

        /* 小行星动画类 */
        .asteroid-orbit { animation: orbit 30s linear infinite; }
    </style>
</head>
<body>
    <div class="solar-system" id="solarSystem">
        <div class="stars" id="stars"></div>

        <div class="solar-container" id="solarContainer">
            <div class="sun"></div>

            <!-- 水星轨道 -->
            <div class="orbit" style="width: 120px; height: 120px;" data-planet="mercury"></div>
            <div class="planet-container mercury-orbit" style="width: 120px; height: 120px;" data-planet="mercury">
                <div class="planet mercury" data-info="mercury"></div>
            </div>

            <!-- 金星轨道 -->
            <div class="orbit" style="width: 160px; height: 160px;" data-planet="venus"></div>
            <div class="planet-container venus-orbit" style="width: 160px; height: 160px;" data-planet="venus">
                <div class="planet venus" data-info="venus"></div>
            </div>

            <!-- 地球轨道 -->
            <div class="orbit" style="width: 200px; height: 200px;" data-planet="earth"></div>
            <div class="planet-container earth-orbit" style="width: 200px; height: 200px;" data-planet="earth">
                <div class="planet earth" data-info="earth"></div>
                <div class="moon-container moon-orbit" style="width: 30px; height: 30px;">
                    <div class="moon" style="width: 4px; height: 4px;"></div>
                </div>
            </div>

            <!-- 火星轨道 -->
            <div class="orbit" style="width: 260px; height: 260px;" data-planet="mars"></div>
            <div class="planet-container mars-orbit" style="width: 260px; height: 260px;" data-planet="mars">
                <div class="planet mars" data-info="mars"></div>
            </div>

            <!-- 小行星带 -->
            <div class="asteroid-belt" id="asteroidBelt"></div>

            <!-- 木星轨道 -->
            <div class="orbit" style="width: 400px; height: 400px;" data-planet="jupiter"></div>
            <div class="planet-container jupiter-orbit" style="width: 400px; height: 400px;" data-planet="jupiter">
                <div class="planet jupiter" data-info="jupiter"></div>
                <!-- 木星的卫星 -->
                <div class="moon-container jupiter-moon1" style="width: 50px; height: 50px;">
                    <div class="moon" style="width: 3px; height: 3px; background: #ffeb3b;"></div>
                </div>
                <div class="moon-container jupiter-moon2" style="width: 60px; height: 60px;">
                    <div class="moon" style="width: 4px; height: 4px; background: #ff9800;"></div>
                </div>
            </div>

            <!-- 土星轨道 -->
            <div class="orbit" style="width: 500px; height: 500px;" data-planet="saturn"></div>
            <div class="planet-container saturn-orbit" style="width: 500px; height: 500px;" data-planet="saturn">
                <div class="planet saturn" data-info="saturn"></div>
                <!-- 土星的卫星 -->
                <div class="moon-container saturn-moon" style="width: 45px; height: 45px;">
                    <div class="moon" style="width: 5px; height: 5px; background: #ffd700;"></div>
                </div>
            </div>

            <!-- 天王星轨道 -->
            <div class="orbit" style="width: 600px; height: 600px;" data-planet="uranus"></div>
            <div class="planet-container uranus-orbit" style="width: 600px; height: 600px;" data-planet="uranus">
                <div class="planet uranus" data-info="uranus"></div>
            </div>

            <!-- 海王星轨道 -->
            <div class="orbit" style="width: 700px; height: 700px;" data-planet="neptune"></div>
            <div class="planet-container neptune-orbit" style="width: 700px; height: 700px;" data-planet="neptune">
                <div class="planet neptune" data-info="neptune"></div>
            </div>
        </div>
    </div>

    <div class="controls">
        <div class="control-group">
            <button onclick="toggleOrbits()">切换轨道显示</button>
        </div>
        <div class="control-group">
            <label>缩放:</label>
            <input type="range" id="zoomSlider" min="0.3" max="3" step="0.1" value="1" oninput="updateZoom(this.value)">
        </div>
        <div class="control-group">
            <button onclick="resetView()">重置视图</button>
        </div>
    </div>

    <div class="speed-control">
        <label>动画速度: </label>
        <input type="range" id="speedSlider" min="0.1" max="3" step="0.1" value="1" oninput="updateSpeed(this.value)">
        <span id="speedValue">1x</span>
    </div>

    <div class="info-panel" id="infoPanel">
        <h3 id="planetName"></h3>
        <p id="planetInfo"></p>
        <button onclick="closeInfo()" style="margin-top: 10px;">关闭</button>
    </div>

    <script>
        // 行星信息数据
        const planetInfo = {
            mercury: {
                name: "水星",
                info: "距太阳最近的行星，表面温度极高，白天可达430°C，夜晚降至-170°C。公转周期88天。"
            },
            venus: {
                name: "金星",
                info: "太阳系中最热的行星，表面温度约462°C。被厚厚的大气层包围，主要成分是二氧化碳。"
            },
            earth: {
                name: "地球",
                info: "我们的家园，太阳系中唯一已知存在生命的行星。71%的表面被海洋覆盖，拥有一颗天然卫星——月球。"
            },
            mars: {
                name: "火星",
                info: "红色星球，因表面含有氧化铁而呈现红色。拥有太阳系最大的火山——奥林匹斯山，高度约21公里。"
            },
            jupiter: {
                name: "木星",
                info: "太阳系最大的行星，质量是其他所有行星总和的2.5倍。拥有大红斑风暴和79颗已知卫星。"
            },
            saturn: {
                name: "土星",
                info: "以其美丽的光环系统而闻名，密度比水还小。拥有82颗已知卫星，其中泰坦是最大的一颗。"
            },
            uranus: {
                name: "天王星",
                info: "冰巨星，自转轴倾斜98度，几乎是横躺着公转。表面温度约-224°C，拥有微弱的环系统。"
            },
            neptune: {
                name: "海王星",
                info: "太阳系最远的行星，风速可达2100公里/小时。拥有14颗已知卫星，其中海卫一最大。"
            }
        };

        // 原始动画持续时间配置
        const originalDurations = {
            'mercury-orbit': 8,
            'venus-orbit': 12,
            'earth-orbit': 16,
            'mars-orbit': 24,
            'jupiter-orbit': 48,
            'saturn-orbit': 72,
            'uranus-orbit': 96,
            'neptune-orbit': 120,
            'moon-orbit': 2,
            'jupiter-moon1': 3,
            'jupiter-moon2': 4,
            'saturn-moon': 2.5,
            'asteroid-orbit': 30
        };

        let isDragging = false;
        let startX, startY;
        let currentX = 0, currentY = 0;
        let currentZoom = 1;
        let currentSpeed = 1;
        let orbitsVisible = true;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            createStars();
            createAsteroids();
            setupDragAndDrop();
            setupPlanetClicks();
        });

        // 创建星空背景
        function createStars() {
            const starsContainer = document.getElementById('stars');
            for (let i = 0; i < 200; i++) {
                const star = document.createElement('div');
                star.className = 'star';
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';
                star.style.width = Math.random() * 3 + 1 + 'px';
                star.style.height = star.style.width;
                star.style.animationDelay = Math.random() * 3 + 's';
                starsContainer.appendChild(star);
            }
        }

        // 创建小行星带
        function createAsteroids() {
            const asteroidBelt = document.getElementById('asteroidBelt');
            for (let i = 0; i < 30; i++) {
                const asteroid = document.createElement('div');
                asteroid.className = 'asteroid asteroid-orbit';

                const distance = 280 + Math.random() * 80;
                const angle = Math.random() * 360;
                const size = Math.random() * 3 + 1;
                const delay = Math.random() * 30;

                asteroid.style.width = size + 'px';
                asteroid.style.height = size + 'px';
                asteroid.style.left = distance * Math.cos(angle * Math.PI / 180) + 'px';
                asteroid.style.top = distance * Math.sin(angle * Math.PI / 180) + 'px';
                asteroid.style.animationDelay = delay + 's';

                asteroidBelt.appendChild(asteroid);
            }
        }

        // 设置拖拽功能
        function setupDragAndDrop() {
            const solarSystem = document.getElementById('solarSystem');

            solarSystem.addEventListener('mousedown', startDrag);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', endDrag);

            // 触摸事件支持
            solarSystem.addEventListener('touchstart', startDrag);
            document.addEventListener('touchmove', drag);
            document.addEventListener('touchend', endDrag);

            function startDrag(e) {
                isDragging = true;
                const clientX = e.clientX || e.touches[0].clientX;
                const clientY = e.clientY || e.touches[0].clientY;
                startX = clientX - currentX;
                startY = clientY - currentY;
                e.preventDefault();
            }

            function drag(e) {
                if (!isDragging) return;
                e.preventDefault();
                const clientX = e.clientX || e.touches[0].clientX;
                const clientY = e.clientY || e.touches[0].clientY;
                currentX = clientX - startX;
                currentY = clientY - startY;
                updateTransform();
            }

            function endDrag() {
                isDragging = false;
            }
        }

        // 设置行星点击事件
        function setupPlanetClicks() {
            const planets = document.querySelectorAll('[data-info]');
            planets.forEach(planet => {
                planet.addEventListener('click', function(e) {
                    e.stopPropagation();
                    showPlanetInfo(this.dataset.info);
                });
            });
        }

        // 显示行星信息
        function showPlanetInfo(planetKey) {
            const info = planetInfo[planetKey];
            if (info) {
                document.getElementById('planetName').textContent = info.name;
                document.getElementById('planetInfo').textContent = info.info;
                document.getElementById('infoPanel').classList.add('show');
            }
        }

        // 关闭信息面板
        function closeInfo() {
            document.getElementById('infoPanel').classList.remove('show');
        }

        // 切换轨道显示
        function toggleOrbits() {
            orbitsVisible = !orbitsVisible;
            const orbits = document.querySelectorAll('.orbit');
            orbits.forEach(orbit => {
                orbit.classList.toggle('hidden', !orbitsVisible);
            });
        }

        // 更新缩放
        function updateZoom(value) {
            currentZoom = parseFloat(value);
            updateTransform();
        }

        // 更新动画速度 - 修复版本
        function updateSpeed(value) {
            currentSpeed = parseFloat(value);
            document.getElementById('speedValue').textContent = currentSpeed.toFixed(1) + 'x';

            // 更新CSS动画持续时间
            updateAnimationSpeeds();
        }

        // 更新所有动画速度
        function updateAnimationSpeeds() {
            const style = document.getElementById('dynamicStyles') || document.createElement('style');
            style.id = 'dynamicStyles';

            let css = '';
            for (const [className, duration] of Object.entries(originalDurations)) {
                const newDuration = duration / currentSpeed;
                css += `.${className} { animation-duration: ${newDuration}s !important; }\n`;
            }

            style.textContent = css;
            if (!document.getElementById('dynamicStyles')) {
                document.head.appendChild(style);
            }
        }

        // 重置视图 - 修复版本
        function resetView() {
            currentX = 0;
            currentY = 0;
            currentZoom = 1;
            currentSpeed = 1;

            // 重置控件
            document.getElementById('zoomSlider').value = 1;
            document.getElementById('speedSlider').value = 1;
            document.getElementById('speedValue').textContent = '1x';

            // 重置动画速度
            updateAnimationSpeeds();

            // 更新变换
            updateTransform();

            // 关闭信息面板
            closeInfo();
        }

        // 更新变换
        function updateTransform() {
            const solarContainer = document.getElementById('solarContainer');
            solarContainer.style.transform = `translate(calc(-50% + ${currentX}px), calc(-50% + ${currentY}px)) scale(${currentZoom})`;
        }

        // 鼠标滚轮缩放
        document.addEventListener('wheel', function(e) {
            e.preventDefault();
            const zoomDelta = e.deltaY > 0 ? -0.1 : 0.1;
            const newZoom = Math.max(0.3, Math.min(3, currentZoom + zoomDelta));
            currentZoom = newZoom;
            document.getElementById('zoomSlider').value = newZoom;
            updateTransform();
        });

        // 防止右键菜单
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });

        // 防止页面滚动
        document.addEventListener('touchmove', function(e) {
            if (isDragging) {
                e.preventDefault();
            }
        }, { passive: false });

        // 点击空白处关闭信息面板
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.info-panel') && !e.target.closest('[data-info]')) {
                closeInfo();
            }
        });
    </script>
</body>
</html>