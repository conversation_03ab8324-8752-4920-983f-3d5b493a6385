<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Xiaomi SU7 Sales & Price Analysis Report</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #ff6700 0%, #ff1e00 100%);
        }
        .chart-container {
            height: 300px;
            position: relative;
        }
        .price-tag {
            position: absolute;
            right: 10px;
            top: 10px;
            background: rgba(255,255,255,0.9);
            padding: 5px 10px;
            border-radius: 20px;
            font-weight: bold;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .stats-card:hover {
            transform: translateY(-5px);
            transition: all 0.3s ease;
        }
        @media (max-width: 640px) {
            .hero-title {
                font-size: 1.8rem;
            }
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Header -->
    <header class="gradient-bg text-white shadow-lg">
        <div class="container mx-auto px-4 py-6">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-2">
                    <i class="fas fa-car text-2xl"></i>
                    <h1 class="text-2xl font-bold">Xiaomi Auto Insights</h1>
                </div>
                <div class="hidden md:flex space-x-4">
                    <a href="#" class="hover:underline">Home</a>
                    <a href="#" class="hover:underline">Reports</a>
                    <a href="#" class="hover:underline">Analysis</a>
                    <a href="#" class="hover:underline">Contact</a>
                </div>
                <button class="md:hidden text-xl">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="gradient-bg text-white py-12">
        <div class="container mx-auto px-4 text-center">
            <h1 class="hero-title text-4xl font-bold mb-4">Xiaomi SU7 Sales & Price Analysis</h1>
            <p class="text-xl max-w-3xl mx-auto">Comprehensive market performance report of Xiaomi's first electric vehicle as of Q2 2024</p>
        </div>
    </section>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
            <div class="stats-card bg-white rounded-xl shadow-md p-6 hover:shadow-lg cursor-pointer">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500">Total Sales</p>
                        <h3 class="text-3xl font-bold text-orange-500">58,763</h3>
                        <p class="text-green-500 mt-2"><i class="fas fa-arrow-up mr-1"></i> 12.5% MoM</p>
                    </div>
                    <div class="bg-orange-100 p-3 rounded-full">
                        <i class="fas fa-chart-line text-orange-500 text-2xl"></i>
                    </div>
                </div>
            </div>

            <div class="stats-card bg-white rounded-xl shadow-md p-6 hover:shadow-lg cursor-pointer">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500">Average Price</p>
                        <h3 class="text-3xl font-bold text-blue-500">¥215,900</h3>
                        <p class="text-gray-500 mt-2">Base model starts at ¥199,900</p>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fas fa-tag text-blue-500 text-2xl"></i>
                    </div>
                </div>
            </div>

            <div class="stats-card bg-white rounded-xl shadow-md p-6 hover:shadow-lg cursor-pointer">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500">Market Share</p>
                        <h3 class="text-3xl font-bold text-purple-500">4.2%</h3>
                        <p class="text-green-500 mt-2"><i class="fas fa-arrow-up mr-1"></i> 2.1% since launch</p>
                    </div>
                    <div class="bg-purple-100 p-3 rounded-full">
                        <i class="fas fa-pie-chart text-purple-500 text-2xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sales Analysis Section -->
        <section class="bg-white rounded-xl shadow-md p-6 mb-12">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-800">Monthly Sales Performance</h2>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 bg-gray-100 rounded-md text-sm">2024</button>
                    <button class="px-3 py-1 bg-orange-500 text-white rounded-md text-sm">Q2</button>
                </div>
            </div>

            <div class="chart-container mb-6">
                <!-- Sales Chart (simulated with CSS) -->
                <div class="relative h-full">
                    <div class="absolute bottom-0 left-0 right-0 flex justify-between px-4">
                        <!-- Sales bars -->
                        <div class="flex flex-col items-center" style="width: 14%">
                            <div class="bg-orange-400 w-full rounded-t-sm" style="height: 30%;"></div>
                            <p class="text-xs mt-1">Jan</p>
                        </div>
                        <div class="flex flex-col items-center" style="width: 14%">
                            <div class="bg-orange-400 w-full rounded-t-sm" style="height: 45%;"></div>
                            <p class="text-xs mt-1">Feb</p>
                        </div>
                        <div class="flex flex-col items-center" style="width: 14%">
                            <div class="bg-orange-500 w-full rounded-t-sm" style="height: 70%;"></div>
                            <p class="text-xs mt-1">Mar</p>
                        </div>
                        <div class="flex flex-col items-center" style="width: 14%">
                            <div class="bg-orange-500 w-full rounded-t-sm" style="height: 85%;"></div>
                            <p class="text-xs mt-1">Apr</p>
                        </div>
                        <div class="flex flex-col items-center" style="width: 14%">
                            <div class="bg-orange-600 w-full rounded-t-sm" style="height: 100%;"></div>
                            <p class="text-xs mt-1">May</p>
                        </div>
                        <div class="flex flex-col items-center" style="width: 14%">
                            <div class="bg-orange-600 w-full rounded-t-sm" style="height: 95%;"></div>
                            <p class="text-xs mt-1">Jun</p>
                        </div>
                        <div class="flex flex-col items-center" style="width: 14%">
                            <div class="bg-orange-700 w-full rounded-t-sm" style="height: 80%;"></div>
                            <p class="text-xs mt-1">Jul</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-semibold text-lg mb-2">Key Observations</h3>
                    <ul class="list-disc pl-5 space-y-2 text-gray-700">
                        <li>Sales peaked in May with 12,489 units sold</li>
                        <li>Consistent month-over-month growth averaging 15%</li>
                        <li>June saw slight dip due to seasonal factors</li>
                        <li>Projected to cross 100,000 units by year-end</li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-semibold text-lg mb-2">Regional Breakdown</h3>
                    <div class="flex flex-wrap gap-2">
                        <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">Beijing 32%</span>
                        <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">Shanghai 28%</span>
                        <span class="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm">Guangdong 18%</span>
                        <span class="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">Zhejiang 12%</span>
                        <span class="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm">Other 10%</span>
                    </div>
                    <p class="mt-3 text-sm text-gray-600">Top-tier cities account for 78% of total sales, indicating strong urban appeal.</p>
                </div>
            </div>
        </section>

        <!-- Price Analysis Section -->
        <section class="bg-white rounded-xl shadow-md p-6 mb-12">
            <h2 class="text-2xl font-bold text-gray-800 mb-6">Price Structure Analysis</h2>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                <!-- Base Model -->
                <div class="border rounded-lg p-4 relative">
                    <span class="price-tag">¥199,900</span>
                    <h3 class="font-bold text-lg mb-2">SU7 Standard</h3>
                    <ul class="text-sm text-gray-600 space-y-1 mb-4">
                        <li><i class="fas fa-battery-quarter text-blue-500 mr-1"></i> 400km CLTC range</li>
                        <li><i class="fas fa-tachometer-alt text-blue-500 mr-1"></i> 0-100km/h in 5.3s</li>
                        <li><i class="fas fa-microchip text-blue-500 mr-1"></i> Basic ADAS features</li>
                    </ul>
                    <div class="bg-gray-100 p-2 rounded text-center">
                        <p class="text-xs font-semibold">Accounts for 45% of sales</p>
                    </div>
                </div>

                <!-- Mid Model -->
                <div class="border rounded-lg p-4 relative border-blue-300 bg-blue-50">
                    <span class="price-tag bg-blue-100 text-blue-800">¥229,900</span>
                    <h3 class="font-bold text-lg mb-2">SU7 Pro</h3>
                    <ul class="text-sm text-gray-600 space-y-1 mb-4">
                        <li><i class="fas fa-battery-half text-blue-500 mr-1"></i> 600km CLTC range</li>
                        <li><i class="fas fa-tachometer-alt text-blue-500 mr-1"></i> 0-100km/h in 3.9s</li>
                        <li><i class="fas fa-microchip text-blue-500 mr-1"></i> Enhanced ADAS package</li>
                    </ul>
                    <div class="bg-blue-100 p-2 rounded text-center">
                        <p class="text-xs font-semibold text-blue-800">Most popular (50% of sales)</p>
                    </div>
                </div>

                <!-- Top Model -->
                <div class="border rounded-lg p-4 relative border-purple-300 bg-purple-50">
                    <span class="price-tag bg-purple-100 text-purple-800">¥299,900</span>
                    <h3 class="font-bold text-lg mb-2">SU7 Max</h3>
                    <ul class="text-sm text-gray-600 space-y-1 mb-4">
                        <li><i class="fas fa-battery-full text-blue-500 mr-1"></i> 800km CLTC range</li>
                        <li><i class="fas fa-tachometer-alt text-blue-500 mr-1"></i> 0-100km/h in 2.8s</li>
                        <li><i class="fas fa-microchip text-blue-500 mr-1"></i> Full autonomous driving</li>
                    </ul>
                    <div class="bg-purple-100 p-2 rounded text-center">
                        <p class="text-xs font-semibold text-purple-800">Premium segment (5% of sales)</p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="font-semibold mb-3">Price Positioning vs Competitors</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full bg-white">
                        <thead>
                            <tr class="bg-gray-100">
                                <th class="py-2 px-4 text-left">Model</th>
                                <th class="py-2 px-4 text-right">Price (¥)</th>
                                <th class="py-2 px-4 text-right">Range (km)</th>
                                <th class="py-2 px-4 text-right">0-100km/h</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="border-b">
                                <td class="py-3 px-4 font-medium">Xiaomi SU7 Pro</td>
                                <td class="py-3 px-4 text-right">229,900</td>
                                <td class="py-3 px-4 text-right">600</td>
                                <td class="py-3 px-4 text-right">3.9s</td>
                            </tr>
                            <tr class="border-b bg-blue-50">
                                <td class="py-3 px-4 font-medium">Tesla Model 3 RWD</td>
                                <td class="py-3 px-4 text-right">245,900</td>
                                <td class="py-3 px-4 text-right">513</td>
                                <td class="py-3 px-4 text-right">6.1s</td>
                            </tr>
                            <tr class="border-b">
                                <td class="py-3 px-4 font-medium">BYD Seal Premium</td>
                                <td class="py-3 px-4 text-right">219,800</td>
                                <td class="py-3 px-4 text-right">550</td>
                                <td class="py-3 px-4 text-right">3.8s</td>
                            </tr>
                            <tr class="border-b bg-blue-50">
                                <td class="py-3 px-4 font-medium">NIO ET5</td>
                                <td class="py-3 px-4 text-right">328,000</td>
                                <td class="py-3 px-4 text-right">560</td>
                                <td class="py-3 px-4 text-right">4.0s</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <p class="mt-3 text-sm text-gray-600">Xiaomi SU7 offers competitive pricing with superior specifications in its segment.</p>
            </div>
        </section>

        <!-- Market Outlook -->
        <section class="bg-white rounded-xl shadow-md p-6">
            <h2 class="text-2xl font-bold text-gray-800 mb-6">Market Outlook & Projections</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-semibold text-lg mb-3">2024 Forecast</h3>
                    <div class="bg-gradient-to-r from-orange-100 to-orange-50 p-4 rounded-lg">
                        <div class="flex justify-between mb-2">
                            <span>Q1 Actual</span>
                            <span class="font-medium">14,892 units</span>
                        </div>
                        <div class="flex justify-between mb-2">
                            <span>Q2 Projection</span>
                            <span class="font-medium">36,500 units</span>
                        </div>
                        <div class="flex justify-between mb-2">
                            <span>H2 Forecast</span>
                            <span class="font-medium">68,000 units</span>
                        </div>
                        <div class="border-t pt-2 mt-2">
                            <div class="flex justify-between font-bold">
                                <span>2024 Total</span>
                                <span class="text-orange-600">~119,392 units</span>
                            </div>
                        </div>
                    </div>
                    <p class="mt-3 text-sm text-gray-600">Projections based on current growth trajectory and planned production capacity expansion.</p>
                </div>

                <div>
                    <h3 class="font-semibold text-lg mb-3">Strategic Recommendations</h3>
                    <div class="space-y-3">
                        <div class="flex items-start">
                            <div class="bg-green-100 p-2 rounded-full mr-3">
                                <i class="fas fa-plus text-green-600"></i>
                            </div>
                            <p class="text-sm">Expand production capacity to meet growing demand, especially for Pro variant</p>
                        </div>
                        <div class="flex items-start">
                            <div class="bg-blue-100 p-2 rounded-full mr-3">
                                <i class="fas fa-map-marker-alt text-blue-600"></i>
                            </div>
                            <p class="text-sm">Increase dealership presence in lower-tier cities to expand market reach</p>
                        </div>
                        <div class="flex items-start">
                            <div class="bg-purple-100 p-2 rounded-full mr-3">
                                <i class="fas fa-tag text-purple-600"></i>
                            </div>
                            <p class="text-sm">Introduce limited-time financing options to boost Standard model sales</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8 mt-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">Xiaomi Auto Insights</h3>
                    <p class="text-gray-400 text-sm">Providing comprehensive analysis on Xiaomi's automotive ventures and market performance.</p>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Reports</h3>
                    <ul class="space-y-2 text-gray-400 text-sm">
                        <li><a href="#" class="hover:text-white">Monthly Sales</a></li>
                        <li><a href="#" class="hover:text-white">Market Analysis</a></li>
                        <li><a href="#" class="hover:text-white">Competitor Benchmarks</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Resources</h3>
                    <ul class="space-y-2 text-gray-400 text-sm">
                        <li><a href="#" class="hover:text-white">Industry Reports</a></li>
                        <li><a href="#" class="hover:text-white">EV Market Trends</a></li>
                        <li><a href="#" class="hover:text-white">Consumer Surveys</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Subscribe</h3>
                    <p class="text-gray-400 text-sm mb-3">Get the latest reports delivered to your inbox</p>
                    <div class="flex">
                        <input type="email" placeholder="Your email" class="px-3 py-2 text-gray-800 rounded-l w-full">
                        <button class="bg-orange-500 px-4 rounded-r"><i class="fas fa-paper-plane"></i></button>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-6 text-center text-gray-400 text-sm">
                <p>© 2024 Xiaomi Auto Insights. All data is simulated for demonstration purposes.</p>
            </div>
        </div>
    </footer>

    <script>
        // Simple interactive elements
        document.addEventListener('DOMContentLoaded', function() {
            // Toggle mobile menu (would be fully functional with more JS)
            const mobileMenuButton = document.querySelector('.md:hidden');
            mobileMenuButton.addEventListener('click', function() {
                alert('Mobile menu would open here in a full implementation');
            });

            // Add animation to stats cards on scroll
            const observerOptions = {
                threshold: 0.1
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-fadeIn');
                    }
                });
            }, observerOptions);

            document.querySelectorAll('.stats-card').forEach(card => {
                observer.observe(card);
            });
        });
    </script>
</body>
</html>