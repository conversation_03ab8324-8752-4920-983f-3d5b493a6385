<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D City Scene</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            overflow: hidden;
            font-family: Arial, sans-serif;
        }

        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            background: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 5px;
            z-index: 100;
            display: none;
        }

        #controls {
            position: absolute;
            bottom: 10px;
            left: 10px;
            color: white;
            background: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 5px;
            z-index: 100;
            display: none;
        }

        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
            z-index: 200;
        }
    </style>
</head>
<body>
    <div id="loading">Loading 3D City...</div>

    <div id="info">
        <h3>3D City Scene</h3>
        <p>Time: <span id="timeDisplay">12:00</span></p>
        <p>Speed: <span id="speedDisplay">1x</span></p>
    </div>

    <div id="controls">
        <p>Controls:</p>
        <p>WASD - Move camera</p>
        <p>Mouse - Look around</p>
        <p>Space - Toggle time speed</p>
        <p>R - Reset camera</p>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r144/three.min.js"></script>
    <script>
        // 等待Three.js加载完成
        function initCity() {
            if (typeof THREE === 'undefined') {
                console.log("Three.js not loaded yet, retrying...");
                setTimeout(initCity, 100);
                return;
            }

            console.log("Three.js loaded, starting city scene...");

            // 场景基本设置
            const scene = new THREE.Scene();
            const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            const renderer = new THREE.WebGLRenderer({ antialias: true });

            // 渲染器配置
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            renderer.setClearColor(0x87CEEB);
            document.body.appendChild(renderer.domElement);

            // 相机位置
            camera.position.set(0, 15, 25);
            camera.lookAt(0, 0, 0);

            // 时间和控制变量
            const clock = new THREE.Clock();
            let time = 12;
            let timeSpeed = 1;
            let speedIndex = 1;
            const speedOptions = [0.1, 1, 5, 10];

            const cars = [];
            const pedestrians = [];
            const trafficLights = [];

            const keys = {};
            let mouseX = 0;
            let mouseY = 0;

            // 创建光照
            function setupLighting() {
                // 环境光
                const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
                scene.add(ambientLight);

                // 主光源
                const sunLight = new THREE.DirectionalLight(0xffffff, 1);
                sunLight.position.set(50, 50, 50);
                sunLight.castShadow = true;
                sunLight.shadow.mapSize.width = 1024;
                sunLight.shadow.mapSize.height = 1024;
                sunLight.shadow.camera.near = 0.5;
                sunLight.shadow.camera.far = 200;
                sunLight.shadow.camera.left = -50;
                sunLight.shadow.camera.right = 50;
                sunLight.shadow.camera.top = 50;
                sunLight.shadow.camera.bottom = -50;
                scene.add(sunLight);

                // 存储光源引用以便后续更新
                scene.userData.ambientLight = ambientLight;
                scene.userData.sunLight = sunLight;

                console.log("Lighting setup complete");
            }

            // 创建地面
            function createGround() {
                const groundGeometry = new THREE.PlaneGeometry(100, 100);
                const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
                const ground = new THREE.Mesh(groundGeometry, groundMaterial);
                ground.rotation.x = -Math.PI / 2;
                ground.receiveShadow = true;
                scene.add(ground);
                console.log("Ground created");
            }

            // 创建建筑物
            function createBuildings() {
                console.log("Creating buildings...");

                // 摩天大楼
                for (let i = 0; i < 3; i++) {
                    const height = 15 + Math.random() * 20;
                    const width = 3 + Math.random() * 2;
                    const depth = 3 + Math.random() * 2;

                    const geometry = new THREE.BoxGeometry(width, height, depth);
                    const material = new THREE.MeshLambertMaterial({
                        color: new THREE.Color(0.6, 0.6, 0.7)
                    });
                    const building = new THREE.Mesh(geometry, material);

                    building.position.set(
                        (Math.random() - 0.5) * 40,
                        height / 2,
                        (Math.random() - 0.5) * 40
                    );
                    building.castShadow = true;
                    building.receiveShadow = true;
                    scene.add(building);

                    // 添加简单窗户
                    addWindows(building, width, height, depth);
                }

                // 中等建筑
                for (let i = 0; i < 6; i++) {
                    const height = 6 + Math.random() * 8;
                    const width = 2.5 + Math.random() * 1.5;
                    const depth = 2.5 + Math.random() * 1.5;

                    const geometry = new THREE.BoxGeometry(width, height, depth);
                    const material = new THREE.MeshLambertMaterial({
                        color: new THREE.Color(0.7, 0.5, 0.5)
                    });
                    const building = new THREE.Mesh(geometry, material);

                    building.position.set(
                        (Math.random() - 0.5) * 50,
                        height / 2,
                        (Math.random() - 0.5) * 50
                    );
                    building.castShadow = true;
                    building.receiveShadow = true;
                    scene.add(building);

                    addWindows(building, width, height, depth);
                }

                // 小商店
                for (let i = 0; i < 8; i++) {
                    const height = 2 + Math.random() * 3;
                    const width = 2 + Math.random() * 1;
                    const depth = 2 + Math.random() * 1;

                    const geometry = new THREE.BoxGeometry(width, height, depth);
                    const material = new THREE.MeshLambertMaterial({
                        color: new THREE.Color(0.8, 0.6, 0.4)
                    });
                    const building = new THREE.Mesh(geometry, material);

                    building.position.set(
                        (Math.random() - 0.5) * 30,
                        height / 2,
                        (Math.random() - 0.5) * 30
                    );
                    building.castShadow = true;
                    building.receiveShadow = true;
                    scene.add(building);
                }

                console.log("Buildings created");
            }

            // 添加窗户
            function addWindows(building, width, height, depth) {
                const windowMaterial = new THREE.MeshBasicMaterial({
                    color: Math.random() > 0.5 ? 0xffff88 : 0x444444
                });

                // 前面窗户
                for (let y = 1.5; y < height - 0.5; y += 2) {
                    for (let x = -width/2 + 0.5; x < width/2 - 0.5; x += 1) {
                        const windowGeometry = new THREE.PlaneGeometry(0.6, 0.8);
                        const window1 = new THREE.Mesh(windowGeometry, windowMaterial);
                        window1.position.set(x, y - height/2, depth/2 + 0.01);
                        building.add(window1);
                    }
                }
            }

            // 创建道路
            function createRoads() {
                const roadMaterial = new THREE.MeshLambertMaterial({ color: 0x444444 });

                // 主要道路
                for (let i = -30; i <= 30; i += 15) {
                    // 水平道路
                    const roadH = new THREE.PlaneGeometry(60, 3);
                    const roadMeshH = new THREE.Mesh(roadH, roadMaterial);
                    roadMeshH.rotation.x = -Math.PI / 2;
                    roadMeshH.position.set(0, 0.01, i);
                    roadMeshH.receiveShadow = true;
                    scene.add(roadMeshH);

                    // 垂直道路
                    const roadV = new THREE.PlaneGeometry(3, 60);
                    const roadMeshV = new THREE.Mesh(roadV, roadMaterial);
                    roadMeshV.rotation.x = -Math.PI / 2;
                    roadMeshV.position.set(i, 0.01, 0);
                    roadMeshV.receiveShadow = true;
                    scene.add(roadMeshV);

                    // 人行横道
                    createCrosswalk(0, i);
                    createCrosswalk(i, 0);
                }

                console.log("Roads created");
            }

            // 创建人行横道
            function createCrosswalk(x, z) {
                const stripeMaterial = new THREE.MeshLambertMaterial({ color: 0xffffff });
                for (let i = -1; i <= 1; i += 0.4) {
                    const stripeGeometry = new THREE.PlaneGeometry(0.2, 2);
                    const stripe = new THREE.Mesh(stripeGeometry, stripeMaterial);
                    stripe.rotation.x = -Math.PI / 2;
                    stripe.position.set(x + i, 0.02, z);
                    scene.add(stripe);
                }
            }

            // 创建汽车
            function createCar() {
                const group = new THREE.Group();

                // 车身
                const bodyGeometry = new THREE.BoxGeometry(1.2, 0.5, 2.5);
                const bodyMaterial = new THREE.MeshLambertMaterial({
                    color: new THREE.Color().setHSL(Math.random(), 0.8, 0.5)
                });
                const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
                body.position.y = 0.25;
                body.castShadow = true;
                group.add(body);

                // 车顶
                const roofGeometry = new THREE.BoxGeometry(1, 0.3, 1.2);
                const roof = new THREE.Mesh(roofGeometry, bodyMaterial);
                roof.position.set(0, 0.65, -0.2);
                roof.castShadow = true;
                group.add(roof);

                // 轮子
                const wheelGeometry = new THREE.CylinderGeometry(0.15, 0.15, 0.1);
                const wheelMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });

                const wheels = [
                    { x: -0.5, y: 0.15, z: 0.8 },
                    { x: 0.5, y: 0.15, z: 0.8 },
                    { x: -0.5, y: 0.15, z: -0.8 },
                    { x: 0.5, y: 0.15, z: -0.8 }
                ];

                wheels.forEach(pos => {
                    const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
                    wheel.position.set(pos.x, pos.y, pos.z);
                    wheel.rotation.z = Math.PI / 2;
                    wheel.castShadow = true;
                    group.add(wheel);
                });

                return group;
            }

            // 创建行人
            function createPedestrian() {
                const group = new THREE.Group();

                // 身体
                const bodyGeometry = new THREE.BoxGeometry(0.25, 0.8, 0.15);
                const bodyMaterial = new THREE.MeshLambertMaterial({
                    color: new THREE.Color().setHSL(Math.random(), 0.5, 0.4)
                });
                const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
                body.position.y = 1;
                body.castShadow = true;
                group.add(body);

                // 头部
                const headGeometry = new THREE.SphereGeometry(0.12);
                const headMaterial = new THREE.MeshLambertMaterial({ color: 0xffdbac });
                const head = new THREE.Mesh(headGeometry, headMaterial);
                head.position.y = 1.6;
                head.castShadow = true;
                group.add(head);

                return group;
            }

            // 创建车辆
            function createVehicles() {
                const routes = [
                    { start: -30, end: 30, axis: 'x', z: -30 },
                    { start: -30, end: 30, axis: 'x', z: -15 },
                    { start: -30, end: 30, axis: 'x', z: 0 },
                    { start: -30, end: 30, axis: 'x', z: 15 },
                    { start: -30, end: 30, axis: 'x', z: 30 },
                    { start: -30, end: 30, axis: 'z', x: -30 },
                    { start: -30, end: 30, axis: 'z', x: -15 },
                    { start: -30, end: 30, axis: 'z', x: 0 },
                    { start: -30, end: 30, axis: 'z', x: 15 },
                    { start: -30, end: 30, axis: 'z', x: 30 }
                ];

                routes.forEach(route => {
                    const car = createCar();
                    const progress = Math.random();

                    if (route.axis === 'x') {
                        car.position.set(
                            route.start + (route.end - route.start) * progress,
                            0.3,
                            route.z
                        );
                    } else {
                        car.position.set(
                            route.x,
                            0.3,
                            route.start + (route.end - route.start) * progress
                        );
                        car.rotation.y = Math.PI / 2;
                    }

                    car.userData = {
                        route: route,
                        progress: progress,
                        speed: 0.005 + Math.random() * 0.01
                    };

                    cars.push(car);
                    scene.add(car);
                });

                console.log("Vehicles created");
            }

            // 创建行人
            function createPedestrians() {
                for (let i = 0; i < 6; i++) {
                    const pedestrian = createPedestrian();
                    pedestrian.position.set(
                        (Math.random() - 0.5) * 40,
                        0,
                        (Math.random() - 0.5) * 40
                    );

                    pedestrian.userData = {
                        targetX: (Math.random() - 0.5) * 40,
                        targetZ: (Math.random() - 0.5) * 40,
                        speed: 0.01 + Math.random() * 0.02
                    };

                    pedestrians.push(pedestrian);
                    scene.add(pedestrian);
                }

                console.log("Pedestrians created");
            }

            // 创建路灯
            function createStreetLights() {
                for (let x = -25; x <= 25; x += 10) {
                    for (let z = -25; z <= 25; z += 10) {
                        if (Math.abs(x) % 15 === 0 || Math.abs(z) % 15 === 0) {
                            const group = new THREE.Group();

                            // 灯杆
                            const poleGeometry = new THREE.CylinderGeometry(0.05, 0.05, 4);
                            const poleMaterial = new THREE.MeshLambertMaterial({ color: 0x666666 });
                            const pole = new THREE.Mesh(poleGeometry, poleMaterial);
                            pole.position.y = 2;
                            pole.castShadow = true;
                            group.add(pole);

                            // 灯
                            const lightGeometry = new THREE.SphereGeometry(0.15);
                            const lightMaterial = new THREE.MeshBasicMaterial({ color: 0xffff99 });
                            const lightBulb = new THREE.Mesh(lightGeometry, lightMaterial);
                            lightBulb.position.y = 4;
                            group.add(lightBulb);

                            group.position.set(x, 0, z);
                            scene.add(group);
                        }
                    }
                }

                console.log("Street lights created");
            }

            // 设置控制
            function setupControls() {
                document.addEventListener('keydown', (event) => {
                    keys[event.code] = true;

                    if (event.code === 'Space') {
                        event.preventDefault();
                        speedIndex = (speedIndex + 1) % speedOptions.length;
                        timeSpeed = speedOptions[speedIndex];
                        document.getElementById('speedDisplay').textContent = timeSpeed + 'x';
                    }

                    if (event.code === 'KeyR') {
                        camera.position.set(0, 15, 25);
                        camera.lookAt(0, 0, 0);
                    }
                });

                document.addEventListener('keyup', (event) => {
                    keys[event.code] = false;
                });

                document.addEventListener('mousemove', (event) => {
                    mouseX = (event.clientX / window.innerWidth) * 2 - 1;
                    mouseY = -(event.clientY / window.innerHeight) * 2 + 1;
                });

                window.addEventListener('resize', () => {
                    camera.aspect = window.innerWidth / window.innerHeight;
                    camera.updateProjectionMatrix();
                    renderer.setSize(window.innerWidth, window.innerHeight);
                });
            }

            // 更新函数
            function updateCamera() {
                const speed = 0.3;

                if (keys['KeyW']) camera.position.z -= speed;
                if (keys['KeyS']) camera.position.z += speed;
                if (keys['KeyA']) camera.position.x -= speed;
                if (keys['KeyD']) camera.position.x += speed;

                const targetX = camera.position.x + mouseX * 3;
                const targetZ = camera.position.z + mouseY * 3;
                camera.lookAt(targetX, 0, targetZ);
            }

            function updateTime() {
                const deltaTime = clock.getDelta();
                time += deltaTime * timeSpeed * 0.3;

                if (time >= 24) time -= 24;
                if (time < 0) time += 24;

                const hours = Math.floor(time);
                const minutes = Math.floor((time - hours) * 60);
                document.getElementById('timeDisplay').textContent =
                    `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;

                updateLighting();
            }

            function updateLighting() {
                const isDay = time >= 6 && time <= 18;
                const sunIntensity = isDay ? Math.sin((time - 6) / 12 * Math.PI) * 0.8 + 0.2 : 0.1;

                scene.userData.sunLight.intensity = sunIntensity;
                scene.userData.ambientLight.intensity = isDay ? 0.4 : 0.2;

                if (isDay) {
                    const progress = (time - 6) / 12;
                    const skyColor = new THREE.Color().setHSL(0.55, 0.6, 0.3 + progress * 0.4);
                    renderer.setClearColor(skyColor);
                } else {
                    renderer.setClearColor(0x001122);
                }
            }

            function updateVehicles() {
                cars.forEach(car => {
                    const data = car.userData;
                    data.progress += data.speed;

                    if (data.progress >= 1) {
                        data.progress = 0;
                    }

                    if (data.route.axis === 'x') {
                        car.position.x = data.route.start + (data.route.end - data.route.start) * data.progress;
                    } else {
                        car.position.z = data.route.start + (data.route.end - data.route.start) * data.progress;
                    }
                });
            }

            function updatePedestrians() {
                pedestrians.forEach(pedestrian => {
                    const data = pedestrian.userData;

                    const dx = data.targetX - pedestrian.position.x;
                    const dz = data.targetZ - pedestrian.position.z;
                    const distance = Math.sqrt(dx * dx + dz * dz);

                    if (distance > 0.5) {
                        pedestrian.position.x += (dx / distance) * data.speed;
                        pedestrian.position.z += (dz / distance) * data.speed;
                        pedestrian.rotation.y = Math.atan2(dx, dz);
                    } else {
                        data.targetX = (Math.random() - 0.5) * 40;
                        data.targetZ = (Math.random() - 0.5) * 40;
                    }
                });
            }

            // 动画循环
            function animate() {
                requestAnimationFrame(animate);

                updateCamera();
                updateTime();
                updateVehicles();
                updatePedestrians();

                renderer.render(scene, camera);
            }

            // 创建场景
            try {
                setupLighting();
                createGround();
                createBuildings();
                createRoads();
                createStreetLights();
                createVehicles();
                createPedestrians();
                setupControls();

                // 隐藏加载提示，显示界面
                document.getElementById('loading').style.display = 'none';
                document.getElementById('info').style.display = 'block';
                document.getElementById('controls').style.display = 'block';

                console.log("City scene complete, starting animation...");
                animate();

            } catch (error) {
                console.error("Error creating city scene:", error);
                document.getElementById('loading').textContent = "Error loading city scene. Check console for details.";
            }
        }

        // 启动初始化
        initCity();
    </script>
</body>
</html>