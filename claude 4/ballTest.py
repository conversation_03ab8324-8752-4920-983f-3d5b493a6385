import pygame
import math
import numpy as np
from collections import deque


class Vector2D:
    """2D向量类"""

    def __init__(self, x=0, y=0):
        self.x = x
        self.y = y

    def __add__(self, other):
        return Vector2D(self.x + other.x, self.y + other.y)

    def __sub__(self, other):
        return Vector2D(self.x - other.x, self.y - other.y)

    def __mul__(self, scalar):
        return Vector2D(self.x * scalar, self.y * scalar)

    def __rmul__(self, scalar):
        return self.__mul__(scalar)

    def dot(self, other):
        return self.x * other.x + self.y * other.y

    def magnitude(self):
        return math.sqrt(self.x ** 2 + self.y ** 2)

    def normalize(self):
        mag = self.magnitude()
        if mag > 0:
            return Vector2D(self.x / mag, self.y / mag)
        return Vector2D(0, 0)

    def rotate(self, angle):
        """旋转向量"""
        cos_a = math.cos(angle)
        sin_a = math.sin(angle)
        return Vector2D(
            self.x * cos_a - self.y * sin_a,
            self.x * sin_a + self.y * cos_a
        )

    def to_tuple(self):
        return (int(self.x), int(self.y))


class Hexagon:
    """正六边形类"""

    def __init__(self, center, radius, rotation_speed, color):
        self.center = center
        self.radius = radius
        self.rotation_speed = rotation_speed  # 弧度/秒
        self.angle = 0
        self.color = color
        self.vertices = []
        self.update_vertices()

    def update_vertices(self):
        """更新六边形顶点"""
        self.vertices = []
        for i in range(6):
            angle = self.angle + i * math.pi / 3
            x = self.center.x + self.radius * math.cos(angle)
            y = self.center.y + self.radius * math.sin(angle)
            self.vertices.append(Vector2D(x, y))

    def update(self, dt):
        """更新旋转角度"""
        self.angle += self.rotation_speed * dt
        # 防止角度过大导致数值问题
        self.angle = self.angle % (2 * math.pi)
        self.update_vertices()

    def get_edges(self):
        """获取六边形的边"""
        edges = []
        for i in range(6):
            start = self.vertices[i]
            end = self.vertices[(i + 1) % 6]
            edges.append((start, end))
        return edges

    def point_inside(self, point):
        """判断点是否在六边形内部"""
        x, y = point.x, point.y
        n = len(self.vertices)
        inside = False

        p1x, p1y = self.vertices[0].x, self.vertices[0].y
        for i in range(1, n + 1):
            p2x, p2y = self.vertices[i % n].x, self.vertices[i % n].y
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y

        return inside


class Ball:
    """小球类"""

    def __init__(self, position, radius=5, mass=1.0):
        self.position = position
        self.velocity = Vector2D(0, 0)
        self.radius = radius
        self.mass = mass
        self.restitution = 0.7  # 降低弹性系数
        self.trail = deque(maxlen=15)  # 缩短轨迹长度
        self.max_speed = 800  # 最大速度限制

    def update(self, dt, gravity, hexagons):
        """更新小球状态"""
        # 限制速度防止数值爆炸
        speed = self.velocity.magnitude()
        if speed > self.max_speed:
            self.velocity = self.velocity.normalize() * self.max_speed

        # 记录轨迹（每几帧记录一次，减少轨迹密度）
        if len(self.trail) == 0 or \
                (abs(self.position.x - self.trail[-1][0]) > 5 or
                 abs(self.position.y - self.trail[-1][1]) > 5):
            self.trail.append((self.position.x, self.position.y))

        # 应用重力
        self.velocity = self.velocity + gravity * dt

        # 检查与六边形的碰撞
        self.handle_collisions(hexagons, dt)

        # 更新位置
        self.position = self.position + self.velocity * dt

        # 防止小球位置出现异常值
        if (abs(self.position.x) > 2000 or abs(self.position.y) > 2000 or
                math.isnan(self.position.x) or math.isnan(self.position.y)):
            self.reset_to_center()

    def reset_to_center(self):
        """重置小球到中心位置"""
        self.position = Vector2D(500, 350)  # 屏幕中心
        self.velocity = Vector2D(0, 0)
        self.trail.clear()

    def handle_collisions(self, hexagons, dt):
        """处理与六边形的碰撞"""
        for hexagon in hexagons:
            edges = hexagon.get_edges()
            for start, end in edges:
                collision_info = self.check_edge_collision(start, end)
                if collision_info:
                    normal, distance = collision_info
                    if distance < self.radius + 2:  # 增加一点容错
                        self.resolve_collision(normal, hexagon, start, end, dt)

    def check_edge_collision(self, start, end):
        """检查与边的碰撞"""
        edge = end - start
        to_ball = self.position - start

        if edge.magnitude() == 0:
            return None

        # 投影参数
        t = max(0, min(1, to_ball.dot(edge) / edge.dot(edge)))
        closest_point = start + edge * t

        # 计算距离和法向量
        to_closest = self.position - closest_point
        distance = to_closest.magnitude()

        if distance > 0:
            normal = to_closest.normalize()
            return normal, distance

        return None

    def resolve_collision(self, normal, hexagon, edge_start, edge_end, dt):
        """解决碰撞"""
        # 计算边的切向量
        edge_vector = (edge_end - edge_start).normalize()

        # 计算边上碰撞点的线速度（由于六边形旋转）
        edge_midpoint = Vector2D(
            (edge_start.x + edge_end.x) / 2,
            (edge_start.y + edge_end.y) / 2
        )
        radius_vector = edge_midpoint - hexagon.center
        tangent_velocity = radius_vector.rotate(
            math.pi / 2).normalize() * hexagon.rotation_speed * radius_vector.magnitude()

        # 分解小球速度为法向和切向分量
        velocity_normal = normal * self.velocity.dot(normal)
        velocity_tangent = self.velocity - velocity_normal

        # 反弹法向分量
        new_velocity_normal = velocity_normal * (-self.restitution)

        # 切向分量受到旋转面的影响（减少影响强度）
        rotation_influence = 0.1  # 降低旋转影响系数
        tangent_force = tangent_velocity * rotation_influence
        new_velocity_tangent = velocity_tangent + tangent_force * dt

        # 合成新速度
        self.velocity = new_velocity_normal + new_velocity_tangent

        # 位置修正，防止穿透
        penetration = self.radius - (self.position - edge_start).dot(normal)
        if penetration > 0:
            self.position = self.position + normal * (penetration + 1)


class PhysicsSimulation:
    """物理模拟系统"""

    def __init__(self):
        pygame.init()
        self.width = 1000
        self.height = 700
        self.screen = pygame.display.set_mode((self.width, self.height))
        pygame.display.set_caption("交叉旋转正六边形物理模拟")
        self.clock = pygame.time.Clock()

        # 物理参数
        self.gravity = Vector2D(0, 200)  # 降低重力

        # 创建两个正六边形
        hex_radius = 120
        left_center = Vector2D(self.width / 2 - 60, self.height / 2)
        right_center = Vector2D(self.width / 2 + 60, self.height / 2)

        self.left_hexagon = Hexagon(left_center, hex_radius, -1.5, (0, 100, 255))  # 降低旋转速度
        self.right_hexagon = Hexagon(right_center, hex_radius, 1.5, (0, 255, 100))

        # 创建小球
        ball_start = Vector2D(self.width / 2, self.height / 2 - 20)
        self.ball = Ball(ball_start, radius=8)

        # 字体
        self.font = pygame.font.Font(None, 24)

        self.running = True

    def find_intersection_area(self):
        """找到两个六边形的重叠区域"""
        intersection_points = []

        # 检查每个六边形的顶点是否在另一个六边形内
        for vertex in self.left_hexagon.vertices:
            if self.right_hexagon.point_inside(vertex):
                intersection_points.append(vertex)

        for vertex in self.right_hexagon.vertices:
            if self.left_hexagon.point_inside(vertex):
                intersection_points.append(vertex)

        return intersection_points

    def constrain_ball_to_intersection(self):
        """约束小球在重叠区域内"""
        in_left = self.left_hexagon.point_inside(self.ball.position)
        in_right = self.right_hexagon.point_inside(self.ball.position)

        # 如果小球完全离开了重叠区域，重置位置
        if not (in_left and in_right):
            # 温和地推回小球
            center = Vector2D(self.width / 2, self.height / 2)
            direction = center - self.ball.position

            if direction.magnitude() > 0:
                # 推向中心
                push_force = direction.normalize() * 50
                self.ball.velocity = self.ball.velocity + push_force * 0.01
            else:
                # 如果在中心，重置位置
                self.ball.reset_to_center()

    def update(self, dt):
        """更新模拟"""
        # 限制dt防止数值不稳定
        dt = min(dt, 1 / 30)  # 最大时间步长

        # 更新六边形旋转
        self.left_hexagon.update(dt)
        self.right_hexagon.update(dt)

        # 更新小球
        self.ball.update(dt, self.gravity, [self.left_hexagon, self.right_hexagon])

        # 约束小球在重叠区域
        self.constrain_ball_to_intersection()

    def draw(self):
        """绘制场景"""
        self.screen.fill((20, 20, 40))

        # 绘制重叠区域
        intersection_points = self.find_intersection_area()
        if len(intersection_points) > 2:
            pygame.draw.polygon(self.screen, (60, 40, 80),
                                [p.to_tuple() for p in intersection_points])

        # 绘制六边形
        self.draw_hexagon(self.left_hexagon)
        self.draw_hexagon(self.right_hexagon)

        # 绘制小球轨迹（简化版本）
        self.draw_trail()

        # 绘制小球
        pygame.draw.circle(self.screen, (255, 50, 50),
                           self.ball.position.to_tuple(),
                           self.ball.radius)

        # 绘制小球外圈（增强可见性）
        pygame.draw.circle(self.screen, (255, 150, 150),
                           self.ball.position.to_tuple(),
                           self.ball.radius, 2)

        # 显示物理参数
        self.draw_info()

        pygame.display.flip()

    def draw_hexagon(self, hexagon):
        """绘制六边形"""
        points = [v.to_tuple() for v in hexagon.vertices]
        pygame.draw.polygon(self.screen, hexagon.color, points, 3)

    def draw_trail(self):
        """绘制小球轨迹（简化版本，去掉长尾巴）"""
        if len(self.ball.trail) > 1:
            # 只绘制最近的几个点，颜色不透明
            recent_trail = list(self.ball.trail)[-8:]  # 只显示最近8个点
            for i in range(1, len(recent_trail)):
                color = (150, 50, 50)  # 固定颜色，不透明
                pygame.draw.line(self.screen, color,
                                 (int(recent_trail[i - 1][0]), int(recent_trail[i - 1][1])),
                                 (int(recent_trail[i][0]), int(recent_trail[i][1])), 1)

    def draw_info(self):
        """显示物理信息"""
        speed = self.ball.velocity.magnitude()
        info_texts = [
            f"小球速度: {speed:.1f}",
            f"小球位置: ({self.ball.position.x:.1f}, {self.ball.position.y:.1f})",
            f"左六边形角度: {math.degrees(self.left_hexagon.angle):.1f}°",
            f"右六边形角度: {math.degrees(self.right_hexagon.angle):.1f}°",
            f"按空格键重置小球"
        ]

        for i, text in enumerate(info_texts):
            surface = self.font.render(text, True, (255, 255, 255))
            self.screen.blit(surface, (10, 10 + i * 25))

    def handle_events(self):
        """处理事件"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_SPACE:
                    # 空格键重置小球位置
                    self.ball.reset_to_center()
                elif event.key == pygame.K_r:
                    # R键重启模拟
                    self.__init__()

    def run(self):
        """运行模拟"""
        while self.running:
            dt = self.clock.tick(60) / 1000.0

            self.handle_events()
            self.update(dt)
            self.draw()

        pygame.quit()


if __name__ == "__main__":
    simulation = PhysicsSimulation()
    simulation.run()