from pptx import Presentation
import os
import sys
from transformers import MarianMTModel, MarianToken<PERSON>
import torch
import logging
from pptx.enum.shapes import MSO_SHAPE_TYPE

# 设置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_translation():
    """初始化翻译模型和分词器"""
    try:
        model_name = "Helsinki-NLP/opus-mt-zh-en"
        logger.info(f"Loading translation model: {model_name}")
        tokenizer = MarianTokenizer.from_pretrained(model_name)
        model = MarianMTModel.from_pretrained(model_name)
        return model, tokenizer
    except Exception as e:
        logger.error(f"Error loading translation model: {e}")
        sys.exit(1)

def translate_text(text, model, tokenizer):
    """使用指定模型翻译文本"""
    try:
        if not text.strip():
            return text

        # 将文本转换为模型输入
        inputs = tokenizer(text, return_tensors="pt", truncation=True, max_length=512)

        # 生成翻译
        outputs = model.generate(**inputs)
        translated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)

        return translated_text
    except Exception as e:
        logger.error(f"Translation error: {e}")
        return text

def translate_table(table, model, tokenizer):
    """翻译��格内容"""
    for row in table.rows:
        for cell in row.cells:
            if cell.text.strip():
                original_text = cell.text.strip()
                translated_text = translate_text(original_text, model, tokenizer)
                cell.text = translated_text

def process_ppt(input_path, output_path, model, tokenizer):
    """处理PPT文件"""
    try:
        if not os.path.exists(input_path):
            raise FileNotFoundError(f"Input file not found: {input_path}")

        logger.info(f"Opening PPT file: {input_path}")
        prs = Presentation(input_path)

        total_slides = len(prs.slides)
        logger.info(f"Found {total_slides} slides")

        for idx, slide in enumerate(prs.slides, 1):
            logger.info(f"Processing slide {idx}/{total_slides}")
            for shape in slide.shapes:
                # 处理表格
                if shape.has_table:
                    logger.info(f"Processing table on slide {idx}")
                    translate_table(shape.table, model, tokenizer)

                # 处理普通文本
                elif hasattr(shape, "text") and shape.text.strip():
                    original_text = shape.text.strip()
                    translated_text = translate_text(original_text, model, tokenizer)
                    logger.info(f"Translated text on slide {idx}: {original_text[:30]}... -> {translated_text[:30]}...")

                    if hasattr(shape, "text_frame"):
                        text_frame = shape.text_frame
                        if text_frame.text.strip():
                            for paragraph in text_frame.paragraphs:
                                if paragraph.text.strip():
                                    paragraph.text = translated_text

        output_dir = os.path.dirname(output_path)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        logger.info(f"Saving translated PPT to: {output_path}")
        prs.save(output_path)
        logger.info("Translation completed successfully!")

    except Exception as e:
        logger.error(f"Error processing PPT: {e}")
        raise

def main():
    try:
        # 获取脚本所在目录的绝对路径
        script_dir = os.path.dirname(os.path.abspath(__file__))

        # 设置输入和输出文件路径
        input_file = os.path.join(script_dir, "威尔森OTA监测报告-25年3月.pptx")
        output_file = os.path.join(script_dir, "威尔森OTA监测报告-25年3月-英文版.pptx")

        logger.info("Initializing translation model...")
        model, tokenizer = setup_translation()

        logger.info("Starting PPT translation process...")
        process_ppt(input_file, output_file, model, tokenizer)

        logger.info(f"Translation completed. Output saved to: {output_file}")

    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
