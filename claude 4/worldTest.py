import pygame
import math
import random

# 初始化pygame
pygame.init()

# 常量设置
WIDTH, HEIGHT = 800, 600
FPS = 60
GRAVITY = 0.3
FRICTION = 0.999
BOUNCE_DAMPING = 0.85

# 颜色定义
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
GREEN = (0, 255, 0)
BLUE = (0, 150, 255)
RED = (255, 100, 100)


class Ball:
    def __init__(self, x, y, radius=12):
        self.x = x
        self.y = y
        self.vx = random.uniform(-3, 3)
        self.vy = random.uniform(-3, 3)
        self.radius = radius
        self.color = GREEN

    def update(self):
        # 应用重力
        self.vy += GRAVITY

        # 应用空气阻力
        self.vx *= FRICTION
        self.vy *= FRICTION

        # 更新位置
        self.x += self.vx
        self.y += self.vy

    def draw(self, screen):
        pygame.draw.circle(screen, self.color, (int(self.x), int(self.y)), self.radius)
        # 添加高光效果
        pygame.draw.circle(screen, WHITE, (int(self.x - 4), int(self.y - 4)), 3)


class Dodecagon:
    def __init__(self, center_x, center_y, radius):
        self.center_x = center_x
        self.center_y = center_y
        self.radius = radius
        self.rotation = 0
        self.rotation_speed = 0.8  # 度/帧
        self.vertices = []
        self.edges = []

    def update(self):
        # 更新旋转角度
        self.rotation += self.rotation_speed
        if self.rotation >= 360:
            self.rotation -= 360

        # 计算顶点位置（正十二边形）
        self.vertices = []
        for i in range(12):
            angle = math.radians(i * 30 + self.rotation)  # 正十二边形每个角30度
            x = self.center_x + self.radius * math.cos(angle)
            y = self.center_y + self.radius * math.sin(angle)
            self.vertices.append((x, y))

        # 计算边
        self.edges = []
        for i in range(12):
            start = self.vertices[i]
            end = self.vertices[(i + 1) % 12]
            self.edges.append((start, end))

    def draw(self, screen):
        if len(self.vertices) > 2:
            pygame.draw.polygon(screen, BLUE, self.vertices, 2)


def get_line_normal(p1, p2):
    """获取线段的单位法向量（指向内部）"""
    dx = p2[0] - p1[0]
    dy = p2[1] - p1[1]

    # 计算垂直向量（逆时针旋转90度）
    normal_x = -dy
    normal_y = dx

    # 单位化
    length = math.sqrt(normal_x ** 2 + normal_y ** 2)
    if length > 0:
        normal_x /= length
        normal_y /= length

    return normal_x, normal_y


def point_to_line_distance(px, py, x1, y1, x2, y2):
    """计算点到线段的最短距离和最近点"""
    # 线段向量
    dx = x2 - x1
    dy = y2 - y1

    # 如果线段长度为0
    if dx == 0 and dy == 0:
        return math.sqrt((px - x1) ** 2 + (py - y1) ** 2), x1, y1

    # 计算投影参数t
    t = ((px - x1) * dx + (py - y1) * dy) / (dx * dx + dy * dy)
    t = max(0, min(1, t))  # 限制在线段范围内

    # 计算最近点
    closest_x = x1 + t * dx
    closest_y = y1 + t * dy

    # 计算距离
    distance = math.sqrt((px - closest_x) ** 2 + (py - closest_y) ** 2)

    return distance, closest_x, closest_y


def check_collision(ball, dodecagon):
    """检查球与十二边形的碰撞"""
    collision_occurred = False

    for edge in dodecagon.edges:
        x1, y1 = edge[0]
        x2, y2 = edge[1]

        # 计算球心到线段的距离
        distance, closest_x, closest_y = point_to_line_distance(ball.x, ball.y, x1, y1, x2, y2)

        if distance < ball.radius:
            collision_occurred = True

            # 计算从最近点指向球心的向量（法向量）
            if distance > 0:
                normal_x = (ball.x - closest_x) / distance
                normal_y = (ball.y - closest_y) / distance
            else:
                # 如果球心在线段上，使用线段的法向量
                normal_x, normal_y = get_line_normal(edge[0], edge[1])

            # 将球推出墙壁
            overlap = ball.radius - distance + 1  # +1 确保完全分离
            ball.x += normal_x * overlap
            ball.y += normal_y * overlap

            # 计算速度在法向量上的分量
            velocity_dot_normal = ball.vx * normal_x + ball.vy * normal_y

            # 只有当球向墙壁运动时才反射
            if velocity_dot_normal < 0:
                # 反射速度
                ball.vx -= 2 * velocity_dot_normal * normal_x
                ball.vy -= 2 * velocity_dot_normal * normal_y

                # 应用弹跳阻尼
                ball.vx *= BOUNCE_DAMPING
                ball.vy *= BOUNCE_DAMPING

    return collision_occurred


def main():
    screen = pygame.display.set_mode((WIDTH, HEIGHT))
    pygame.display.set_caption("旋转正十二边形中的弹跳球")
    clock = pygame.time.Clock()

    # 创建球和正十二边形
    ball = Ball(WIDTH // 2, HEIGHT // 2 - 50)
    dodecagon = Dodecagon(WIDTH // 2, HEIGHT // 2, 180)

    running = True
    paused = False

    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_SPACE:
                    # 空格键重置球的位置和速度
                    ball.x = WIDTH // 2 + random.uniform(-50, 50)
                    ball.y = HEIGHT // 2 - 50
                    ball.vx = random.uniform(-6, 6)
                    ball.vy = random.uniform(-6, 6)
                elif event.key == pygame.K_UP:
                    # 增加旋转速度
                    dodecagon.rotation_speed += 0.2
                elif event.key == pygame.K_DOWN:
                    # 减少旋转速度
                    dodecagon.rotation_speed = max(0, dodecagon.rotation_speed - 0.2)
                elif event.key == pygame.K_p:
                    # 暂停/继续
                    paused = not paused
                elif event.key == pygame.K_r:
                    # 重置所有
                    ball = Ball(WIDTH // 2, HEIGHT // 2 - 50)
                    dodecagon.rotation_speed = 0.8

        if not paused:
            # 更新
            ball.update()
            dodecagon.update()

            # 碰撞检测
            check_collision(ball, dodecagon)

        # 绘制
        screen.fill(BLACK)
        dodecagon.draw(screen)
        ball.draw(screen)

        # 显示信息
        font = pygame.font.Font(None, 24)
        info_lines = [
            f"旋转速度: {dodecagon.rotation_speed:.1f}",
            f"球速度: ({ball.vx:.1f}, {ball.vy:.1f})",
            f"球位置: ({ball.x:.0f}, {ball.y:.0f})",
            "",
            "控制:",
            "空格: 重置球",
            "↑↓: 调节旋转速度",
            "P: 暂停/继续",
            "R: 重置全部"
        ]

        for i, line in enumerate(info_lines):
            text = font.render(line, True, WHITE)
            screen.blit(text, (10, 10 + i * 25))

        if paused:
            pause_text = pygame.font.Font(None, 48).render("暂停", True, RED)
            screen.blit(pause_text, (WIDTH // 2 - 40, HEIGHT // 2))

        pygame.display.flip()
        clock.tick(FPS)

    pygame.quit()


if __name__ == "__main__":
    main()