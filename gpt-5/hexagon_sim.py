import pygame
import math
from collections import deque
from shapely.geometry import Polygon

pygame.init()

# 窗口设置
W, H = 800, 600
screen = pygame.display.set_mode((W, H))
pygame.display.set_caption("交叉旋转正六边形物理模拟")
clock = pygame.time.Clock()

# 物理参数
g = 200        # 重力加速度
restitution = 0.9  # 弹性系数
ball_radius = 8

# 六边形参数
hex_radius = 120
left_center = (W//2 - 100, H//2)
right_center = (W//2 + 100, H//2)
omega_left =  math.radians(40)   # 逆时针角速度
omega_right = -math.radians(40)  # 顺时针角速度

angle_left = 0
angle_right = 0

# 小球初始状态
ball_pos = [W//2, H//2]
ball_vel = [100, -50]

# 轨迹记录
trail = deque(maxlen=50)

def regular_hexagon_points(center, radius, angle):
    """返回旋转后的正六边形顶点"""
    cx, cy = center
    pts = []
    for i in range(6):
        a = math.radians(60*i) + angle
        x = cx + radius * math.cos(a)
        y = cy + radius * math.sin(a)
        pts.append((x, y))
    return pts

def point_line_distance(p, a, b):
    """点到线段距离和最近点"""
    px, py = p
    ax, ay = a
    bx, by = b
    apx, apy = px - ax, py - ay
    abx, aby = bx - ax, by - ay
    ab_len2 = abx*abx + aby*aby
    if ab_len2 == 0:
        return math.hypot(apx, apy), a
    t = max(0, min(1, (apx*abx + apy*aby) / ab_len2))
    closest = (ax + t*abx, ay + t*aby)
    dx, dy = px - closest[0], py - closest[1]
    return math.hypot(dx, dy), closest

def collide_with_polygon(ball_pos, ball_vel, polygon, restitution=1.0):
    """检测并处理小球与多边形边的碰撞"""
    px, py = ball_pos
    for i in range(len(polygon)):
        a = polygon[i]
        b = polygon[(i+1)%len(polygon)]
        dist, closest = point_line_distance((px, py), a, b)
        if dist < ball_radius:
            nx, ny = px - closest[0], py - closest[1]
            n_len = math.hypot(nx, ny)
            if n_len == 0:
                continue
            nx /= n_len
            ny /= n_len
            overlap = ball_radius - dist
            ball_pos[0] += nx * overlap
            ball_pos[1] += ny * overlap
            vn = ball_vel[0]*nx + ball_vel[1]*ny
            ball_vel[0] -= (1+restitution) * vn * nx
            ball_vel[1] -= (1+restitution) * vn * ny
    return ball_pos, ball_vel

def add_tangent_velocity(ball_pos, ball_vel, polygon, center, omega):
    """根据旋转多边形的边界点给小球增加切向速度"""
    # 找到最近的边界点
    min_dist = 1e9
    closest_point = None
    px, py = ball_pos
    for i in range(len(polygon)):
        a = polygon[i]
        b = polygon[(i+1)%len(polygon)]
        dist, cp = point_line_distance((px, py), a, b)
        if dist < min_dist:
            min_dist = dist
            closest_point = cp
    if closest_point:
        cx, cy = center
        rx, ry = closest_point[0] - cx, closest_point[1] - cy
        tangent_vx = -omega * ry
        tangent_vy =  omega * rx
        ball_vel[0] += tangent_vx
        ball_vel[1] += tangent_vy
    return ball_vel

def get_intersection_polygon(poly1_pts, poly2_pts):
    """返回两个多边形的交集顶点列表"""
    p1 = Polygon(poly1_pts)
    p2 = Polygon(poly2_pts)
    inter = p1.intersection(p2)
    if inter.is_empty:
        return []
    if inter.geom_type == 'Polygon':
        return list(inter.exterior.coords)
    elif inter.geom_type == 'MultiPolygon':
        largest = max(inter.geoms, key=lambda g: g.area)
        return list(largest.exterior.coords)
    else:
        return []

running = True
while running:
    dt = clock.tick(60) / 1000.0

    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False

    # 更新六边形角度
    angle_left  += omega_left  * dt
    angle_right += omega_right * dt

    # 重力
    ball_vel[1] += g * dt

    # 位置更新
    ball_pos[0] += ball_vel[0] * dt
    ball_pos[1] += ball_vel[1] * dt

    # 生成六边形
    hex_left  = regular_hexagon_points(left_center,  hex_radius, angle_left)
    hex_right = regular_hexagon_points(right_center, hex_radius, angle_right)

    # 计算交集多边形
    intersection_pts = get_intersection_polygon(hex_left, hex_right)

    # 先用交集区域限制小球（防止出界）
    if intersection_pts:
        ball_pos, ball_vel = collide_with_polygon(ball_pos, ball_vel, intersection_pts, restitution)

    # 再根据各自旋转六边形的边给小球添加切向速度
    ball_vel = add_tangent_velocity(ball_pos, ball_vel, hex_left,  left_center, omega_left)
    ball_vel = add_tangent_velocity(ball_pos, ball_vel, hex_right, right_center, omega_right)

    # 绘制
    screen.fill((0,0,0))

    # 交集区域绘制
    if intersection_pts:
        pygame.draw.polygon(screen, (200,0,200), intersection_pts, 0)  # 紫色填充

    # 六边形轮廓
    pygame.draw.polygon(screen, (0,0,255), hex_left, 2)
    pygame.draw.polygon(screen, (0,255,0), hex_right, 2)

    # 轨迹
    trail.append((ball_pos[0], ball_pos[1]))
    for i, pos in enumerate(trail):
        alpha = int(255 * (i / len(trail)))
        pygame.draw.circle(screen, (255,0,0), (int(pos[0]), int(pos[1])), 3)

    # 小球
    pygame.draw.circle(screen, (255,0,0), (int(ball_pos[0]), int(ball_pos[1])), ball_radius)

    # 显示速度
    font = pygame.font.SysFont(None, 20)
    vel_text = font.render(f"v=({ball_vel[0]:.1f},{ball_vel[1]:.1f})", True, (255,255,255))
    screen.blit(vel_text, (10,10))

    pygame.display.flip()

pygame.quit()
