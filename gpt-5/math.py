# Segmented sieve to count primes up to n and determine the index of n (if prime).
# No external libraries used.
n = 179_424_673

import math
def simple_sieve(limit):
    """Return list of primes up to limit (inclusive) using simple sieve."""
    sieve = bytearray(b'\x01') * (limit + 1)
    sieve[0:2] = b'\x00\x00'
    for p in range(2, int(limit**0.5) + 1):
        if sieve[p]:
            step = p
            start = p*p
            sieve[start:limit+1:step] = b'\x00' * ((limit - start)//step + 1)
    return [i for i, isprime in enumerate(sieve) if isprime]

# 1. get primes up to sqrt(n)
limit = int(math.isqrt(n)) + 1
primes = simple_sieve(limit)

# 2. segmented sieve over [2..n], count primes, and check if n is prime
count = 0
segment_size = 1_000_000  # 1 million per segment (adjustable)

is_n_prime = False

for low in range(2, n + 1, segment_size):
    high = min(low + segment_size - 1, n)
    seg_len = high - low + 1
    segment = bytearray(b'\x01') * seg_len  # True means prime candidate
    for p in primes:
        # find the smallest multiple of p in [low, high]
        start = (low + p - 1) // p * p
        if start < p*p:
            start = p*p
        if start > high:
            continue
        # mark multiples as composite
        start_index = start - low
        step = p
        segment[start_index:seg_len:step] = b'\x00' * ((seg_len - 1 - start_index)//step + 1)
    # count primes in this segment
    for i, isprime in enumerate(segment):
        if isprime:
            num = low + i
            if num >= 2:
                count += 1
                if num == n:
                    is_n_prime = True

print(f"Number: {n}")
print(f"Is prime: {is_n_prime}")
if is_n_prime:
    print(f"It is the {count:,}th prime (1-indexed).")
else:
    print(f"{n} is not prime. There are {count:,} primes <= {n}.")
