<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8"/>
<title>Game of Life mapped to 3D shapes (single file)</title>
<meta name="viewport" content="width=device-width,initial-scale=1" />
<style>
  html,body { height:100%; margin:0; background:#0b0b0f; color:#ddd; font-family:system-ui,Segoe UI,Roboto,Helvetica,Arial; -webkit-font-smoothing:antialiased; }
  #topbar { position: absolute; left:12px; top:12px; z-index:20; background: rgba(10,10,12,0.7); padding:10px; border-radius:8px; box-shadow:0 8px 30px rgba(0,0,0,0.6); }
  label, select, input, button { font-size:13px; color:#ddd; }
  button { margin-left:6px; }
  #canvas { display:block; width:100vw; height:100vh; }
  .small { font-size:12px; color:#aaa; margin-top:6px; }
  #credits { position: absolute; right:12px; bottom:12px; color:#889; font-size:12px; }
</style>
</head>
<body>
  <div id="topbar">
    <div style="display:flex;gap:8px;align-items:center;">
      <label>Shape:</label>
      <select id="shape">
        <option value="plane">Plane</option>
        <option value="sphere" selected>Sphere</option>
        <option value="cylinder">Cylinder</option>
        <option value="torus">Torus</option>
        <option value="moebius">Möbius-like</option>
      </select>

      <label>Grid:</label>
      <input id="gridSize" type="number" min="8" max="300" value="64" style="width:68px"/>

      <label>Wrap:</label>
      <input id="wrap" type="checkbox" checked />

      <label>Speed:</label>
      <input id="speed" type="range" min="1" max="60" value="12"/>
      <span id="speedVal">12</span>

      <button id="start">Start</button>
      <button id="step">Step</button>
      <button id="random">Random</button>
      <button id="clear">Clear</button>
    </div>
    <div class="small">Drag to rotate · Scroll to zoom · Click a dot to toggle a cell</div>
  </div>

  <canvas id="canvas"></canvas>
  <div id="credits">Conway's Game of Life → mapped to 3D shapes — single-file demo</div>

<script>
// ======= CONFIG & STATE =======
const canvas = document.getElementById('canvas');
const ctx = canvas.getContext('2d', { alpha: false });
let W = window.innerWidth, H = window.innerHeight;
canvas.width = W; canvas.height = H;

const UI = {
  shapeSel: document.getElementById('shape'),
  gridSize: document.getElementById('gridSize'),
  wrap: document.getElementById('wrap'),
  startBtn: document.getElementById('start'),
  stepBtn: document.getElementById('step'),
  randomBtn: document.getElementById('random'),
  clearBtn: document.getElementById('clear'),
  speed: document.getElementById('speed'),
  speedVal: document.getElementById('speedVal')
};

let gridN = clampInt(parseInt(UI.gridSize.value), 8, 300);
let grid = new Uint8Array(gridN * gridN);
let grid2 = new Uint8Array(gridN * gridN);
let running = false;
let wrap = UI.wrap.checked;
let speed = parseInt(UI.speed.value); // ticks per second
let lastTick = performance.now();
let accum = 0;

// camera / projection
let cam = {
  dist: 3.2,     // distance from origin
  fov: 800,      // perspective scale
  yaw: Math.PI*0.15,
  pitch: -0.35
};

// interaction
let dragging = false, lastX=0, lastY=0;
let scale = 1.0;

// precomputed sample positions (3D) and projected positions cached per frame
let samplePositions = []; // length gridN*gridN of {x,y,z, u,v, i,j}
let projCache = new Array(0); // per frame [ {x2,y2,screenZ,size} ]

// drawing styles
const bg = '#0b0b0f';
const aliveColor = [ 180, 255, 180 ]; // RGB for alive cells
const deadColor = [ 25, 30, 40 ];
const halo = 0.45; // for glow

// ======= UTIL =======
function clampInt(v,a,b){ v=Math.floor(v); if(isNaN(v)) v=a; return Math.max(a, Math.min(b, v)); }
function idx(i,j){ return i + j*gridN; }

// linear neighbor offsets
const neigh = [
  [-1,-1],[0,-1],[1,-1],
  [-1, 0],       [1, 0],
  [-1, 1],[0, 1],[1, 1]
];

// ======= SHAPE MAPPINGS =======
/*
 Each mapping receives normalized coordinates:
  u in [0,1) across columns (i)
  v in [0,1) across rows    (j)
 Return {x,y,z} in roughly unit-scale centered around origin
*/

function mapPlane(u,v){
  // flat plane in XY
  const aspect = 1.0;
  const x = (u - 0.5) * 2 * aspect;
  const y = (v - 0.5) * -2; // invert to match grid top->+y
  const z = 0;
  return {x,y,z};
}

function mapSphere(u,v){
  // latitude-longitude mapping (avoid poles degenerate by offsetting v slightly)
  const lon = (u - 0.5) * Math.PI * 2;         // -pi..pi
  const lat = (v - 0.5) * Math.PI;             // -pi/2..pi/2
  const r = 1.0;
  const x = r * Math.cos(lat) * Math.cos(lon);
  const y = r * Math.sin(lat);
  const z = r * Math.cos(lat) * Math.sin(lon);
  return {x,y,z};
}

function mapCylinder(u,v){
  // vertical cylinder, open top-bottom
  const theta = u * Math.PI * 2;
  const h = (v - 0.5) * 2;
  const r = 1.0;
  const x = r * Math.cos(theta);
  const y = h;
  const z = r * Math.sin(theta);
  return {x,y,z};
}

function mapTorus(u,v){
  // torus major radius R, minor r
  const R = 1.0;
  const r = 0.4;
  const uAngle = u * Math.PI * 2;
  const vAngle = v * Math.PI * 2;
  const x = (R + r * Math.cos(vAngle)) * Math.cos(uAngle);
  const y = r * Math.sin(vAngle);
  const z = (R + r * Math.cos(vAngle)) * Math.sin(uAngle);
  return {x,y,z};
}

function mapMoebius(u,v){
  // a Möbius-like strip parameterization (single sided). We'll map v in [-0.5,0.5]
  const U = u * Math.PI * 2; // 0..2pi
  const V = (v - 0.5) * 2;   // -1..1
  const R = 1.0;
  const x = (R + V * Math.cos(U / 2)) * Math.cos(U);
  const y = (R + V * Math.cos(U / 2)) * Math.sin(U);
  const z = V * Math.sin(U / 2);
  return {x,y,z};
}

function pickMapping(name){
  switch(name){
    case 'plane': return mapPlane;
    case 'sphere': return mapSphere;
    case 'cylinder': return mapCylinder;
    case 'torus': return mapTorus;
    case 'moebius': return mapMoebius;
    default: return mapSphere;
  }
}

// ======= GRID & STATE =======
function allocateGrid(n){
  gridN = clampInt(n, 8, 300);
  grid = new Uint8Array(gridN * gridN);
  grid2 = new Uint8Array(gridN * gridN);
  computeSamplePositions();
}

function computeSamplePositions(){
  samplePositions = new Array(gridN * gridN);
  const mapper = pickMapping(UI.shapeSel.value);
  for(let j=0;j<gridN;j++){
    for(let i=0;i<gridN;i++){
      const u = (i + 0.5) / gridN;
      const v = (j + 0.5) / gridN;
      const p = mapper(u,v);
      samplePositions[idx(i,j)] = { x:p.x, y:p.y, z:p.z, u, v, i, j };
    }
  }
  projCache = new Array(gridN * gridN);
}

// randomize and clear
function randomize(p=0.25){
  for(let k=0;k<grid.length;k++) grid[k] = Math.random() < p ? 1 : 0;
}
function clearGrid(){ grid.fill(0); }

// Game of Life step
function stepLife(){
  const wrapOn = wrap;
  const N = gridN;
  // note: iterate j rows, i cols
  for(let j=0;j<N;j++){
    for(let i=0;i<N;i++){
      let ncount = 0;
      for(const d of neigh){
        let ni = i + d[0], nj = j + d[1];
        if(wrapOn){
          ni = (ni + N) % N; nj = (nj + N) % N;
        } else {
          if(ni < 0 || ni >= N || nj < 0 || nj >= N) continue;
        }
        ncount += grid[idx(ni,nj)];
      }
      const cur = grid[idx(i,j)];
      let nx = 0;
      if(cur === 1){
        nx = (ncount === 2 || ncount === 3) ? 1 : 0;
      } else {
        nx = (ncount === 3) ? 1 : 0;
      }
      grid2[idx(i,j)] = nx;
    }
  }
  // swap
  const tmp = grid; grid = grid2; grid2 = tmp;
}

// ======= RENDERING (projection & painter's algorithm) =======
function projectAndSort(){
  // compute rotation matrix from cam.yaw & cam.pitch
  const cy = Math.cos(cam.yaw), sy = Math.sin(cam.yaw);
  const cp = Math.cos(cam.pitch), sp = Math.sin(cam.pitch);
  // rotation: first yaw around Y, then pitch around X (camera orbit)
  // rotation of point p: p' = R_x(pitch) * R_y(yaw) * p
  const N = gridN;
  for(let k=0;k<N*N;k++){
    const s = samplePositions[k];
    if(!s) continue;
    let x = s.x, y = s.y, z = s.z;
    // yaw (around world Y)
    let rx =  cy * x + sy * z;
    let rz = -sy * x + cy * z;
    // pitch (around world X)
    let ry = cp * y - sp * rz;
    rz = sp * y + cp * rz;
    // translate camera by -cam.dist along z
    const dz = rz - cam.dist;
    const px = (rx * cam.fov) / (dz || 0.0001) * scale + W/2;
    const py = (ry * cam.fov) / (dz || 0.0001) * scale + H/2;
    // size & depth cue (farther => smaller)
    const screenZ = dz;
    projCache[k] = { x: px, y: py, z: screenZ, depth: dz };
  }
  // create an index array sorted by depth (painter: draw farther first)
  const order = new Array(N*N);
  for(let i=0;i<order.length;i++) order[i] = i;
  // sort by depth descending (big dz = behind => draw first)
  order.sort((a,b)=> projCache[b].depth - projCache[a].depth);
  return order;
}

function render(){
  ctx.fillStyle = bg;
  ctx.fillRect(0,0,W,H);
  const order = projectAndSort();
  // draw each cell as circle; size based on local projected scale and grid density
  const baseSize = Math.min(W,H) / Math.max(120, gridN) * 0.9;
  for(let t=0;t<order.length;t++){
    const k = order[t];
    const p = projCache[k];
    const sp = samplePositions[k];
    const alive = grid[k] === 1;
    // skip if off-screen (small optimization)
    if(p.x < -50 || p.x > W+50 || p.y < -50 || p.y > H+50) continue;
    // depth factor: nearer (smaller z) -> larger. Use clamp
    const df = 1 / Math.max(0.2, Math.abs(p.depth) + 0.2);
    const size = baseSize * (0.6 + df * 1.8);
    // color: alive => bright greenish, dead => dark bluish
    if(alive){
      // compute brightness based on depth and local "lighting" (fake)
      const bright = Math.min(1, 0.6 + df * 0.9);
      const r = Math.floor(aliveColor[0] * bright);
      const g = Math.floor(aliveColor[1] * bright);
      const b = Math.floor(aliveColor[2] * bright);
      // glow circle
      const grad = ctx.createRadialGradient(p.x, p.y, 0, p.x, p.y, size*2.2);
      grad.addColorStop(0, `rgba(${r},${g},${b},${0.28})`);
      grad.addColorStop(0.5, `rgba(${r},${g},${b},${0.12})`);
      grad.addColorStop(1, `rgba(0,0,0,0)`);
      ctx.fillStyle = grad;
      ctx.beginPath(); ctx.arc(p.x, p.y, size*1.8, 0, Math.PI*2); ctx.fill();
      // core
      ctx.fillStyle = `rgb(${r},${g},${b})`;
      ctx.beginPath(); ctx.arc(p.x, p.y, size*0.85, 0, Math.PI*2); ctx.fill();
    } else {
      // subtle dead dot
      const darkness = Math.max(0.08, 0.03 + df*0.04);
      const r = Math.floor(deadColor[0] + (0 - deadColor[0]) * darkness);
      const g = Math.floor(deadColor[1] + (0 - deadColor[1]) * darkness);
      const b = Math.floor(deadColor[2] + (0 - deadColor[2]) * darkness);
      ctx.fillStyle = `rgb(${r},${g},${b})`;
      ctx.beginPath(); ctx.arc(p.x, p.y, size*0.32, 0, Math.PI*2); ctx.fill();
    }
  }
}

// ======= ANIMATION LOOP =======
function frame(now){
  // handle ticking
  const spd = Math.max(1, Math.min(120, parseInt(UI.speed.value)));
  UI.speedVal.innerText = spd;
  speed = spd;
  if(running){
    const dt = now - lastTick;
    const interval = 1000 / speed;
    accum += dt;
    while(accum >= interval){
      stepLife();
      accum -= interval;
    }
    lastTick = now;
  } else {
    lastTick = now;
  }

  render();
  requestAnimationFrame(frame);
}

// ======= EVENT HANDLERS =======
window.addEventListener('resize', ()=>{
  W = window.innerWidth; H = window.innerHeight;
  canvas.width = W; canvas.height = H;
  computeSamplePositions();
});

canvas.addEventListener('pointerdown', (ev)=>{
  dragging = true;
  lastX = ev.clientX; lastY = ev.clientY;
  canvas.setPointerCapture(ev.pointerId);
});

canvas.addEventListener('pointermove', (ev)=>{
  if(dragging && ev.buttons === 1){
    const dx = (ev.clientX - lastX) * 0.01;
    const dy = (ev.clientY - lastY) * 0.01;
    lastX = ev.clientX; lastY = ev.clientY;
    // If Ctrl/Alt is held, treat as click-toggle mode (don't rotate)
    if(ev.ctrlKey || ev.altKey) return;
    cam.yaw += dx;
    cam.pitch = Math.max(-Math.PI/2 + 0.01, Math.min(Math.PI/2 - 0.01, cam.pitch + dy));
  }
});

canvas.addEventListener('pointerup', (ev)=>{
  // detect click toggles (small movement)
  canvas.releasePointerCapture(ev.pointerId);
  dragging = false;
  // if it was a click (not a drag), toggle nearest cell
  if(Math.hypot(ev.clientX - lastX, ev.clientY - lastY) < 6){
    pickAndToggle(ev.clientX, ev.clientY);
  }
});

canvas.addEventListener('wheel', (ev)=>{
  ev.preventDefault();
  const delta = ev.deltaY > 0 ? 1.08 : 0.92;
  cam.dist *= delta;
  cam.dist = Math.max(0.6, Math.min(12, cam.dist));
  scale *= (ev.deltaY > 0 ? 0.98 : 1.02);
  scale = Math.max(0.2, Math.min(2.8, scale));
}, { passive: false });

// pick nearest projected cell and toggle it
function pickAndToggle(mx,my){
  // we already have projCache from last render; but ensure computing it
  projectAndSort();
  let best = null, bestD = Infinity;
  for(let k=0;k<projCache.length;k++){
    const p = projCache[k];
    if(!p) continue;
    const dx = p.x - mx, dy = p.y - my;
    const d = dx*dx + dy*dy;
    if(d < bestD){
      bestD = d; best = k;
    }
  }
  if(best !== null && bestD < 4000){ // threshold (approx)
    grid[best] = grid[best] ? 0 : 1;
  }
}

// UI
UI.startBtn.addEventListener('click', ()=>{
  running = !running;
  UI.startBtn.textContent = running ? 'Pause' : 'Start';
});
UI.stepBtn.addEventListener('click', ()=>{ if(!running) stepLife(); });
UI.randomBtn.addEventListener('click', ()=>{ randomize(0.28); });
UI.clearBtn.addEventListener('click', ()=>{ clearGrid(); });
UI.shapeSel.addEventListener('change', ()=>{
  computeSamplePositions();
});
UI.gridSize.addEventListener('change', ()=>{
  const v = clampInt(parseInt(UI.gridSize.value), 8, 300);
  allocateGrid(v);
});
UI.wrap.addEventListener('change', ()=>{ wrap = UI.wrap.checked; });

// ======= START =======
allocateGrid(gridN);
randomize(0.18);
lastTick = performance.now();
requestAnimationFrame(frame);
</script>
</body>
</html>
