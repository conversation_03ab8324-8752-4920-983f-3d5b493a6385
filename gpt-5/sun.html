<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<title>太阳系模拟</title>
<style>
html,body {
    margin:0;
    padding:0;
    overflow:hidden;
    background:black;
    color:white;
    font-family:sans-serif;
}
#canvas {
    display:block;
    background:black;
}
#controls {
    position:absolute;
    top:10px;
    left:10px;
    background:rgba(0,0,0,0.5);
    padding:8px;
    border-radius:5px;
    font-size:14px;
}
#infoPanel {
    position:absolute;
    bottom:10px;
    left:10px;
    background:rgba(0,0,0,0.7);
    padding:10px;
    border-radius:5px;
    max-width:250px;
    display:none;
}
</style>
</head>
<body>
<canvas id="canvas"></canvas>
<div id="controls">
  <label><input type="checkbox" id="toggleOrbits" checked> 显示轨道</label>
</div>
<div id="infoPanel"></div>
<script>
// 获取画布和上下文
const canvas = document.getElementById('canvas');
const ctx = canvas.getContext('2d');
let width = window.innerWidth;
let height = window.innerHeight;
canvas.width = width;
canvas.height = height;

// 缩放与拖拽
let scale = 1;
let offsetX = 0;
let offsetY = 0;
let isDragging = false;
let dragStartX, dragStartY;

canvas.addEventListener('mousedown', e=>{
    isDragging = true;
    dragStartX = e.clientX - offsetX;
    dragStartY = e.clientY - offsetY;
});
canvas.addEventListener('mouseup', e=>{
    isDragging = false;
});
canvas.addEventListener('mouseout', e=>{
    isDragging = false;
});
canvas.addEventListener('mousemove', e=>{
    if(isDragging){
        offsetX = e.clientX - dragStartX;
        offsetY = e.clientY - dragStartY;
    }
});
canvas.addEventListener('wheel', e=>{
    e.preventDefault();
    let zoom = Math.exp(e.deltaY * -0.001);
    scale *= zoom;
});

// 轨道开关
const toggleOrbits = document.getElementById('toggleOrbits');
let showOrbits = toggleOrbits.checked;
toggleOrbits.addEventListener('change', ()=>{
    showOrbits = toggleOrbits.checked;
});

// 信息面板
const infoPanel = document.getElementById('infoPanel');
function showInfo(planet){
    infoPanel.style.display = 'block';
    infoPanel.innerHTML = `<strong>${planet.name}</strong><br>${planet.info}`;
}
function hideInfo(){
    infoPanel.style.display = 'none';
}

// 背景星空
let stars = [];
for(let i=0;i<500;i++){
    stars.push({
        x: Math.random()*2000-1000,
        y: Math.random()*2000-1000,
        r: Math.random()*1.5
    });
}

// 定义天体
class Body {
    constructor(name, radius, distance, color, speed, info){
        this.name = name;
        this.radius = radius;
        this.distance = distance;
        this.color = color;
        this.speed = speed;
        this.angle = Math.random()*Math.PI*2;
        this.info = info || '';
        this.satellites = [];
    }
    update(dt){
        this.angle += this.speed*dt;
        this.satellites.forEach(s=>s.update(dt));
    }
    draw(cx, cy){
        let x = cx + Math.cos(this.angle)*this.distance;
        let y = cy + Math.sin(this.angle)*this.distance;
        // 画轨道
        if(showOrbits && this.distance>0){
            ctx.beginPath();
            ctx.strokeStyle = 'rgba(255,255,255,0.2)';
            ctx.arc(cx, cy, this.distance, 0, Math.PI*2);
            ctx.stroke();
        }
        // 行星
        ctx.beginPath();
        ctx.fillStyle = this.color;
        ctx.arc(x, y, this.radius, 0, Math.PI*2);
        ctx.fill();

        // 卫星
        this.satellites.forEach(s=>{
            s.draw(x, y);
        });

        this.screenX = x;
        this.screenY = y;
    }
}

// 数据（距离与速度非真实比例，方便显示）
let sun = new Body('太阳', 20, 0, 'yellow', 0, '太阳是太阳系的中心恒星，提供光和热。');

let mercury = new Body('水星', 4, 40, 'gray', 0.02, '水星是离太阳最近的行星。');
let venus   = new Body('金星', 6, 60, 'goldenrod', 0.015, '金星是太阳系中温度最高的行星。');
let earth   = new Body('地球', 6, 80, 'blue', 0.01, '地球是人类的家园。');
let moon    = new Body('月球', 2, 10, 'lightgray', 0.05, '月球是地球的唯一天然卫星。');
earth.satellites.push(moon);

let mars    = new Body('火星', 5, 100, 'red', 0.008, '火星被称为红色星球。');

let jupiter = new Body('木星', 12, 140, 'orange', 0.004, '木星是太阳系中最大的行星。');
let io      = new Body('木卫一', 2, 15, 'yellow', 0.07, '木卫一是木星的伽利略卫星之一。');
let europa  = new Body('木卫二', 2, 20, 'white', 0.05, '木卫二有冰层覆盖的表面。');
jupiter.satellites.push(io, europa);

let saturn  = new Body('土星', 10, 180, 'khaki', 0.003, '土星以其美丽的光环著称。');
let titan   = new Body('土卫六', 2, 18, 'peru', 0.04, '土卫六是土星最大的卫星。');
saturn.satellites.push(titan);

let uranus  = new Body('天王星', 8, 220, 'lightblue', 0.002, '天王星自转轴几乎平躺在轨道平面上。');
let neptune = new Body('海王星', 8, 260, 'blueviolet', 0.001, '海王星是距离太阳最远的行星。');

// 小行星带
let asteroids = [];
for(let i=0;i<200;i++){
    let dist = 120 + Math.random()*20;
    let angle = Math.random()*Math.PI*2;
    let speed = 0.006 + Math.random()*0.002;
    asteroids.push({
        distance: dist,
        angle: angle,
        speed: speed,
        size: Math.random()*1.5+0.5
    });
}

let planets = [mercury, venus, earth, mars, jupiter, saturn, uranus, neptune];

// 点击检测
canvas.addEventListener('click', e=>{
    let mx = (e.clientX - offsetX - width/2)/scale;
    let my = (e.clientY - offsetY - height/2)/scale;
    let found = null;
    [sun,...planets].forEach(p=>{
        let dx = mx - Math.cos(p.angle)*p.distance;
        let dy = my - Math.sin(p.angle)*p.distance;
        if(Math.sqrt(dx*dx+dy*dy) < p.radius+2){
            found = p;
        }
        p.satellites.forEach(s=>{
            let sx = Math.cos(p.angle)*p.distance + Math.cos(s.angle)*s.distance;
            let sy = Math.sin(p.angle)*p.distance + Math.sin(s.angle)*s.distance;
            let dx2 = mx - sx;
            let dy2 = my - sy;
            if(Math.sqrt(dx2*dx2+dy2*dy2) < s.radius+2){
                found = s;
            }
        });
    });
    if(found){
        showInfo(found);
    } else {
        hideInfo();
    }
});

// 动画
let lastTime = performance.now();
function animate(time){
    let dt = (time - lastTime)/16; // 以 60fps 基准
    lastTime = time;

    ctx.setTransform(1,0,0,1,0,0);
    ctx.clearRect(0,0,width,height);

    // 背景
    ctx.fillStyle = 'black';
    ctx.fillRect(0,0,width,height);
    ctx.save();
    ctx.translate(width/2+offsetX, height/2+offsetY);
    ctx.scale(scale, scale);

    // 星星
    ctx.fillStyle = 'white';
    stars.forEach(s=>{
        ctx.beginPath();
        ctx.arc(s.x, s.y, s.r, 0, Math.PI*2);
        ctx.fill();
    });

    // 太阳脉动
    let pulse = 20 + Math.sin(time/500)*2;
    ctx.beginPath();
    let grad = ctx.createRadialGradient(0,0,0,0,0,pulse*3);
    grad.addColorStop(0,'yellow');
    grad.addColorStop(1,'rgba(255,165,0,0)');
    ctx.fillStyle = grad;
    ctx.arc(0,0,pulse*3,0,Math.PI*2);
    ctx.fill();

    ctx.beginPath();
    ctx.fillStyle = 'yellow';
    ctx.arc(0,0,pulse,0,Math.PI*2);
    ctx.fill();

    sun.screenX = 0;
    sun.screenY = 0;

    // 更新 & 绘制行星
    planets.forEach(p=>p.update(dt));
    planets.forEach(p=>p.draw(0,0));

    // 小行星带
    ctx.fillStyle = 'gray';
    asteroids.forEach(a=>{
        a.angle += a.speed*dt;
        let ax = Math.cos(a.angle)*a.distance;
        let ay = Math.sin(a.angle)*a.distance;
        ctx.beginPath();
        ctx.arc(ax, ay, a.size, 0, Math.PI*2);
        ctx.fill();
    });

    ctx.restore();
    requestAnimationFrame(animate);
}
animate(lastTime);

window.addEventListener('resize', ()=>{
    width = window.innerWidth;
    height = window.innerHeight;
    canvas.width = width;
    canvas.height = height;
});
</script>
</body>
</html>
