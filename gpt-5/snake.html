<!DOCTYPE html>
<html lang="zh">
<head>
<meta charset="UTF-8">
<title>贪吃蛇游戏</title>
<style>
    body {
        background: #222;
        color: white;
        font-family: Arial, sans-serif;
        text-align: center;
        margin-top: 20px;
    }
    canvas {
        background: #111;
        display: block;
        margin: 20px auto;
        border: 2px solid #555;
    }
    #score {
        font-size: 20px;
    }
</style>
</head>
<body>
<h1>贪吃蛇游戏</h1>
<div id="score">分数: 0</div>
<canvas id="gameCanvas" width="400" height="400"></canvas>

<script>
const canvas = document.getElementById("gameCanvas");
const ctx = canvas.getContext("2d");
const box = 20; // 格子大小
let score = 0;

// 蛇的初始位置
let snake = [];
snake[0] = { x: 9 * box, y: 9 * box };

// 食物类型
const FOOD_TYPES = [
    { color: "green", effect: "normal" }, // 普通
    { color: "red", effect: "dead" },     // 死亡
    { color: "yellow", effect: "double" } // 加倍
];

// 生成随机食物
function generateFood() {
    const type = FOOD_TYPES[Math.floor(Math.random() * FOOD_TYPES.length)];
    return {
        x: Math.floor(Math.random() * (canvas.width / box)) * box,
        y: Math.floor(Math.random() * (canvas.height / box)) * box,
        color: type.color,
        effect: type.effect
    };
}

// 初始多个食物
let foods = [];
for (let i = 0; i < 3; i++) {
    foods.push(generateFood());
}

// 方向控制
let d;
document.addEventListener("keydown", direction);
function direction(event) {
    if (event.key === "ArrowLeft" && d !== "RIGHT") d = "LEFT";
    else if (event.key === "ArrowUp" && d !== "DOWN") d = "UP";
    else if (event.key === "ArrowRight" && d !== "LEFT") d = "RIGHT";
    else if (event.key === "ArrowDown" && d !== "UP") d = "DOWN";
}

// 碰撞检测
function collision(head, array) {
    for (let i = 0; i < array.length; i++) {
        if (head.x === array[i].x && head.y === array[i].y) {
            return true;
        }
    }
    return false;
}

// 游戏主循环
function draw() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 绘制蛇
    for (let i = 0; i < snake.length; i++) {
        ctx.fillStyle = i === 0 ? "lime" : "white";
        ctx.fillRect(snake[i].x, snake[i].y, box, box);
    }

    // 绘制食物
    foods.forEach(food => {
        ctx.fillStyle = food.color;
        ctx.fillRect(food.x, food.y, box, box);
    });

    // 蛇头位置
    let snakeX = snake[0].x;
    let snakeY = snake[0].y;

    if (d === "LEFT") snakeX -= box;
    if (d === "UP") snakeY -= box;
    if (d === "RIGHT") snakeX += box;
    if (d === "DOWN") snakeY += box;

    let newHead = { x: snakeX, y: snakeY };

    // 检测是否吃到食物
    let ateFood = false;
    foods = foods.map(food => {
        if (snakeX === food.x && snakeY === food.y) {
            if (food.effect === "dead") {
                alert("你吃到了红色食物，游戏结束！");
                clearInterval(game);
            } else if (food.effect === "double") {
                score += 5;
                // 长度加倍
                for (let i = 0; i < snake.length; i++) {
                    snake.push({ ...snake[snake.length - 1] });
                }
                ateFood = true;
            } else {
                score += 1;
                ateFood = true;
            }
            return generateFood();
        }
        return food;
    });

    if (!ateFood) {
        snake.pop(); // 没吃到食物就移动
    }

    // 游戏结束检测（撞墙或撞自己）
    if (
        snakeX < 0 || snakeX >= canvas.width ||
        snakeY < 0 || snakeY >= canvas.height ||
        collision(newHead, snake)
    ) {
        alert("游戏结束！");
        clearInterval(game);
    }

    snake.unshift(newHead);

    document.getElementById("score").textContent = "分数: " + score;
}

let game = setInterval(draw, 150);
</script>
</body>
</html>
