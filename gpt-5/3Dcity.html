<!DOCTYPE html>
<html lang="zh">
<head>
<meta charset="UTF-8">
<title>Three.js 城市场景</title>
<style>
    body { margin: 0; overflow: hidden; }
    canvas { display: block; }
</style>
</head>
<body>
<script src="https://cdn.jsdelivr.net/npm/three@0.159.0/build/three.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/three@0.159.0/examples/js/controls/OrbitControls.js"></script>
<script>
let scene, camera, renderer, controls;
let cars = [], pedestrians = [];
let sunLight, ambientLight;
let clock = new THREE.Clock();

init();
animate();

function init(){
    scene = new THREE.Scene();
    scene.background = new THREE.Color(0xa0d0ff);

    camera = new THREE.PerspectiveCamera(60, window.innerWidth/window.innerHeight, 0.1, 1000);
    camera.position.set(50, 50, 50);

    renderer = new THREE.WebGLRenderer({antialias:true});
    renderer.setSize(window.innerWidth, window.innerHeight);
    document.body.appendChild(renderer.domElement);

    controls = new THREE.OrbitControls(camera, renderer.domElement);

    // 环境光
    ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    scene.add(ambientLight);

    // 太阳光（模拟昼夜）
    sunLight = new THREE.DirectionalLight(0xffffff, 1);
    sunLight.position.set(50, 100, 50);
    scene.add(sunLight);

    // 地面
    const ground = new THREE.Mesh(
        new THREE.PlaneGeometry(200, 200),
        new THREE.MeshStandardMaterial({color: 0x555555})
    );
    ground.rotation.x = -Math.PI/2;
    scene.add(ground);

    // 添加道路
    createRoad();

    // 创建建筑
    createBuildings();

    // 创建街道元素
    createStreetElements();

    // 创建汽车
    createCars();

    // 创建行人
    createPedestrians();

    window.addEventListener('resize', onWindowResize);
}

function createRoad(){
    const roadMaterial = new THREE.MeshStandardMaterial({color: 0x333333});
    const road = new THREE.Mesh(new THREE.BoxGeometry(200, 0.1, 10), roadMaterial);
    road.position.y = 0.05;
    scene.add(road);

    // 人行横道
    const stripeMaterial = new THREE.MeshStandardMaterial({color: 0xffffff});
    for(let i= -5; i<=5; i++){
        const stripe = new THREE.Mesh(new THREE.BoxGeometry(1, 0.01, 0.5), stripeMaterial);
        stripe.position.set(i*2, 0.06, 0);
        scene.add(stripe);
    }

    // 交通灯
    const pole = new THREE.Mesh(new THREE.CylinderGeometry(0.1,0.1,5), new THREE.MeshStandardMaterial({color:0x000000}));
    pole.position.set(-5,2.5,5);
    scene.add(pole);

    const lightBox = new THREE.Mesh(new THREE.BoxGeometry(0.5,1.5,0.5), new THREE.MeshStandardMaterial({color:0x222222}));
    lightBox.position.set(-5,4,5);
    scene.add(lightBox);

    const redLight = new THREE.Mesh(new THREE.SphereGeometry(0.2), new THREE.MeshStandardMaterial({color:0xff0000}));
    redLight.position.set(-5,4.5,5.3);
    scene.add(redLight);

    const greenLight = new THREE.Mesh(new THREE.SphereGeometry(0.2), new THREE.MeshStandardMaterial({color:0x00ff00}));
    greenLight.position.set(-5,3.5,5.3);
    scene.add(greenLight);
}

function createBuildings(){
    const colors = [0xa0a0a0, 0x8888ff, 0xff8888, 0x88ff88];
    for(let i=0; i<20; i++){
        const width = THREE.MathUtils.randInt(4, 8);
        const height = THREE.MathUtils.randInt(10, 30);
        const depth = THREE.MathUtils.randInt(4, 8);
        const building = new THREE.Mesh(
            new THREE.BoxGeometry(width, height, depth),
            new THREE.MeshStandardMaterial({color: colors[Math.floor(Math.random()*colors.length)]})
        );
        building.position.set(
            THREE.MathUtils.randInt(-80, 80),
            height/2,
            THREE.MathUtils.randInt(-80, 80)
        );
        scene.add(building);
    }
}

function createStreetElements(){
    // 路灯
    for(let i=-80; i<=80; i+=20){
        const pole = new THREE.Mesh(new THREE.CylinderGeometry(0.2,0.2,6), new THREE.MeshStandardMaterial({color:0x555555}));
        pole.position.set(i,3, -5);
        scene.add(pole);

        const lamp = new THREE.Mesh(new THREE.SphereGeometry(0.5), new THREE.MeshStandardMaterial({emissive:0xffffaa, emissiveIntensity:0.5}));
        lamp.position.set(i,6, -5);
        scene.add(lamp);
    }

    // 树木
    for(let i=-80; i<=80; i+=15){
        const trunk = new THREE.Mesh(new THREE.CylinderGeometry(0.5,0.5,3), new THREE.MeshStandardMaterial({color:0x8b4513}));
        trunk.position.set(i,1.5, 8);
        scene.add(trunk);

        const foliage = new THREE.Mesh(new THREE.SphereGeometry(2), new THREE.MeshStandardMaterial({color:0x228B22}));
        foliage.position.set(i,4.5, 8);
        scene.add(foliage);
    }
}

function createCars(){
    const carMaterial = new THREE.MeshStandardMaterial({color:0xff0000});
    for(let i=0; i<5; i++){
        const car = new THREE.Mesh(new THREE.BoxGeometry(2,1,1), carMaterial.clone());
        car.material.color.setHex(Math.random()*0xffffff);
        car.position.set(-80 + i*15, 0.5, 0);
        scene.add(car);
        cars.push(car);
    }
}

function createPedestrians(){
    const pedMaterial = new THREE.MeshStandardMaterial({color:0x00ff00});
    for(let i=0; i<5; i++){
        const ped = new THREE.Mesh(new THREE.BoxGeometry(0.5,1.8,0.5), pedMaterial.clone());
        ped.material.color.setHex(Math.random()*0xffffff);
        ped.position.set(THREE.MathUtils.randFloat(-10, 10), 0.9, THREE.MathUtils.randFloat(-5, 5));
        scene.add(ped);
        pedestrians.push(ped);
    }
}

function animate(){
    requestAnimationFrame(animate);
    const delta = clock.getDelta();

    // 汽车移动
    cars.forEach(car=>{
        car.position.x += delta * 10;
        if(car.position.x > 100) car.position.x = -100;
    });

    // 行人移动
    pedestrians.forEach(ped=>{
        ped.position.z += delta * 2;
        if(ped.position.z > 5) ped.position.z = -5;
    });

    // 昼夜循环
    const elapsed = clock.getElapsedTime();
    const dayLength = 20; // 20秒一个循环
    const angle = (elapsed / dayLength) * Math.PI * 2;
    sunLight.position.set(Math.cos(angle)*50, Math.sin(angle)*100, 50);
    sunLight.intensity = Math.max(0, Math.sin(angle));
    ambientLight.intensity = 0.3 + 0.7 * Math.max(0, Math.sin(angle));
    scene.background = new THREE.Color().setHSL(0.6, 0.5, 0.5 + 0.5*Math.max(0, Math.sin(angle)));

    controls.update();
    renderer.render(scene, camera);
}

function onWindowResize(){
    camera.aspect = window.innerWidth/window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
}
</script>
</body>
</html>
