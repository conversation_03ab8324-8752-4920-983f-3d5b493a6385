<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <title>Interactive Rubik's Cube (Three.js) — up to 20×20×20</title>
  <style>
    html,body { height:100%; margin:0; background:#111; color:#ddd; font-family:system-ui,Segoe UI,Roboto,Helvetica,Arial; }
    #ui { position: absolute; left: 12px; top: 12px; z-index: 20; background: rgba(20,20,20,0.8); padding: 12px; border-radius:8px; box-shadow: 0 6px 18px rgba(0,0,0,0.6); }
    #ui input[type=number] { width:64px; }
    #ui button, #ui input, #ui label { margin: 6px 6px 0 0; }
    #canvas-container { width:100%; height:100%; display:block; }
    .small { font-size:12px; color:#aaa; }
    .control-row { margin-bottom:8px; }
    #footer { position: absolute; right: 12px; bottom: 12px; color:#999; font-size:12px; }
  </style>
</head>
<body>
  <div id="ui">
    <div class="control-row">
      <label>Size (n × n × n): </label>
      <input id="sizeInput" type="number" min="1" max="20" value="3" />
      <button id="buildBtn">Build</button>
    </div>

    <div class="control-row">
      <button id="scrambleBtn">Scramble</button>
      <button id="resetBtn">Reset</button>
      <button id="solveBtn">Solve</button>
    </div>

    <div class="control-row small">
      <label>Animation speed:</label>
      <input id="speed" type="range" min="0.2" max="2.5" step="0.1" value="1" />
      <span id="speedVal">1.0x</span>
    </div>

    <div class="control-row small">
      <label>Manual rotate layer:</label>
      <select id="axisSelect">
        <option value="x">X</option>
        <option value="y" selected>Y</option>
        <option value="z">Z</option>
      </select>
      <input id="layerIndex" type="number" min="0" value="0" style="width:60px" />
      <button id="rotatePos">+90°</button>
      <button id="rotateNeg">-90°</button>
    </div>

    <div class="small">Instructions:</div>
    <div class="small" style="max-width:320px">
      Click a cubie face and drag to rotate a layer. Use Scramble to apply random moves, then Solve to reverse them step-by-step. For large sizes, allow extra time — performance depends on your machine.
    </div>
  </div>

  <div id="canvas-container"></div>
  <div id="footer">Three.js Rubik's Cube — drag to rotate camera, scroll to zoom</div>

  <script type="module">
    import * as THREE from 'https://unpkg.com/three@0.152.2/build/three.module.js';
    import { OrbitControls } from 'https://unpkg.com/three@0.152.2/examples/jsm/controls/OrbitControls.js';

    // ---------- Config & Globals ----------
    const container = document.getElementById('canvas-container');
    let scene, camera, renderer, controls, raycaster;
    let size = 3; // default
    const gap = 0.02; // gap between cubies
    const cubieSize = 1; // base cubie unit
    let cubies = []; // array of {mesh, coords: {x,y,z}}
    let cubeGroup = new THREE.Group();
    let animating = false;
    let animationQueue = []; // functions that return promises for each move
    let moveHistory = []; // record moves: {axis:'x'|'y'|'z', layer:int, dir:1|-1}
    let animSpeed = 1.0; // multiplier
    let isMouseDown = false;
    let pickInfo = null; // on mousedown
    const MAX_SIZE = 20;

    // Colors standard mapping:
    // We'll map: +Y = white (Up), -Y = yellow (Down)
    // +Z = red (Front), -Z = orange (Back)
    // +X = blue (Right), -X = green (Left)
    const FACE_COLORS = {
      up: 0xffffff,    // white
      down: 0xFFFF00,  // yellow
      front: 0xff0000, // red
      back: 0xffa500,  // orange
      right: 0x0000ff, // blue
      left: 0x00a000   // green
    };

    // ---------- Init scene ----------
    function init() {
      scene = new THREE.Scene();
      scene.background = new THREE.Color(0x0b0b0b);

      const w = container.clientWidth || window.innerWidth;
      const h = container.clientHeight || window.innerHeight;

      camera = new THREE.PerspectiveCamera(50, w / h, 0.1, 2000);
      camera.position.set(4, 4, 6);

      renderer = new THREE.WebGLRenderer({ antialias: true });
      renderer.setSize(w, h);
      renderer.setPixelRatio(window.devicePixelRatio ? Math.min(window.devicePixelRatio, 2) : 1);
      container.innerHTML = '';
      container.appendChild(renderer.domElement);

      controls = new OrbitControls(camera, renderer.domElement);
      controls.enableDamping = true;
      controls.dampingFactor = 0.08;
      controls.minDistance = 2;
      controls.maxDistance = 40;

      // lights
      const ambient = new THREE.AmbientLight(0xffffff, 0.5);
      scene.add(ambient);
      const dir = new THREE.DirectionalLight(0xffffff, 0.6);
      dir.position.set(5, 10, 7);
      scene.add(dir);

      raycaster = new THREE.Raycaster();

      window.addEventListener('resize', onWindowResize);
      renderer.domElement.addEventListener('pointerdown', onPointerDown);
      renderer.domElement.addEventListener('pointermove', onPointerMove);
      renderer.domElement.addEventListener('pointerup', onPointerUp);
    }

    function onWindowResize() {
      const w = container.clientWidth || window.innerWidth;
      const h = container.clientHeight || window.innerHeight;
      camera.aspect = w / h;
      camera.updateProjectionMatrix();
      renderer.setSize(w, h);
    }

    // ---------- Build cube ----------
    function clearCube() {
      cubies = [];
      moveHistory = [];
      animationQueue = [];
      animating = false;
      while (cubeGroup.children.length) cubeGroup.remove(cubeGroup.children[0]);
      scene.remove(cubeGroup);
      cubeGroup = new THREE.Group();
    }

    function buildCube(n = 3) {
      clearCube();
      size = Math.max(1, Math.min(MAX_SIZE, Math.floor(n)));
      const totalSize = size * cubieSize + (size - 1) * gap;
      const start = - (totalSize / 2) + cubieSize / 2;

      const geometry = new THREE.BoxGeometry(cubieSize, cubieSize, cubieSize);

      // We'll create materials per face using MeshStandardMaterial with colors or black
      for (let x = 0; x < size; x++) {
        for (let y = 0; y < size; y++) {
          for (let z = 0; z < size; z++) {
            // small offset for gaps
            const px = start + x * (cubieSize + gap);
            const py = start + y * (cubieSize + gap);
            const pz = start + z * (cubieSize + gap);

            // Create six face materials; default to dark (no sticker)
            const faceMaterials = [];
            // BoxGeometry face order: +X, -X, +Y, -Y, +Z, -Z (three.js standard for BoxGeometry)
            // We'll map colors based on cubie's position on outer layer
            // +X (right)
            faceMaterials.push(new THREE.MeshStandardMaterial({ color: (x === size - 1) ? FACE_COLORS.right : 0x111111 }));
            // -X (left)
            faceMaterials.push(new THREE.MeshStandardMaterial({ color: (x === 0) ? FACE_COLORS.left : 0x111111 }));
            // +Y (up)
            faceMaterials.push(new THREE.MeshStandardMaterial({ color: (y === size - 1) ? FACE_COLORS.up : 0x111111 }));
            // -Y (down)
            faceMaterials.push(new THREE.MeshStandardMaterial({ color: (y === 0) ? FACE_COLORS.down : 0x111111 }));
            // +Z (front)
            faceMaterials.push(new THREE.MeshStandardMaterial({ color: (z === size - 1) ? FACE_COLORS.front : 0x111111 }));
            // -Z (back)
            faceMaterials.push(new THREE.MeshStandardMaterial({ color: (z === 0) ? FACE_COLORS.back : 0x111111 }));

            const mesh = new THREE.Mesh(geometry, faceMaterials);
            mesh.position.set(px, py, pz);
            mesh.userData.coords = { x, y, z }; // integer cubie indices
            // For picking, we want each cubie to have unique id
            mesh.userData.uuid = THREE.MathUtils.generateUUID();
            cubeGroup.add(mesh);
            cubies.push({ mesh, coords: { x, y, z } });
          }
        }
      }

      scene.add(cubeGroup);
      // center cubeGroup is at 0,0,0
    }

    // ---------- Utility helpers ----------
    function getCubiesInLayer(axis, layerIndex) {
      // axis 'x'|'y'|'z'; layerIndex 0..size-1 (index along that axis)
      const out = [];
      for (const c of cubies) {
        const coords = c.mesh.userData.coords;
        if (coords[axis] === layerIndex) out.push(c.mesh);
      }
      return out;
    }

    // Round position and update coords after a completed rotation
    function commitCubieTransforms() {
      // After rotating group, we need to snap positions to grid,
      // update userData.coords for each cubie, and reparent them to cubeGroup.
      const eps = 1e-3;
      const totalSize = size * cubieSize + (size - 1) * gap;
      const start = - (totalSize / 2) + cubieSize / 2;
      for (const c of cubies) {
        // round position relative to start and step
        const p = c.mesh.position;
        // find nearest index along each axis
        const step = cubieSize + gap;
        const rx = Math.round((p.x - start) / step);
        const ry = Math.round((p.y - start) / step);
        const rz = Math.round((p.z - start) / step);
        // clamp
        const nx = Math.max(0, Math.min(size - 1, rx));
        const ny = Math.max(0, Math.min(size - 1, ry));
        const nz = Math.max(0, Math.min(size - 1, rz));
        c.mesh.userData.coords = { x: nx, y: ny, z: nz };

        // snap world position to grid
        const px = start + nx * step;
        const py = start + ny * step;
        const pz = start + nz * step;
        c.mesh.position.set(px, py, pz);

        // also round rotations to multiples of 90 degrees along each axis to prevent drift
        const rxDeg = Math.round(THREE.MathUtils.radToDeg(c.mesh.rotation.x) / 90) * 90;
        const ryDeg = Math.round(THREE.MathUtils.radToDeg(c.mesh.rotation.y) / 90) * 90;
        const rzDeg = Math.round(THREE.MathUtils.radToDeg(c.mesh.rotation.z) / 90) * 90;
        c.mesh.rotation.set(
          THREE.MathUtils.degToRad(rxDeg),
          THREE.MathUtils.degToRad(ryDeg),
          THREE.MathUtils.degToRad(rzDeg)
        );

        // reparent to cubeGroup (the children were temporarily parented to rotating group)
        if (c.mesh.parent !== cubeGroup) {
          cubeGroup.add(c.mesh);
        }
      }
    }

    // ---------- Rotation animation ----------
    function enqueueLayerRotation(axis, layerIndex, dir = 1) {
      // axis 'x' 'y' 'z', dir 1 => +90 deg (right-hand), -1 => -90 deg
      animationQueue.push(() => rotateLayer(axis, layerIndex, dir));
    }

    function rotateLayer(axis, layerIndex, dir = 1) {
      // returns promise that resolves when done
      if (animating) {
        // avoid double
      }
      animating = true;
      const group = new THREE.Group();
      scene.add(group);
      // gather meshes
      const selected = getCubiesInLayer(axis, layerIndex);
      for (const m of selected) {
        // convert to group-local coords
        THREE.SceneUtils?.detach?.(m, m.parent, scene); // safety if available
        group.add(m);
      }
      // move group to origin (cubeGroup already centered at 0)
      // compute rotation axis vector
      const axisVec = (axis === 'x') ? new THREE.Vector3(1, 0, 0) : (axis === 'y') ? new THREE.Vector3(0, 1, 0) : new THREE.Vector3(0, 0, 1);
      const targetAngle = (Math.PI / 2) * dir;
      const duration = 400 / animSpeed; // ms

      return new Promise(resolve => {
        const start = performance.now();
        function animateNow(t) {
          const elapsed = t - start;
          const p = Math.min(1, elapsed / duration);
          const eased = easeInOutCubic(p);
          group.rotation.set(0, 0, 0);
          group.rotateOnAxis(axisVec, eased * targetAngle);
          if (p < 1) {
            requestAnimationFrame(animateNow);
          } else {
            // finalize: apply rotation to children (by keeping their world transforms), then detach and reparent to cubeGroup
            // To keep world transform, compute matrix
            for (const child of [...group.children]) {
              child.applyMatrix4(group.matrix);
              cubeGroup.add(child);
            }
            scene.remove(group);
            // commit transforms (snap positions and rotations)
            commitCubieTransforms();
            animating = false;
            // record move
            moveHistory.push({ axis, layerIndex, dir });
            resolve();
          }
        }
        requestAnimationFrame(animateNow);
      });
    }

    function easeInOutCubic(t) {
      return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    }

    // ---------- Input & Interaction ----------
    function onPointerDown(event) {
      if (animating || animationQueue.length > 0) return; // disable while animating
      isMouseDown = true;
      const rect = renderer.domElement.getBoundingClientRect();
      const pointer = {
        x: ((event.clientX - rect.left) / rect.width) * 2 - 1,
        y: -((event.clientY - rect.top) / rect.height) * 2 + 1
      };
      raycaster.setFromCamera(pointer, camera);
      const intersects = raycaster.intersectObjects(cubeGroup.children, true);
      if (intersects.length > 0) {
        const it = intersects[0];
        const mesh = it.object;
        // face normal (in object local space)
        const faceNormal = it.face.normal.clone().applyMatrix3(new THREE.Matrix3().getNormalMatrix(mesh.matrixWorld)).normalize();
        pickInfo = {
          mesh,
          faceNormal,
          startScreen: { x: event.clientX, y: event.clientY },
          startPoint: it.point,
          pickedCoords: { ...mesh.userData.coords }
        };
      } else {
        pickInfo = null;
      }
    }

    function onPointerMove(event) {
      if (!isMouseDown || !pickInfo) return;
      // nothing too heavy; we only compute drag vector and visualize nothing
    }

    function onPointerUp(event) {
      if (!isMouseDown) return;
      isMouseDown = false;
      if (!pickInfo) return;
      const dx = event.clientX - pickInfo.startScreen.x;
      const dy = event.clientY - pickInfo.startScreen.y;
      const dragLen = Math.sqrt(dx * dx + dy * dy);
      if (dragLen < 6) {
        pickInfo = null;
        return; // too small; treat as click
      }

      // Decide rotation axis from face normal and drag direction.
      // We use camera-space drag vector and faceNormal camera-space to decide which cube axis to use.
      // Convert faceNormal to world (camera) space already in pickInfo.faceNormal.
      const cam = camera;
      // Project drag vector to camera plane axes (right, up)
      const camUp = new THREE.Vector3(0, 1, 0).applyQuaternion(cam.quaternion);
      const camRight = new THREE.Vector3(1, 0, 0).applyQuaternion(cam.quaternion);

      // Drag direction in world units:
      const dragWorld = camRight.clone().multiplyScalar(dx).add(camUp.clone().multiplyScalar(-dy)).normalize();

      // Candidate cube axes in world space:
      const xAxis = new THREE.Vector3(1, 0, 0); // cube-space
      const yAxis = new THREE.Vector3(0, 1, 0);
      const zAxis = new THREE.Vector3(0, 0, 1);
      // rotate axes by cubeGroup rotation (it may be identity since cubeGroup centered) - but cubeGroup is not rotated separately, we keep axes in world
      // Determine which axis is most perpendicular to faceNormal but most aligned with dragWorld -> to find rotation axis
      // For face normal n, valid rotation axes lie in plane perpendicular to n (i.e., dot(axis, n) ~ 0). Among those, pick axis with largest abs(dot(axis, dragWorld)).
      const axes = [
        { name: 'x', vec: xAxis.clone() },
        { name: 'y', vec: yAxis.clone() },
        { name: 'z', vec: zAxis.clone() },
      ];
      // For each, compute score: |dot(axis, dragWorld)| but require |dot(axis, faceNormal)| small
      let best = null;
      for (const a of axes) {
        const dotNF = Math.abs(a.vec.dot(pickInfo.faceNormal));
        const dotDrag = Math.abs(a.vec.dot(dragWorld));
        const score = (1 - Math.min(1, dotNF)) * dotDrag; // prioritize axis orthogonal to face normal
        if (!best || score > best.score) best = { axis: a.name, score, dotNF, dotDrag };
      }

      // decide direction of rotation (dir = 1 or -1)
      // sign = sign of dot(axis, dragWorld x faceNormal) — align with right-hand rule
      const axisVec = (best.axis === 'x') ? xAxis : (best.axis === 'y') ? yAxis : zAxis;
      const cross = new THREE.Vector3().crossVectors(dragWorld, pickInfo.faceNormal);
      const s = Math.sign(axisVec.dot(cross));
      const dir = s === 0 ? (dx > 0 ? 1 : -1) : s;

      // compute layer index from picked coords along axis
      const layerIndex = pickInfo.pickedCoords[best.axis];

      // enqueue rotation
      enqueueLayerRotation(best.axis, layerIndex, dir);
      processAnimationQueue();

      pickInfo = null;
    }

    async function processAnimationQueue() {
      if (animating) return;
      while (animationQueue.length > 0) {
        const fn = animationQueue.shift();
        await fn();
      }
    }

    // ---------- UI handlers ----------
    document.getElementById('buildBtn').addEventListener('click', () => {
      const n = parseInt(document.getElementById('sizeInput').value) || 3;
      buildCube(n);
    });

    document.getElementById('scrambleBtn').addEventListener('click', () => {
      if (animating || animationQueue.length) return;
      const moves = Math.max(5, Math.min(200, Math.floor(size * 5))); // a reasonable scramble count
      const axes = ['x', 'y', 'z'];
      for (let i = 0; i < moves; i++) {
        const axis = axes[Math.floor(Math.random() * axes.length)];
        const layer = Math.floor(Math.random() * size);
        const dir = Math.random() > 0.5 ? 1 : -1;
        enqueueLayerRotation(axis, layer, dir);
      }
      processAnimationQueue();
    });

    document.getElementById('resetBtn').addEventListener('click', () => {
      if (animating || animationQueue.length) return;
      buildCube(size);
    });

    document.getElementById('speed').addEventListener('input', (e) => {
      animSpeed = parseFloat(e.target.value);
      document.getElementById('speedVal').innerText = animSpeed.toFixed(1) + 'x';
    });

    document.getElementById('rotatePos').addEventListener('click', () => {
      const axis = document.getElementById('axisSelect').value;
      let idx = parseInt(document.getElementById('layerIndex').value);
      idx = clampLayer(idx);
      enqueueLayerRotation(axis, idx, 1);
      processAnimationQueue();
    });
    document.getElementById('rotateNeg').addEventListener('click', () => {
      const axis = document.getElementById('axisSelect').value;
      let idx = parseInt(document.getElementById('layerIndex').value);
      idx = clampLayer(idx);
      enqueueLayerRotation(axis, idx, -1);
      processAnimationQueue();
    });

    function clampLayer(idx) {
      if (isNaN(idx)) idx = 0;
      idx = Math.max(0, Math.min(size - 1, Math.floor(idx)));
      document.getElementById('layerIndex').value = idx;
      return idx;
    }

    // Solve button: reverse moveHistory
    document.getElementById('solveBtn').addEventListener('click', async () => {
      if (animating || animationQueue.length) return;
      // create inverse moves from history in reverse order
      const inv = [];
      for (let i = moveHistory.length - 1; i >= 0; i--) {
        const m = moveHistory[i];
        inv.push({ axis: m.axis, layerIndex: m.layerIndex, dir: -m.dir });
      }
      // enqueue them
      for (const m of inv) enqueueLayerRotation(m.axis, m.layerIndex, m.dir);
      // clear history (because after solving, cube is solved)
      moveHistory = [];
      await processAnimationQueue();
    });

    // ---------- Render loop ----------
    function animate() {
      requestAnimationFrame(animate);
      controls.update();
      renderer.render(scene, camera);
    }

    // ---------- Boot ----------
    init();
    buildCube(size);
    animate();

    // ---------- small helpers ----------
    // Override SceneUtils.detach fallback (if not present)
    if (!THREE.SceneUtils) THREE.SceneUtils = {};
    if (!THREE.SceneUtils.detach) {
      THREE.SceneUtils.detach = function (child, parent, scene) {
        // No-op fallback since we don't use it exactly; we reparent carefully elsewhere.
      };
    }

    // expose for debugging (optional)
    window._rubik = {
      scene, camera, renderer, cubies, buildCube, getCubiesInLayer, enqueueLayerRotation, processAnimationQueue
    };

    // Clean up when unloading
    window.addEventListener('beforeunload', () => {
      renderer?.dispose?.();
    });

  </script>
</body>
</html>
