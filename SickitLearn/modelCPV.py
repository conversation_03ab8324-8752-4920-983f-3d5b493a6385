import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt

# 读取Excel文件
model = pd.read_excel(r"C:\Users\<USER>\Desktop\配置明细-对比-训练.xlsx")

# 将所有列中的特殊值替换为0
model = model.replace('无装备型号，系统未展示', 0)
model = model.replace('2-T父配置', 0)
model = model.replace('1-IN类无参数', 0)
model = model.replace('非调研数据', 0)

# 处理百分比值的函数
def convert_percentage(val):
    if isinstance(val, str) and '%' in val:
        return float(val.strip('%')) / 100
    return val

# 设置要分析的列名
columns_to_analyze = [
    'CPV',
    '装备率',
    '重要度',
    '近三年装备率平均增速',
    '装备率-2022',
    '装备率同比',
    '装车率',
    '近三年装车率平均增速',
    '装车率-2022',
    '装车率同比',
    '装车量',
    '装车量同比'
]

# 转换所有需要分析的列中的百分比值
for col in columns_to_analyze:
    model[col] = model[col].apply(convert_percentage)

# 计算相关系数
correlation_matrix = model[columns_to_analyze].corr()

# 获取CPV与其他变量的相关系数
cpv_correlations = correlation_matrix['CPV'].sort_values(ascending=False)

print("\nCPV与各指标的相关系数:")
print(cpv_correlations)

# 设置中文显示
# plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
# plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
#
# # 创建相关系数热力图
# plt.figure(figsize=(12, 8))
# sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, fmt='.2f')
# plt.title('CPV与各指标相关系数热力图')
# plt.xticks(rotation=45, ha='right')
# plt.yticks(rotation=0)
# plt.tight_layout()
# plt.show()

# 将相关系数结果保存到Excel
# correlation_df = pd.DataFrame({'相关系数': cpv_correlations})
# correlation_df.to_excel('CPV相关性分析结果.xlsx')
