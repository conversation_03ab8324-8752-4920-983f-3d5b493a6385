import pandas as pd
import os
from sklearn.impute import SimpleImputer


def load_house_data(housing_path):
    csv_path = os.path.join(housing_path, "housing.csv")
    return pd.read_csv(csv_path)

housing = load_house_data(r"D:\GitHub\handson-ml2\datasets\housing")

# housing["rooms_per_household"] = housing["total_rooms"]/housing["households"]
# housing["bedrooms_per_room"] = housing["total_bedrooms"]/housing["total_rooms"]
# housing["population_per_household"]=housing["population"]/housing["households"]
#
# # 选择数值型列计算相关系数
# numeric_features = housing.select_dtypes(include=[np.number]).columns
# corr_matrix = housing[numeric_features].corr()
# print("\n数值特征之间的相关系数(与房价的关系):")
# print(corr_matrix["median_house_value"].sort_values(ascending=False))

# def test_set_check(identifier, test_ratio):
#     return crc32(np.int64(identifier)) & 0xffffffff < test_ratio * 2**32
#
# def split_train_test(data, test_ratio, id_column):
#     ids = data[id_column]
#     in_test_set = ids.apply(lambda id_: test_set_check(id_, test_ratio))
#     return data.loc[~in_test_set], data.loc[in_test_set]
#
# housing_with_id = housing.reset_index()   # adds an `index` column
# train_set, test_set = split_train_test(housing_with_id, 0.2, "index")

# attributes = ["median_house_value", "median_income", "total_rooms",
#              "housing_median_age"]
# scatter_matrix(housing[attributes], figsize=(12, 8))
# housing.plot(kind="scatter", x="median_income", y="median_house_value",
#              alpha=0.1)

imputer = SimpleImputer(strategy="median")
housing_num = housing.drop("ocean_proximity", axis=1)
imputer.fit(housing_num)
print(imputer.statistics_)
