from sklearn.datasets import load_digits
import matplotlib.pyplot as plt
import matplotlib as mpl
from sklearn.linear_model import SGDClassifier

# 加载内置的digits数据集（是MNIST的一个子集）
mnist = load_digits()

# 打印数据集的基本信息
# print("数据集的形状:", mnist.data.shape)
# print("标签的形状:", mnist.target.shape)
# print("可用的属性:", mnist.keys())

X, y = mnist.data, mnist.target
# print(X.shape)  # (1797, 64)  1797个样本，每个样本64个特征
# print(y.shape)  # (1797,)  1797个标签

# some_digit = X[0]
# some_digit_image = some_digit.reshape(8, 8)  # 将一维数组转换为二维数组
#
# plt.imshow(some_digit_image, cmap=mpl.cm.binary, interpolation="nearest")
# plt.axis("off")
# plt.show()
# print(y[0])

X_train, X_test, y_train, y_test = X[:1500], X[1500:], y[:1500], y[1500:]
y_train_5 = (y_train == 5)  # 创建一个布尔数组，表示是否为5
y_test_5 = (y_test == 5)  # 创建一个布尔数组，表示是否为5

sgd_clf = SGDClassifier(random_state=42)
sgd_clf.fit(X_train, y_train_5)

print(sgd_clf.predict([X[1]]))
