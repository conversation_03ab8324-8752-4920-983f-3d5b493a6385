<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>理想汽车·新能源市场研报分析看板</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5/dist/echarts.min.js"></script>
    <style>
        *{margin:0;padding:0;font-family:-apple-system,BlinkMacSystemFont,'PingFang SC','Microsoft YaHei',sans-serif;}
        body{background:#f6f8fa;color:#333;}
        .header{height:64px;background:#fff;border-bottom:1px solid #e1e4e8;display:flex;align-items:center;justify-content:space-between;padding:0 40px;box-shadow:0 2px 4px rgba(0,0,0,.05);}
        .header h1{font-size:22px;font-weight:600;color:#0366d6;}
        .header .date{font-size:14px;color:#586069;}
        .wrapper{display:flex;height:calc(100vh - 64px);}
        .sidebar{width:240px;background:#fff;border-right:1px solid #e1e4e8;padding:20px 0;}
        .sidebar .nav-item{padding:12px 24px;font-size:14px;cursor:pointer;border-left:3px solid transparent;}
        .sidebar .nav-item.active{background:#f1f8ff;border-left-color:#0366d6;color:#0366d6;font-weight:600;}
        .main{flex:1;padding:24px;overflow-y:auto;}
        .section{background:#fff;border:1px solid #e1e4e8;border-radius:6px;margin-bottom:24px;}
        .section-header{padding:16px 24px;border-bottom:1px solid #e1e4e8;font-size:16px;font-weight:600;}
        .section-body{padding:24px;}
        .flex-row{display:flex;gap:24px;}
        .flex-1{flex:1;}
        .chart{height:360px;}
        .btn-group{display:flex;gap:8px;margin-bottom:12px;}
        .btn{padding:6px 12px;border:1px solid #d1d9e0;border-radius:6px;background:#fff;font-size:13px;cursor:pointer;transition:all .15s;}
        .btn.active{background:#0366d6;color:#fff;border-color:#0366d6;}
        .btn:hover:not(.active){background:#f3f4f6;}
        .table{width:100%;border-collapse:collapse;font-size:14px;}
        .table th,.table td{padding:8px 12px;text-align:right;border-bottom:1px solid #e1e4e8;}
        .table th:first-child,.table td:first-child{text-align:left;}
        .table th{background:#f6f8fa;font-weight:600;}
        .kpi-grid{display:grid;grid-template-columns:repeat(4,1fr);gap:24px;margin-bottom:24px;}
        .kpi-card{background:#fff;border:1px solid #e1e4e8;border-radius:6px;padding:24px;text-align:center;}
        .kpi-value{font-size:28px;font-weight:700;color:#0366d6;margin-bottom:4px;}
        .kpi-label{font-size:13px;color:#586069;}
        .kpi-change{font-size:13px;margin-top:4px;}
        .kpi-change.positive{color:#28a745;}
        .kpi-change.negative{color:#d73a49;}
    </style>
</head>
<body>
<div class="header">
    <h1>理想汽车·新能源市场研报分析看板</h1>
    <div class="date" id="now"></div>
</div>
<div class="wrapper">
    <aside class="sidebar">
        <div class="nav-item active" data-sec="trend">销量趋势</div>
        <div class="nav-item" data-sec="model">车型结构</div>
        <div class="nav-item" data-sec="compare">对比分析</div>
    </aside>

    <main class="main">
        <!-- 关键指标 -->
        <div class="kpi-grid">
            <div class="kpi-card">
                <div class="kpi-value">110.97万</div>
                <div class="kpi-label">25年6月新能源销量</div>
                <div class="kpi-change positive">+15.0% 环比</div>
            </div>
            <div class="kpi-card">
                <div class="kpi-value">3.58万</div>
                <div class="kpi-label">25年6月理想销量</div>
                <div class="kpi-change negative">-19.4% 环比</div>
            </div>
            <div class="kpi-card">
                <div class="kpi-value">45.5%</div>
                <div class="kpi-label">理想L6 占比</div>
                <div class="kpi-change">当月最畅销车型</div>
            </div>
            <div class="kpi-card">
                <div class="kpi-value">3.2%</div>
                <div class="kpi-label">理想市场份额</div>
                <div class="kpi-change">较上月-0.7pp</div>
            </div>
        </div>

        <!-- 新能源整体趋势 -->
        <section class="section" id="trend">
            <div class="section-header">新能源整体市场销量趋势</div>
            <div class="section-body">
                <div class="btn-group">
                    <button class="btn active" data-set="ne-sale">销量</button>
                    <button class="btn" data-set="ne-yoy">同比</button>
                    <button class="btn" data-set="ne-mom">环比</button>
                </div>
                <div id="chartNe" class="chart"></div>
            </div>
        </section>

        <!-- 理想品牌趋势 -->
        <section class="section">
            <div class="section-header">理想品牌销量趋势</div>
            <div class="section-body">
                <div class="btn-group">
                    <button class="btn active" data-set="lx-sale">销量</button>
                    <button class="btn" data-set="lx-yoy">同比</button>
                    <button class="btn" data-set="lx-mom">环比</button>
                </div>
                <div id="chartLx" class="chart"></div>
            </div>
        </section>

        <!-- 车型结构 -->
        <section class="section" id="model" style="display:none;">
            <div class="section-header">25年6月理想各车型销量结构</div>
            <div class="section-body flex-row">
                <div class="flex-1 chart" id="chartPie"></div>
                <table class="table flex-1">
                    <thead>
                        <tr><th>车型</th><th>销量</th><th>占比</th></tr>
                    </thead>
                    <tbody>
                        <tr><td>理想L6</td><td>16,296</td><td>45.5%</td></tr>
                        <tr><td>理想L7</td><td>8,056</td><td>22.5%</td></tr>
                        <tr><td>理想L9</td><td>4,852</td><td>13.5%</td></tr>
                        <tr><td>理想L8</td><td>4,335</td><td>12.1%</td></tr>
                        <tr><td>理想MEGA</td><td>2,294</td><td>6.4%</td></tr>
                    </tbody>
                </table>
            </div>
        </section>

        <!-- 对比分析 -->
        <section class="section" id="compare" style="display:none;">
            <div class="section-header">理想 vs 新能源大盘增速对比</div>
            <div class="section-body">
                <div id="chartCompare" class="chart"></div>
            </div>
        </section>
    </main>
</div>

<script>
// 1. 基本数据
const months=['24/07','24/08','24/09','24/10','24/11','24/12','25/01','25/02','25/03','25/04','25/05','25/06'];
const ne={
    sale:[882999,1012070,1105428,1182133,1242798,1293189,703643,656305,969226,870670,964767,1109724],
    yoy:[44.9,48.8,57.8,64.7,61.4,45.2,5.9,74.7,38.8,29.3,22.9,33.3],
    mom:[6.1,14.6,9.2,6.9,5.1,4.1,-45.6,-6.7,47.7,-10.2,10.8,15.0]
};
const lx={
    sale:[50790,47980,52997,50211,50518,58411,30772,26007,36252,34777,44489,35844],
    yoy:[51.7,41.6,45.7,25.2,20.7,15.9,-3.5,24.2,11.1,28.4,31.4,-24.6],
    mom:[6.9,-5.5,10.5,-5.3,0.6,15.6,-47.3,-15.5,39.4,-4.1,27.9,-19.4]
};

// 2. 图表初始化
const chartNe=echarts.init(document.getElementById('chartNe'));
const chartLx=echarts.init(document.getElementById('chartLx'));
const chartPie=echarts.init(document.getElementById('chartPie'));
const chartCompare=echarts.init(document.getElementById('chartCompare'));

function drawLine(el,data,title,color='#0366d6'){
    el.setOption({
        color:[color],
        grid:{left:40,right:20,top:40,bottom:40},
        tooltip:{trigger:'axis'},
        xAxis:{type:'category',data:months,axisLine:{lineStyle:{color:'#ccc'}}},
        yAxis:{type:'value',axisLine:{lineStyle:{color:'#ccc'}}},
        series:[{data,type:'line',smooth:true,areaStyle:{opacity:.1}}]
    });
}
drawLine(chartNe,ne.sale,'销量');
drawLine(chartLx,lx.sale,'销量','#28a745');

chartPie.setOption({
    tooltip:{trigger:'item',formatter:'{b}: {c} ({d}%)'},
    series:[{
        type:'pie',radius:['40%','70%'],
        data:[
            {value:16296,name:'理想L6',itemStyle:{color:'#0366d6'}},
            {value:8056,name:'理想L7',itemStyle:{color:'#28a745'}},
            {value:4852,name:'理想L9',itemStyle:{color:'#ff6b35'}},
            {value:4335,name:'理想L8',itemStyle:{color:'#f9c74f'}},
            {value:2294,name:'理想MEGA',itemStyle:{color:'#9d4edd'}}
        ],
        label:{formatter:'{b}: {d}%'}
    }]
});

chartCompare.setOption({
    tooltip:{trigger:'axis'},
    legend:{data:['新能源同比','理想同比'],top:10},
    grid:{left:40,right:20,top:60,bottom:40},
    xAxis:{type:'category',data:months},
    yAxis:{type:'value',axisLabel:{formatter:'{value}%'}},
    series:[
        {name:'新能源同比',type:'line',data:ne.yoy},
        {name:'理想同比',type:'line',data:lx.yoy}
    ]
});

// 3. 按钮交互
document.querySelectorAll('.btn-group .btn').forEach(btn=>{
    btn.onclick=function(){
        const group=this.parentElement;
        group.querySelector('.active').classList.remove('active');
        this.classList.add('active');
        const set=this.dataset.set;
        if(set==='ne-sale'){chartNe.setOption({series:[{data:ne.sale}]});}
        if(set==='ne-yoy'){chartNe.setOption({series:[{data:ne.yoy,yAxis:{axisLabel:{formatter:'{value}%'}}}]});}
        if(set==='ne-mom'){chartNe.setOption({series:[{data:ne.mom,yAxis:{axisLabel:{formatter:'{value}%'}}}]});}
        if(set==='lx-sale'){chartLx.setOption({series:[{data:lx.sale}]});}
        if(set==='lx-yoy'){chartLx.setOption({series:[{data:lx.yoy,yAxis:{axisLabel:{formatter:'{value}%'}}}]});}
        if(set==='lx-mom'){chartLx.setOption({series:[{data:lx.mom,yAxis:{axisLabel:{formatter:'{value}%'}}}]});}
    };
});

// 4. 左侧导航
document.querySelectorAll('.nav-item').forEach(nav=>{
    nav.onclick=function(){
        document.querySelectorAll('.nav-item').forEach(n=>n.classList.remove('active'));
        this.classList.add('active');
        const sec=this.dataset.sec;
        document.querySelectorAll('section').forEach(s=>s.style.display='none');
        document.getElementById(sec).style.display='block';
    };
});

// 5. 响应式
addEventListener('resize',()=>{
    [chartNe,chartLx,chartPie,chartCompare].forEach(c=>c.resize());
});

// 6. 时间
document.getElementById('now').innerText=new Date().toLocaleString('zh-CN');
</script>
</body>
</html>