<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>2025年汽车消费补贴政策报告</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }

        .slide {
            width: 90%;
            max-width: 1200px;
            min-height: 600px;
            margin: 2rem auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 2.5rem;
            position: relative;
            overflow: hidden;
            display: none;
        }

        .slide.active {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .header {
            border-bottom: 2px solid #e1e5eb;
            padding-bottom: 1rem;
            margin-bottom: 1.5rem;
        }

        .footer {
            position: absolute;
            bottom: 1rem;
            right: 2rem;
            color: #666;
            font-size: 0.9rem;
        }

        .nav-btn {
            position: fixed;
            top: 50%;
            transform: translateY(-50%);
            width: 50px;
            height: 50px;
            background: rgba(255,255,255,0.8);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            cursor: pointer;
            z-index: 100;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: #3b82f6;
            color: white;
        }

        .prev-btn {
            left: 2rem;
        }

        .next-btn {
            right: 2rem;
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            height: 5px;
            background: #3b82f6;
            z-index: 100;
            transition: width 0.3s ease;
        }

        .policy-card {
            border-left: 4px solid #3b82f6;
            transition: all 0.3s ease;
        }

        .policy-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .tab-btn {
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            background: #3b82f6;
            color: white;
        }

        .chart-container {
            height: 300px;
            position: relative;
        }

        .highlight {
            background: linear-gradient(90deg, rgba(59,130,246,0.1) 0%, rgba(59,130,246,0) 100%);
            padding: 0.5rem 1rem;
            border-radius: 6px;
        }
    </style>
</head>
<body>
    <div class="progress-bar" id="progressBar"></div>

    <div class="nav-btn prev-btn" id="prevBtn">
        <i class="fas fa-chevron-left"></i>
    </div>

    <div class="nav-btn next-btn" id="nextBtn">
        <i class="fas fa-chevron-right"></i>
    </div>

    <!-- 封面幻灯片 -->
    <div class="slide active" id="slide1">
        <div class="flex flex-col items-center justify-center h-full">
            <div class="text-center">
                <h1 class="text-5xl font-bold text-blue-600 mb-6">2025年汽车消费补贴政策报告</h1>
                <p class="text-xl text-gray-600 mb-10">全国各地区汽车消费促进政策分析</p>
                <div class="flex justify-center space-x-4">
                    <div class="bg-blue-100 text-blue-800 px-4 py-2 rounded-lg">
                        <i class="fas fa-calendar-alt mr-2"></i>2025年7月数据
                    </div>
                    <div class="bg-green-100 text-green-800 px-4 py-2 rounded-lg">
                        <i class="fas fa-map-marker-alt mr-2"></i>覆盖10+省市
                    </div>
                </div>
            </div>
            <div class="mt-16 text-gray-500">
                <p>数据来源：各地方政府公开政策文件</p>
            </div>
        </div>
        <div class="footer">1 / 10</div>
    </div>

    <!-- 目录幻灯片 -->
    <div class="slide" id="slide2">
        <div class="header">
            <h2 class="text-3xl font-bold text-blue-600">报告目录</h2>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
            <div class="bg-gray-50 p-6 rounded-lg hover:bg-blue-50 transition-colors cursor-pointer" onclick="goToSlide(3)">
                <div class="flex items-center mb-3">
                    <div class="bg-blue-100 text-blue-800 w-10 h-10 rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <h3 class="text-xl font-semibold">政策概览</h3>
                </div>
                <p class="text-gray-600">全国汽车消费补贴政策总体情况</p>
            </div>

            <div class="bg-gray-50 p-6 rounded-lg hover:bg-blue-50 transition-colors cursor-pointer" onclick="goToSlide(4)">
                <div class="flex items-center mb-3">
                    <div class="bg-green-100 text-green-800 w-10 h-10 rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-map-marked-alt"></i>
                    </div>
                    <h3 class="text-xl font-semibold">地区分布</h3>
                </div>
                <p class="text-gray-600">各省市政策实施情况对比</p>
            </div>

            <div class="bg-gray-50 p-6 rounded-lg hover:bg-blue-50 transition-colors cursor-pointer" onclick="goToSlide(5)">
                <div class="flex items-center mb-3">
                    <div class="bg-yellow-100 text-yellow-800 w-10 h-10 rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-car"></i>
                    </div>
                    <h3 class="text-xl font-semibold">补贴标准</h3>
                </div>
                <p class="text-gray-600">燃油车与新能源车补贴对比</p>
            </div>

            <div class="bg-gray-50 p-6 rounded-lg hover:bg-blue-50 transition-colors cursor-pointer" onclick="goToSlide(6)">
                <div class="flex items-center mb-3">
                    <div class="bg-purple-100 text-purple-800 w-10 h-10 rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <h3 class="text-xl font-semibold">补贴金额</h3>
                </div>
                <p class="text-gray-600">各地区补贴金额区间分析</p>
            </div>

            <div class="bg-gray-50 p-6 rounded-lg hover:bg-blue-50 transition-colors cursor-pointer" onclick="goToSlide(7)">
                <div class="flex items-center mb-3">
                    <div class="bg-red-100 text-red-800 w-10 h-10 rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-calendar"></i>
                    </div>
                    <h3 class="text-xl font-semibold">时间分布</h3>
                </div>
                <p class="text-gray-600">政策实施时间与有效期分析</p>
            </div>

            <div class="bg-gray-50 p-6 rounded-lg hover:bg-blue-50 transition-colors cursor-pointer" onclick="goToSlide(8)">
                <div class="flex items-center mb-3">
                    <div class="bg-indigo-100 text-indigo-800 w-10 h-10 rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h3 class="text-xl font-semibold">政策详情</h3>
                </div>
                <p class="text-gray-600">各地区具体政策内容</p>
            </div>
        </div>
        <div class="footer">2 / 10</div>
    </div>

    <!-- 政策概览 -->
    <div class="slide" id="slide3">
        <div class="header">
            <h2 class="text-3xl font-bold text-blue-600">政策概览</h2>
            <p class="text-gray-600">全国汽车消费补贴政策总体情况</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">政策数量</h3>
                    <div class="bg-blue-100 text-blue-800 p-2 rounded-full">
                        <i class="fas fa-file-alt"></i>
                    </div>
                </div>
                <p class="text-4xl font-bold text-blue-600 mb-2">10+</p>
                <p class="text-gray-500">个地方政策</p>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">覆盖省市</h3>
                    <div class="bg-green-100 text-green-800 p-2 rounded-full">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                </div>
                <p class="text-4xl font-bold text-green-600 mb-2">8</p>
                <p class="text-gray-500">个省级行政区</p>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">补贴总额</h3>
                    <div class="bg-yellow-100 text-yellow-800 p-2 rounded-full">
                        <i class="fas fa-yen-sign"></i>
                    </div>
                </div>
                <p class="text-4xl font-bold text-yellow-600 mb-2">1亿+</p>
                <p class="text-gray-500">元补贴资金</p>
            </div>
        </div>

        <div class="mt-8">
            <h3 class="text-xl font-semibold mb-4">政策类型分布</h3>
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <div class="chart-container">
                    <canvas id="policyTypeChart"></canvas>
                </div>
            </div>
        </div>

        <div class="mt-6">
            <div class="highlight">
                <p><i class="fas fa-lightbulb text-yellow-500 mr-2"></i> <strong>主要发现：</strong>所有政策均为地方性购置补贴政策，主要针对个人消费者购车提供直接现金补贴或消费券。</p>
            </div>
        </div>
        <div class="footer">3 / 10</div>
    </div>

    <!-- 地区分布 -->
    <div class="slide" id="slide4">
        <div class="header">
            <h2 class="text-3xl font-bold text-blue-600">地区分布</h2>
            <p class="text-gray-600">各省市政策实施情况对比</p>
        </div>

        <div class="mt-8">
            <h3 class="text-xl font-semibold mb-4">政策地区分布</h3>
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <div class="chart-container">
                    <canvas id="regionChart"></canvas>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
            <div>
                <h3 class="text-xl font-semibold mb-4">政策数量TOP省份</h3>
                <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center mr-3">
                                    1
                                </div>
                                <span>广东省</span>
                            </div>
                            <span class="font-semibold">3项政策</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center mr-3">
                                    2
                                </div>
                                <span>四川省</span>
                            </div>
                            <span class="font-semibold">3项政策</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center mr-3">
                                    3
                                </div>
                                <span>吉林省</span>
                            </div>
                            <span class="font-semibold">2项政策</span>
                        </div>
                    </div>
                </div>
            </div>

            <div>
                <h3 class="text-xl font-semibold mb-4">补贴金额TOP地区</h3>
                <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-green-100 text-green-800 rounded-full flex items-center justify-center mr-3">
                                    1
                                </div>
                                <span>上海市</span>
                            </div>
                            <span class="font-semibold">1.32亿元</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-green-100 text-green-800 rounded-full flex items-center justify-center mr-3">
                                    2
                                </div>
                                <span>四川省绵阳市</span>
                            </div>
                            <span class="font-semibold">3200万元</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-green-100 text-green-800 rounded-full flex items-center justify-center mr-3">
                                    3
                                </div>
                                <span>广东省广州市</span>
                            </div>
                            <span class="font-semibold">700万元</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-6">
            <div class="highlight">
                <p><i class="fas fa-lightbulb text-yellow-500 mr-2"></i> <strong>主要发现：</strong>广东省和四川省政策数量最多，上海市补贴总额最高，单个政策补贴金额可达上亿元。</p>
            </div>
        </div>
        <div class="footer">4 / 10</div>
    </div>

    <!-- 补贴标准 -->
    <div class="slide" id="slide5">
        <div class="header">
            <h2 class="text-3xl font-bold text-blue-600">补贴标准</h2>
            <p class="text-gray-600">燃油车与新能源车补贴对比</p>
        </div>

        <div class="mt-8">
            <div class="flex space-x-2 mb-6">
                <button class="tab-btn active px-4 py-2 rounded-lg bg-blue-600 text-white" onclick="changeTab('all')">全部车型</button>
                <button class="tab-btn px-4 py-2 rounded-lg bg-gray-200" onclick="changeTab('fuel')">燃油车</button>
                <button class="tab-btn px-4 py-2 rounded-lg bg-gray-200" onclick="changeTab('newEnergy')">新能源车</button>
            </div>

            <div id="allTab" class="tab-content">
                <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                    <div class="chart-container">
                        <canvas id="subsidyStandardChart"></canvas>
                    </div>
                </div>

                <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-xl font-semibold mb-4">燃油车补贴特点</h3>
                        <ul class="space-y-3 text-gray-700">
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-blue-500 mt-1 mr-2"></i>
                                <span>补贴金额普遍低于新能源车</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-blue-500 mt-1 mr-2"></i>
                                <span>按购车价格分档补贴，最高5500元</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-blue-500 mt-1 mr-2"></i>
                                <span>部分政策要求符合国六B排放标准</span>
                            </li>
                        </ul>
                    </div>

                    <div>
                        <h3 class="text-xl font-semibold mb-4">新能源车补贴特点</h3>
                        <ul class="space-y-3 text-gray-700">
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>补贴金额普遍高于燃油车20-50%</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>最高补贴可达6500元</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>部分政策可与置换补贴叠加</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div id="fuelTab" class="tab-content hidden">
                <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                    <div class="chart-container">
                        <canvas id="fuelSubsidyChart"></canvas>
                    </div>
                </div>

                <div class="mt-6">
                    <h3 class="text-xl font-semibold mb-4">典型燃油车补贴标准</h3>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-100">
                                    <tr>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">价格区间</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">补贴金额</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">代表地区</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">5万元以下</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">500-1000元</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">绵阳市、宜宾市</td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">5-10万元</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">1000-2000元</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">淮南市、长春市</td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">10-20万元</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">1500-4000元</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">淮南市、东莞市</td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">20万元以上</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">4000-5500元</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">淮南市、上海市</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div id="newEnergyTab" class="tab-content hidden">
                <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                    <div class="chart-container">
                        <canvas id="newEnergySubsidyChart"></canvas>
                    </div>
                </div>

                <div class="mt-6">
                    <h3 class="text-xl font-semibold mb-4">典型新能源车补贴标准</h3>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-100">
                                    <tr>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">价格区间</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">补贴金额</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">代表地区</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">5万元以下</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">1000-2000元</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">淮南市、宜宾市</td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">5-10万元</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">1500-2500元</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">淮南市、绵阳市</td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">10-20万元</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">2500-4000元</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">淮南市、东莞市</td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">20万元以上</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">4500-6500元</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">绵阳市、广州市</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-6">
            <div class="highlight">
                <p><i class="fas fa-lightbulb text-yellow-
</html>