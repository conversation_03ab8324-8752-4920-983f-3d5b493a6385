<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新能源市场研报看板</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
    <style>
        .chart-container {
            position: relative;
            height: 400px;
            width: 100%;
        }
        .tooltip {
            position: absolute;
            padding: 8px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            border-radius: 4px;
            pointer-events: none;
            font-size: 12px;
            z-index: 100;
            display: none;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .tab-button.active {
            border-bottom: 3px solid #3b82f6;
            color: #3b82f6;
            font-weight: 600;
        }
        @media (max-width: 768px) {
            .chart-container {
                height: 300px;
            }
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <div class="container mx-auto px-4 py-8 max-w-7xl">
        <!-- Header -->
        <header class="mb-8">
            <h1 class="text-3xl md:text-4xl font-bold text-gray-800 mb-2">新能源市场深度分析报告</h1>
            <p class="text-gray-600">2025年6月数据更新 | 市场趋势与品牌表现</p>
            <div class="border-b border-gray-200 mt-4"></div>
        </header>

        <!-- Navigation Tabs -->
        <div class="flex space-x-6 border-b border-gray-200 mb-6 overflow-x-auto">
            <button class="tab-button py-2 px-1 active" data-tab="market-trend">整体市场趋势</button>
            <button class="tab-button py-2 px-1" data-tab="brand-performance">品牌表现</button>
            <button class="tab-button py-2 px-1" data-tab="model-analysis">车型分析</button>
        </div>

        <!-- Market Trend Tab -->
        <div id="market-trend" class="tab-content active">
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-800">新能源整体市场销量趋势</h2>
                    <div class="flex space-x-2">
                        <button class="filter-btn px-3 py-1 rounded-md bg-blue-100 text-blue-600 text-sm" data-filter="volume">销量</button>
                        <button class="filter-btn px-3 py-1 rounded-md bg-gray-100 text-gray-600 text-sm" data-filter="yoy">同比</button>
                        <button class="filter-btn px-3 py-1 rounded-md bg-gray-100 text-gray-600 text-sm" data-filter="mom">环比</button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="marketTrendChart"></canvas>
                </div>
                <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <p class="text-sm text-gray-500">最新月销量</p>
                        <p class="text-2xl font-bold text-blue-600">1,109,724</p>
                        <p class="text-sm text-gray-600">2025年6月</p>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg">
                        <p class="text-sm text-gray-500">同比增长</p>
                        <p class="text-2xl font-bold text-green-600">33.3%</p>
                        <p class="text-sm text-gray-600">较2024年6月</p>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <p class="text-sm text-gray-500">环比增长</p>
                        <p class="text-2xl font-bold text-purple-600">15.0%</p>
                        <p class="text-sm text-gray-600">较2025年5月</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">市场季节性分析</h2>
                <div class="chart-container">
                    <canvas id="seasonalChart"></canvas>
                </div>
                <div class="mt-4 text-sm text-gray-600">
                    <p>• 市场呈现明显的季节性特征，每年1-2月受春节因素影响销量大幅下滑</p>
                    <p>• 2025年3月后市场快速恢复，6月销量创历史新高</p>
                    <p>• 同比增速保持30%以上高位，显示市场仍处于快速增长期</p>
                </div>
            </div>
        </div>

        <!-- Brand Performance Tab -->
        <div id="brand-performance" class="tab-content">
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-800">理想品牌销量趋势</h2>
                    <div class="flex space-x-2">
                        <button class="brand-filter-btn px-3 py-1 rounded-md bg-blue-100 text-blue-600 text-sm" data-filter="volume">销量</button>
                        <button class="brand-filter-btn px-3 py-1 rounded-md bg-gray-100 text-gray-600 text-sm" data-filter="yoy">同比</button>
                        <button class="brand-filter-btn px-3 py-1 rounded-md bg-gray-100 text-gray-600 text-sm" data-filter="mom">环比</button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="brandTrendChart"></canvas>
                </div>
                <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <p class="text-sm text-gray-500">最新月销量</p>
                        <p class="text-2xl font-bold text-blue-600">35,844</p>
                        <p class="text-sm text-gray-600">2025年6月</p>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg">
                        <p class="text-sm text-gray-500">同比增长</p>
                        <p class="text-2xl font-bold text-red-600">-24.6%</p>
                        <p class="text-sm text-gray-600">较2024年6月</p>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <p class="text-sm text-gray-500">环比增长</p>
                        <p class="text-2xl font-bold text-red-600">-19.4%</p>
                        <p class="text-sm text-gray-600">较2025年5月</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">理想品牌市场份额</h2>
                <div class="chart-container">
                    <canvas id="marketShareChart"></canvas>
                </div>
                <div class="mt-4 text-sm text-gray-600">
                    <p>• 2025年6月理想品牌市场份额为3.2%，较2024年同期下降2.3个百分点</p>
                    <p>• 2024年理想品牌市场份额稳定在4.5%左右，2025年出现明显下滑</p>
                    <p>• 2025年6月销量同比下滑24.6%，表现弱于市场整体(+33.3%)</p>
                </div>
            </div>
        </div>

        <!-- Model Analysis Tab -->
        <div id="model-analysis" class="tab-content">
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">2025年6月理想各车型销量占比</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="chart-container">
                        <canvas id="modelPieChart"></canvas>
                    </div>
                    <div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">车型</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">销量</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">占比</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">理想L6</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">16,296</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">45.5%</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">理想L7</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">8,056</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">22.5%</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">理想L9</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">4,852</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">13.5%</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">理想L8</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">4,335</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">12.1%</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">理想MEGA</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2,294</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">6.4%</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-4 text-sm text-gray-600">
                            <p>• 理想L6成为绝对主力车型，贡献品牌近半销量</p>
                            <p>• L系列(L6+L7+L8+L9)合计占比93.6%，是品牌核心产品线</p>
                            <p>• MEGA车型表现不及预期，仅贡献6.4%销量</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">理想各车型销量趋势</h2>
                <div class="mb-4 flex space-x-2 overflow-x-auto">
                    <button class="model-filter-btn px-3 py-1 rounded-md bg-blue-100 text-blue-600 text-sm whitespace-nowrap" data-model="all">全部车型</button>
                    <button class="model-filter-btn px-3 py-1 rounded-md bg-gray-100 text-gray-600 text-sm whitespace-nowrap" data-model="l6">理想L6</button>
                    <button class="model-filter-btn px-3 py-1 rounded-md bg-gray-100 text-gray-600 text-sm whitespace-nowrap" data-model="l7">理想L7</button>
                    <button class="model-filter-btn px-3 py-1 rounded-md bg-gray-100 text-gray-600 text-sm whitespace-nowrap" data-model="l8">理想L8</button>
                    <button class="model-filter-btn px-3 py-1 rounded-md bg-gray-100 text-gray-600 text-sm whitespace-nowrap" data-model="l9">理想L9</button>
                    <button class="model-filter-btn px-3 py-1 rounded-md bg-gray-100 text-gray-600 text-sm whitespace-nowrap" data-model="mega">理想MEGA</button>
                </div>
                <div class="chart-container">
                    <canvas id="modelTrendChart"></canvas>
                </div>
                <div class="mt-4 text-sm text-gray-600">
                    <p>• 理想L6自上市后迅速成为销量主力，2025年5月达到峰值16,296辆</p>
                    <p>• L7/L8/L9三款车型销量相对稳定，2025年6月均出现不同程度下滑</p>
                    <p>• MEGA车型销量持续低迷，未能打开高端MPV市场</p>
                </div>
            </div>
        </div>

        <!-- Summary -->
        <div class="mt-8 bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">核心结论</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-medium text-gray-700 mb-2">市场整体</h3>
                    <ul class="list-disc pl-5 text-gray-600 space-y-2">
                        <li>新能源市场保持强劲增长，2025年6月销量突破110万辆，同比增长33.3%</li>
                        <li>季节性波动明显，但整体向上趋势不变，预计2025年全年销量将超1200万辆</li>
                        <li>市场渗透率持续提升，竞争格局加速分化</li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-medium text-gray-700 mb-2">理想品牌</h3>
                    <ul class="list-disc pl-5 text-gray-600 space-y-2">
                        <li>2025年6月销量35,844辆，同比下滑24.6%，市场份额降至3.2%</li>
                        <li>产品结构以L6为主(45.5%)，L系列合计占比93.6%</li>
                        <li>MEGA车型表现不及预期，高端化战略面临挑战</li>
                        <li>需警惕销量持续下滑风险，加快产品迭代与营销创新</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Tab Switching
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', () => {
                document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

                button.classList.add('active');
                const tabId = button.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });

        // Market Trend Data
        const months = ['24年7月', '8月', '9月', '10月', '11月', '12月', '25年1月', '2月', '3月', '4月', '5月', '6月'];
        const marketVolume = [882999, 1012070, 1105428, 1182133, 1242798, 1293189, 703643, 656305, 969226, 870670, 964767, 1109724];
        const marketYoy = [44.9, 48.8, 57.8, 64.7, 61.4, 45.2, 5.9, 74.7, 38.8, 29.3, 22.9, 33.3];
        const marketMom = [6.1, 14.6, 9.2, 6.9, 5.1, 4.1, -45.6, -6.7, 47.7, -10.2, 10.8, 15.0];

        // Brand Trend Data
        const brandVolume = [50790, 47980, 52997, 50211, 50518, 58411, 30772, 26007, 36252, 34777, 44489, 35844];
        const brandYoy = [51.7, 41.6, 45.7, 25.2, 20.7, 15.9, -3.5, 24.2, 11.1, 28.4, 31.4, -24.6];
        const brandMom = [6.9, -5.5, 10.5, -5.3, 0.6, 15.6, -47.3, -15.5, 39.4, -4.1, 27.9, -19.4];

        // Model Data
        const modelNames = ['理想L9', '理想L7', '理想L8', '理想MEGA', '理想L6', '理想i8'];
        const modelSales = [4852, 8056, 4335, 2294, 16296, 11];
        const modelPercentages = [13.5, 22.5, 12.1, 6.4, 45.5, 0.0];

        // Model Trend Data (simplified for demo)
        const modelTrendData = {
            l6: [0, 0, 0, 0, 0, 0, 0, 0, 8000, 12000, 16296, 13000],
            l7: [12000, 11500, 12500, 11800, 12000, 13500, 7000, 6000, 8500, 8200, 9500, 8056],
            l8: [10000, 9500, 10500, 9800, 9500, 11000, 5500, 4800, 7000, 6500, 8000, 4335],
            l9: [8000, 7500, 8500, 7800, 8000, 9000, 4500, 4000, 6000, 5500, 7000, 4852],
            mega: [0, 0, 0, 0, 0, 500, 2500, 2200, 3000, 2800, 3500, 2294],
            i8: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11]
        };

        // Market Trend Chart
        const marketTrendCtx = document.getElementById('marketTrendChart').getContext('2d');
        let marketTrendChart = new Chart(marketTrendCtx, {
            type: 'bar',
            data: {
                labels: months,
                datasets: [{
                    label: '销量',
                    data: marketVolume,
                    backgroundColor: 'rgba(59, 130, 246, 0.7)',
                    borderColor: 'rgba(59, 130, 246, 1)',
                    borderWidth: 1,
                    yAxisID: 'y'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.dataset.yAxisID === 'y') {
                                    label += context.raw.toLocaleString() + '辆';
                                } else {
                                    label += context.raw + '%';
                                }
                                return label;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: '销量(辆)'
                        },
                        ticks: {
                            callback: function(value) {
                                return (value / 10000) + '万';
                            }
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: false,
                        position: 'right',
                        min: -50,
                        max: 100,
                        title: {
                            display: true,
                            text: '增长率(%)'
                        },
                        grid: {
                            drawOnChartArea: false
                        }
                    }
                }
            }
        });

        // Seasonal Chart
        const seasonalCtx = document.getElementById('seasonalChart').getContext('2d');
        const seasonalChart = new Chart(seasonalCtx, {
            type: 'line',
            data: {
                labels: months,
                datasets: [
                    {
                        label: '2024年',
                        data: marketVolume.slice(0, 6),
                        borderColor: 'rgba(99, 102, 241, 1)',
                        backgroundColor: 'rgba(99, 102, 241, 0.1)',
                        borderWidth: 2,
                        tension: 0.3,
                        fill: true
                    },
                    {
                        label: '2025年',
                        data: [null, null, null, null, null, null, ...marketVolume.slice(6)],
                        borderColor: 'rgba(239, 68, 68, 1)',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        borderWidth: 2,
                        tension: 0.3,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                label += context.raw.toLocaleString() + '辆';
                                return label;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        title: {
                            display: true,
                            text: '销量(辆)'
                        },
                        ticks: {
                            callback: function(value) {
                                return (value / 10000) + '万';
                            }
                        }
                    }
                }
            }
        });

        // Brand Trend Chart
        const brandTrendCtx = document.getElementById('brandTrendChart').getContext('2d');
        let brandTrendChart = new Chart(brandTrendCtx, {
            type: 'bar',
            data: {
                labels: months,
                datasets: [{
                    label: '销量',
                    data: brandVolume,
                    backgroundColor: 'rgba(59, 130, 246, 0.7)',
                    borderColor: 'rgba(59, 130, 246, 1)',
                    borderWidth: 1,
                    yAxisID: 'y'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.dataset.yAxisID === 'y') {
                                    label += context.raw.toLocaleString() + '辆';
                                } else {
                                    label += context.raw + '%';
                                }
                                return label;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: '销量(辆)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: false,
                        position: 'right',
                        min: -50,
                        max: 100,
                        title: {
                            display: true,
                            text: '增长率(%)'
                        },
                        grid: {
                            drawOnChartArea: false
                        }
                    }
                }
            }
        });

        // Market Share Chart
        const marketShareCtx = document.getElementById('marketShareChart').getContext('2d');
        const marketShareChart = new Chart(marketShareCtx, {
            type: 'line',
            data: {
                labels: months,
                datasets: [{
                    label: '理想市场份额(%)',
                    data: brandVolume.map((v, i) => (v / marketVolume[i] * 100).toFixed(1)),
                    borderColor: 'rgba(234, 88, 12, 1)',
                    backgroundColor: 'rgba(234, 88, 12, 0.1)',
                    borderWidth: 2,
                    tension: 0.3,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.parsed.y + '%';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        title: {
                            display: true,
                            text: '市场份额(%)'
                        },
                        min: 0,
                        max: 6
                    }
                }
            }
        });

        // Model Pie Chart
        const modelPieCtx = document.getElementById('modelPieChart').getContext('2d');
        const modelPieChart = new Chart(modelPieCtx, {
            type: 'doughnut',
            data: {
                labels: modelNames,
                datasets: [{
                    data: modelSales,
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)',
                        'rgba(201, 203, 207, 0.7)'
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)',
                        'rgba(201, 203, 207, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const percentage = modelPercentages[context.dataIndex];
                                return `${label}: ${value}辆 (${percentage}%)`;
                            }
                        }
                    },
                    datalabels: {
                        formatter: (value, ctx) => {
                            return modelPercentages[ctx.dataIndex] + '%';
                        },
                        color: '#000',
                        font: {
                            weight: 'bold'
                        }
                    }
                }
            },
            plugins: [ChartDataLabels]
        });

        // Model Trend Chart
        const modelTrendCtx = document.getElementById('modelTrendChart').getContext('2d');
        const modelTrendChart = new Chart(modelTrendCtx, {
            type: 'line',
            data: {
                labels: months,
                datasets: [
                    {
                        label: '理想L6',
                        data: modelTrendData.l6,
                        borderColor: 'rgba(153, 102, 255, 1)',
                        backgroundColor: 'rgba(153, 102, 255, 0.1)',
                        borderWidth: 2,
                        tension: 0.3,
                        fill: true,
                        hidden: false
                    },
                    {
                        label: '理想L7',
                        data: modelTrendData.l7,
                        borderColor: 'rgba(54, 162, 235, 1)',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        borderWidth: 2,
                        tension: 0.3,
                        fill: true,
                        hidden: true
                    },
                    {
                        label: '理想L8',
                        data: modelTrendData.l8,
                        borderColor: 'rgba(255, 206, 86, 1)',
                        backgroundColor: 'rgba(255, 206, 86, 0.1)',
                        borderWidth: 2,
                        tension: 0.3,
                        fill: true,
                        hidden: true
                    },
                    {
                        label: '理想L9',
                        data: modelTrendData.l9,
                        borderColor: 'rgba(255, 99, 132, 1)',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        borderWidth: 2,
                        tension: 0.3,
                        fill: true,
                        hidden: true
                    },
                    {
                        label: '理想MEGA',
                        data: modelTrendData.mega,
                        borderColor: 'rgba(75, 192, 192, 1)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        borderWidth: 2,
                        tension: 0.3,
                        fill: true,
                        hidden: true
                    },
                    {
                        label: '理想i8',
                        data: modelTrendData.i8,
                        borderColor: 'rgba(201, 203, 207, 1)',
                        backgroundColor: 'rgba(201, 203, 207, 0.1)',
                        borderWidth: 2,
                        tension: 0.3,
                        fill: true,
                        hidden: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                label += context.raw.toLocaleString() + '辆';
                                return label;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        title: {
                            display: true,
                            text: '销量(辆)'
                        }
                    }
                }
            }
        });

        // Filter Button Events
        document.querySelectorAll('.filter-btn').forEach(button => {
            button.addEventListener('click', () => {
                document.querySelectorAll('.filter-btn').forEach(btn => {
                    btn.classList.remove('bg-blue-100', 'text-blue-600');
                    btn.classList.add('bg-gray-100', 'text-gray-600');
                });

                button.classList.remove('bg-gray-100', 'text-gray-600');
                button.classList.add('bg-blue-100', 'text-blue-600');

                const filter = button.getAttribute('data-filter');

                if (filter === 'volume') {
                    marketTrendChart.data.datasets = [{
                        label: '销量',
                        data: marketVolume,
                        backgroundColor: 'rgba(59, 130, 246, 0.7)',
                        borderColor: 'rgba(59, 130, 246, 1)',
                        borderWidth: 1,
                        yAxisID: 'y'
                    }];
                    marketTrendChart.options.scales.y.title.text = '销量(辆)';
                    marketTrendChart.options.scales.y.ticks.callback = function(value) {
                        return (value / 10000) + '万';
                    };
                } else if (filter === 'yoy') {
                    marketTrendChart.data.datasets = [{
                        label: '同比',
                        data: marketYoy,
                        backgroundColor: 'rgba(16, 185, 129, 0.7)',
                        borderColor: 'rgba(16, 185, 129, 1)',
                        borderWidth: 1,
                        yAxisID: 'y1'
                    }];
                    marketTrendChart.options.scales.y.title.text = '同比增长(%)';
                    marketTrendChart.options.scales.y.ticks.callback = function(value) {
                        return value + '%';
                    };
                } else if (filter === 'mom') {
                    marketTrendChart.data.datasets = [{
                        label: '环比',
                        data: marketMom,
                        backgroundColor: 'rgba(124, 58, 237, 0.7)',
                        borderColor: 'rgba(124, 58, 237, 1)',
                        borderWidth: 1,
                        yAxisID: 'y1'
                    }];
                    marketTrendChart.options.scales.y.title.text = '环比增长(%)';
                    marketTrendChart.options.scales.y.ticks.callback = function(value) {
                        return value + '%';
                    };
                }

                marketTrendChart.update();
            });
        });

        // Brand Filter Button Events
        document.querySelectorAll('.brand-filter-btn').forEach(button => {
            button.addEventListener('click', () => {
                document.querySelectorAll('.brand-filter-btn').forEach(btn => {
                    btn.classList.remove('bg-blue-100', 'text-blue-600');
                    btn.classList.add('bg-gray-100', 'text-gray-600');
                });

                button.classList.remove('bg-gray-100', 'text-gray-600');
                button.classList.add('bg-blue-100', 'text-blue-600');

                const filter = button.getAttribute('data-filter');

                if (filter === 'volume') {
                    brandTrendChart.data.datasets = [{
                        label: '销量',
                        data: brandVolume,
                        backgroundColor: 'rgba(59, 130, 246, 0.7)',
                        borderColor: 'rgba(59, 130, 246, 1)',
                        borderWidth: 1,
                        yAxisID: 'y'
                    }];
                    brandTrendChart.options.scales.y.title.text = '销量(辆)';
                } else if (filter === 'yoy') {
                    brandTrendChart.data.datasets = [{
                        label: '同比',
                        data: brandYoy,
                        backgroundColor: 'rgba(16, 185, 129, 0.7)',
                        borderColor: 'rgba(16, 185, 129, 1)',
                        borderWidth: 1,
                        yAxisID: 'y1'
                    }];
                    brandTrendChart.options.scales.y.title.text = '同比增长(%)';
                } else if (filter === 'mom') {
                    brandTrendChart.data.datasets = [{
                        label: '环比',
                        data: brandMom,
                        backgroundColor: 'rgba(124, 58, 237, 0.7)',
                        borderColor: 'rgba(124, 58, 237, 1)',
                        borderWidth: 1,
                        yAxisID: 'y1'
                    }];
                    brandTrendChart.options.scales.y.title.text = '环比增长(%)';
                }

                brandTrendChart.update();
            });
        });

        // Model Filter Button Events
        document.querySelectorAll('.model-filter-btn').forEach(button => {
            button.addEventListener('click', () => {
                document.querySelectorAll('.model-filter-btn').forEach(btn => {
                    btn.classList.remove('bg-blue-100', 'text-blue-600');
                    btn.classList.add('bg-gray-100', 'text-gray-600');
                });

                button.classList.remove('bg-gray-100', 'text-gray-600');
                button.classList.add('bg-blue-100', 'text-blue-600');

                const model = button.getAttribute('data-model');

                modelTrendChart.data.datasets.forEach(dataset => {
                    dataset.hidden = true;
                });

                if (model === 'all') {
                    modelTrendChart.data.datasets.forEach(dataset => {
                        dataset.hidden = false;
                    });
                } else {
                    const modelIndex = {
                        'l6': 0,
                        'l7': 1,
                        'l8': 2,
                        'l9': 3,
                        'mega': 4,
                        'i8': 5
                    }[model];

                    if (modelIndex !== undefined) {
                        modelTrendChart.data.datasets[modelIndex].hidden = false;
                    }
                }

                modelTrendChart.update();
            });
        });
    </script>
</body>
</html>