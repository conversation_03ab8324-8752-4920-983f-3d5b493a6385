<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>理想汽车科技数据大屏</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: #0a0e27;
            color: #fff;
            overflow: hidden;
        }

        .container {
            width: 100vw;
            height: 100vh;
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 80px 1fr 1fr;
            gap: 20px;
        }

        .header {
            grid-column: 1 / -1;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: linear-gradient(135deg, #1a1f3a 0%, #0f1320 100%);
            border-radius: 10px;
            padding: 0 40px;
            box-shadow: 0 4px 20px rgba(0, 123, 255, 0.1);
        }

        .header h1 {
            font-size: 28px;
            background: linear-gradient(90deg, #00d4ff 0%, #0099ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .time {
            font-size: 18px;
            color: #00d4ff;
        }

        .chart-box {
            background: linear-gradient(135deg, #1a1f3a 0%, #0f1320 100%);
            border-radius: 10px;
            padding: 20px;
            position: relative;
            box-shadow: 0 4px 20px rgba(0, 123, 255, 0.1);
            display: flex;
            flex-direction: column;
        }

        .chart-title {
            font-size: 18px;
            margin-bottom: 10px;
            color: #00d4ff;
        }

        .chart-container {
            flex: 1;
            position: relative;
        }

        .btn-group {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            z-index: 10;
        }

        .btn {
            padding: 6px 16px;
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid #00d4ff;
            border-radius: 4px;
            color: #00d4ff;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
        }

        .btn:hover {
            background: rgba(0, 212, 255, 0.2);
            transform: translateY(-1px);
        }

        .btn.active {
            background: #00d4ff;
            color: #0a0e27;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 20px;
        }

        .stat-card {
            background: rgba(0, 212, 255, 0.05);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #00d4ff;
        }

        .stat-label {
            font-size: 14px;
            color: #8892b0;
            margin-top: 5px;
        }

        .glow {
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from {
                box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
            }
            to {
                box-shadow: 0 0 20px rgba(0, 212, 255, 0.8);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>理想汽车 · 新能源市场数据大屏</h1>
            <div class="time" id="currentTime"></div>
        </div>

        <div class="chart-box">
            <h2 class="chart-title">新能源整体市场销量趋势</h2>
            <div class="btn-group">
                <button class="btn active" onclick="switchChart('新能源')">销量</button>
                <button class="btn" onclick="switchChart('新能源同比')">同比</button>
                <button class="btn" onclick="switchChart('新能源环比')">环比</button>
            </div>
            <div id="newEnergyChart" class="chart-container"></div>
        </div>

        <div class="chart-box">
            <h2 class="chart-title">理想品牌销量趋势</h2>
            <div class="btn-group">
                <button class="btn active" onclick="switchChart2('理想销量')">销量</button>
                <button class="btn" onclick="switchChart2('理想同比')">同比</button>
                <button class="btn" onclick="switchChart2('理想环比')">环比</button>
            </div>
            <div id="liXiangChart" class="chart-container"></div>
        </div>

        <div class="chart-box">
            <h2 class="chart-title">25年6月理想各车型销量占比</h2>
            <div id="modelChart" class="chart-container"></div>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">16,296</div>
                    <div class="stat-label">理想L6 销量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">45.5%</div>
                    <div class="stat-label">理想L6 占比</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">35,844</div>
                    <div class="stat-label">总销量</div>
                </div>
            </div>
        </div>

        <div class="chart-box">
            <h2 class="chart-title">实时数据监控</h2>
            <div id="realtimeChart" class="chart-container"></div>
        </div>
    </div>

    <script>
        // 更新时间
        function updateTime() {
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('currentTime').textContent = timeStr;
        }
        setInterval(updateTime, 1000);
        updateTime();

        // 数据准备
        const months = ['24年7月', '8月', '9月', '10月', '11月', '12月', '25年1月', '2月', '3月', '4月', '5月', '6月'];

        const newEnergyData = {
            销量: [882999, 1012070, 1105428, 1182133, 1242798, 1293189, 703643, 656305, 969226, 870670, 964767, 1109724],
            同比: [44.9, 48.8, 57.8, 64.7, 61.4, 45.2, 5.9, 74.7, 38.8, 29.3, 22.9, 33.3],
            环比: [6.1, 14.6, 9.2, 6.9, 5.1, 4.1, -45.6, -6.7, 47.7, -10.2, 10.8, 15.0]
        };

        const liXiangData = {
            销量: [50790, 47980, 52997, 50211, 50518, 58411, 30772, 26007, 36252, 34777, 44489, 35844],
            同比: [51.7, 41.6, 45.7, 25.2, 20.7, 15.9, -3.5, 24.2, 11.1, 28.4, 31.4, -24.6],
            环比: [6.9, -5.5, 10.5, -5.3, 0.6, 15.6, -47.3, -15.5, 39.4, -4.1, 27.9, -19.4]
        };

        // 新能源市场图表
        const newEnergyChart = echarts.init(document.getElementById('newEnergyChart'));
        let currentNewEnergyType = '销量';

        function updateNewEnergyChart(type) {
            const option = {
                backgroundColor: 'transparent',
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985'
                        }
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: months,
                    axisLine: {
                        lineStyle: {
                            color: '#8892b0'
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        lineStyle: {
                            color: '#8892b0'
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(136, 146, 176, 0.1)'
                        }
                    }
                },
                series: [{
                    name: type,
                    type: 'line',
                    smooth: true,
                    symbol: 'circle',
                    symbolSize: 8,
                    itemStyle: {
                        color: '#00d4ff'
                    },
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                            offset: 0,
                            color: 'rgba(0, 212, 255, 0.3)'
                        }, {
                            offset: 1,
                            color: 'rgba(0, 212, 255, 0.05)'
                        }])
                    },
                    data: newEnergyData[type]
                }]
            };
            newEnergyChart.setOption(option);
        }
        updateNewEnergyChart(currentNewEnergyType);

        // 理想品牌图表
        const liXiangChart = echarts.init(document.getElementById('liXiangChart'));
        let currentLiXiangType = '销量';

        function updateLiXiangChart(type) {
            const option = {
                backgroundColor: 'transparent',
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: months,
                    axisLine: {
                        lineStyle: {
                            color: '#8892b0'
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        lineStyle: {
                            color: '#8892b0'
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(136, 146, 176, 0.1)'
                        }
                    }
                },
                series: [{
                    name: type,
                    type: 'bar',
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                            offset: 0,
                            color: '#00d4ff'
                        }, {
                            offset: 1,
                            color: '#0066ff'
                        }])
                    },
                    data: liXiangData[type]
                }]
            };
            liXiangChart.setOption(option);
        }
        updateLiXiangChart(currentLiXiangType);

        // 车型占比饼图
        const modelChart = echarts.init(document.getElementById('modelChart'));
        const modelOption = {
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            series: [{
                name: '车型占比',
                type: 'pie',
                radius: ['40%', '70%'],
                avoidLabelOverlap: false,
                itemStyle: {
                    borderColor: '#0a0e27',
                    borderWidth: 2
                },
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: 20,
                        fontWeight: 'bold'
                    }
                },
                labelLine: {
                    show: false
                },
                data: [
                    {value: 4852, name: '理想L9', itemStyle: {color: '#00d4ff'}},
                    {value: 8056, name: '理想L7', itemStyle: {color: '#0099ff'}},
                    {value: 4335, name: '理想L8', itemStyle: {color: '#0066ff'}},
                    {value: 2294, name: '理想MEGA', itemStyle: {color: '#0033ff'}},
                    {value: 16296, name: '理想L6', itemStyle: {color: '#ff0066'}},
                    {value: 11, name: '理想i8', itemStyle: {color: '#6600ff'}}
                ]
            }]
        };
        modelChart.setOption(modelOption);

        // 实时数据图
        const realtimeChart = echarts.init(document.getElementById('realtimeChart'));
        let realtimeData = [];
        for (let i = 0; i < 10; i++) {
            realtimeData.push(Math.random() * 1000 + 500);
        }

        setInterval(() => {
            realtimeData.shift();
            realtimeData.push(Math.random() * 1000 + 500);
            updateRealtimeChart();
        }, 2000);

        function updateRealtimeChart() {
            const option = {
                backgroundColor: 'transparent',
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    top: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    axisLine: {
                        lineStyle: {
                            color: '#8892b0'
                        }
                    },
                    axisLabel: {
                        show: false
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        lineStyle: {
                            color: '#8892b0'
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(136, 146, 176, 0.1)'
                        }
                    },
                    axisLabel: {
                        show: false
                    }
                },
                series: [{
                    type: 'line',
                    smooth: true,
                    symbol: 'none',
                    sampling: 'average',
                    itemStyle: {
                        color: '#00ff88'
                    },
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                            offset: 0,
                            color: 'rgba(0, 255, 136, 0.3)'
                        }, {
                            offset: 1,
                            color: 'rgba(0, 255, 136, 0.05)'
                        }])
                    },
                    data: realtimeData
                }]
            };
            realtimeChart.setOption(option);
        }
        updateRealtimeChart();

        // 切换图表类型
        function switchChart(type) {
            document.querySelectorAll('#newEnergyChart ~ .btn-group .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            if (type === '新能源') updateNewEnergyChart('销量');
            else if (type === '新能源同比') updateNewEnergyChart('同比');
            else if (type === '新能源环比') updateNewEnergyChart('环比');
        }

        function switchChart2(type) {
            document.querySelectorAll('#liXiangChart ~ .btn-group .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            if (type === '理想销量') updateLiXiangChart('销量');
            else if (type === '理想同比') updateLiXiangChart('同比');
            else if (type === '理想环比') updateLiXiangChart('环比');
        }

        // 响应式
        window.addEventListener('resize', () => {
            newEnergyChart.resize();
            liXiangChart.resize();
            modelChart.resize();
            realtimeChart.resize();
        });
    </script>
</body>
</html>