# PPT翻译工具使用说明

## 功能描述
这个工具可以将PPT文件中的中文内容翻译成英文，支持：
- 文本框内容翻译
- 表格内容翻译
- 自动检测中文内容
- 保持原有格式和布局
- 调用大模型API进行高质量翻译

## 安装依赖
```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 直接运行
```bash
python ppt_translator.py
```

### 2. 自定义参数
```python
from ppt_translator import PPTTranslator

# 配置信息
API_ENDPOINT = "https://yunwu.ai/v1/chat/completions"
API_KEY = "your_api_key"
MODEL_NAME = "gpt-4o-mini"

# 创建翻译器实例
translator = PPTTranslator(API_ENDPOINT, API_KEY, MODEL_NAME)

# 执行翻译
input_file = "your_ppt_file.pptx"
output_file = "translated_ppt_file.pptx"
success = translator.translate_ppt(input_file, output_file)
```

## 配置说明
- `API_ENDPOINT`: 大模型API端点
- `API_KEY`: API密钥
- `MODEL_NAME`: 使用的模型名称（默认：gpt-4o-mini）

## 注意事项
1. 确保PPT文件路径正确
2. 检查API密钥是否有效
3. 翻译过程中请保持网络连接
4. 大文件翻译可能需要较长时间
5. 翻译后的文件会保存为新文件，原文件不会被修改

## 错误处理
- 如果API调用失败，会自动重试3次
- 翻译失败的文本会保留原文
- 所有操作都有详细的日志记录 