import requests
import json
import base64
import urllib3
import warnings

# 禁用不安全请求的警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

API_KEY = "dUFmXtCOJBC02K53CR5oRaC7"
SECRET_KEY = "0CPeOAaHbjpEYHBpkfk3y5i1HAgJ7Ulr"


def read_pdf_file(file_path):
    """
    读取PDF文件并转换为base64编码
    :param file_path: PDF文件路径
    :return: base64编码的字符串
    """
    try:
        with open(file_path, 'rb') as file:
            pdf_content = file.read()
            return base64.b64encode(pdf_content).decode('utf-8')
    except Exception as e:
        print(f"读取文件失败: {str(e)}")
        return None


def main():
    file_path = r"C:\Users\<USER>\Desktop\OTA监测报告-25年6月-行业总览+用户心声.pdf"
    pdf_content = read_pdf_file(file_path)

    if pdf_content is None:
        print("文件读取失败")
        return

    url = "https://aip.baidubce.com/rpc/2.0/mt/v2/doc-translation/create?access_token=" + get_access_token()

    payload = json.dumps({
        "from": "auto",
        "to": "en",
        "input": {
            "format": "pptx", #pptx
            "content": pdf_content,
            "term_ids":"wmg5ovvq2d"
        },
        "output": {
            "formats": [
                "pptx"
            ]
        }
    })

    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }

    try:
        response = requests.request("POST", url, headers=headers, data=payload, verify=False)
        result = response.json()
        print("翻译任务创建成功，任务ID：", result.get('result')["id"])
        print("完整响应：", response.text)
    except Exception as e:
        print(f"请求失败: {str(e)}")


def get_access_token():
    """
    使用 AK，SK 生成鉴权签名（Access Token）
    :return: access_token，或是None(如果错误)
    """
    url = "https://aip.baidubce.com/oauth/2.0/token"
    params = {"grant_type": "client_credentials", "client_id": API_KEY, "client_secret": SECRET_KEY}
    return str(requests.post(url, params=params, verify=False).json().get("access_token"))


if __name__ == '__main__':
    main()
