<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新能源市场分析看板</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <style>
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .gradient-bg {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }
        .tooltip {
            position: relative;
            display: inline-block;
        }
        .tooltip .tooltiptext {
            visibility: hidden;
            width: 200px;
            background-color: #555;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 5px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -100px;
            opacity: 0;
            transition: opacity 0.3s;
        }
        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
    </style>
</head>
<body class="gradient-bg min-h-screen font-sans">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <header class="mb-8 text-center">
            <h1 class="text-3xl md:text-4xl font-bold text-gray-800 mb-2">新能源市场分析看板</h1>
            <p class="text-gray-600">2024年7月 - 2025年6月 市场趋势与品牌表现</p>
            <div class="flex justify-center mt-4">
                <span class="bg-blue-100 text-blue-800 text-sm font-medium px-3 py-1 rounded-full mr-2">最新数据</span>
                <span class="bg-green-100 text-green-800 text-sm font-medium px-3 py-1 rounded-full">更新日期: 2025年7月</span>
            </div>
        </header>

        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-xl shadow-md overflow-hidden transition duration-300 card-hover">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-50 text-blue-600 mr-4">
                            <i class="fas fa-chart-line text-xl"></i>
                        </div>
                        <div>
                            <p class="text-gray-500 text-sm">新能源市场总销量</p>
                            <h3 class="text-2xl font-bold text-gray-800">1,109,724</h3>
                            <p class="text-green-500 text-sm font-medium">+33.3% YoY</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-md overflow-hidden transition duration-300 card-hover">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-purple-50 text-purple-600 mr-4">
                            <i class="fas fa-car text-xl"></i>
                        </div>
                        <div>
                            <p class="text-gray-500 text-sm">理想品牌总销量</p>
                            <h3 class="text-2xl font-bold text-gray-800">35,844</h3>
                            <p class="text-red-500 text-sm font-medium">-24.6% YoY</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-md overflow-hidden transition duration-300 card-hover">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-50 text-yellow-600 mr-4">
                            <i class="fas fa-percentage text-xl"></i>
                        </div>
                        <div>
                            <p class="text-gray-500 text-sm">理想市场份额</p>
                            <h3 class="text-2xl font-bold text-gray-800">3.23%</h3>
                            <p class="text-red-500 text-sm font-medium">-2.3% MoM</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Market Trend Section -->
        <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8 transition duration-300 card-hover">
            <div class="p-6">
                <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
                    <h2 class="text-xl font-bold text-gray-800 mb-2 md:mb-0">新能源整体市场销量趋势</h2>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 bg-blue-100 text-blue-600 rounded-full text-sm font-medium">销量</button>
                        <button class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm font-medium">同比</button>
                        <button class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm font-medium">环比</button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="marketTrendChart"></canvas>
                </div>
                <div class="mt-4 text-sm text-gray-600">
                    <p><span class="font-medium">分析要点:</span> 新能源市场在2024年下半年持续增长，12月达到峰值1,293,189辆。2025年初受季节性因素影响大幅回落，但6月已回升至1,109,724辆，同比增长33.3%。</p>
                </div>
            </div>
        </div>

        <!-- Li Auto Trend Section -->
        <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8 transition duration-300 card-hover">
            <div class="p-6">
                <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
                    <h2 class="text-xl font-bold text-gray-800 mb-2 md:mb-0">理想品牌销量趋势</h2>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 bg-purple-100 text-purple-600 rounded-full text-sm font-medium">销量</button>
                        <button class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm font-medium">同比</button>
                        <button class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm font-medium">环比</button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="liAutoTrendChart"></canvas>
                </div>
                <div class="mt-4 text-sm text-gray-600">
                    <p><span class="font-medium">分析要点:</span> 理想品牌在2024年12月达到峰值58,411辆后，2025年1月大幅下滑47.3%。尽管5月有所回升至44,489辆，但6月再次下滑至35,844辆，同比减少24.6%，表现弱于整体市场。</p>
                </div>
            </div>
        </div>

        <!-- Model Distribution Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <div class="bg-white rounded-xl shadow-md overflow-hidden transition duration-300 card-hover">
                <div class="p-6">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">2025年6月理想各车型销量占比</h2>
                    <div class="chart-container">
                        <canvas id="modelDistributionChart"></canvas>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-md overflow-hidden transition duration-300 card-hover">
                <div class="p-6">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">车型销量明细</h2>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">车型</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">销量</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">占比</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">理想L6</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">16,296</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">45.5%</td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">理想L7</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">8,056</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">22.5%</td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">理想L9</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">4,852</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">13.5%</td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">理想L8</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">4,335</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">12.1%</td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">理想MEGA</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2,294</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">6.4%</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-4 text-sm text-gray-600">
                        <p><span class="font-medium">分析要点:</span> 理想L6成为绝对主力车型，占比45.5%；L7和L9/L8系列合计占比48.1%，MEGA车型占比仅6.4%，表现不及预期。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Key Insights Section -->
        <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8 transition duration-300 card-hover">
            <div class="p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">核心洞察</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <div class="flex items-start">
                            <div class="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-blue-800">市场整体表现强劲</h3>
                                <p class="mt-1 text-sm text-blue-700">新能源市场连续12个月同比增长，2025年6月销量达110.9万辆，同比增长33.3%，显示市场持续扩张态势。</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-purple-50 p-4 rounded-lg">
                        <div class="flex items-start">
                            <div class="flex-shrink-0 h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center text-purple-600">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-purple-800">理想品牌增长乏力</h3>
                                <p class="mt-1 text-sm text-purple-700">2025年6月理想销量同比下滑24.6%，远低于市场平均水平，市场份额从2024年12月的4.5%降至3.2%。</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-green-50 p-4 rounded-lg">
                        <div class="flex items-start">
                            <div class="flex-shrink-0 h-10 w-10 rounded-full bg-green-100 flex items-center justify-center text-green-600">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-green-800">L6成为销量支柱</h3>
                                <p class="mt-1 text-sm text-green-700">理想L6单车型贡献品牌45.5%的销量，但高端车型(L9/L8)和MEGA表现平平，产品结构失衡。</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-yellow-50 p-4 rounded-lg">
                        <div class="flex items-start">
                            <div class="flex-shrink-0 h-10 w-10 rounded-full bg-yellow-100 flex items-center justify-center text-yellow-600">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-yellow-800">季节性波动明显</h3>
                                <p class="mt-1 text-sm text-yellow-700">2025年1月新能源市场销量环比下滑45.6%，理想下滑47.3%，显示行业受春节等因素影响显著。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="text-center text-gray-500 text-sm py-6">
            <p>数据来源: 新能源市场监测系统 | 分析日期: 2025年7月</p>
            <p class="mt-1">© 2025 市场研究团队. 保留所有权利.</p>
        </footer>
    </div>

    <script>
        // Market Trend Chart
        const marketCtx = document.getElementById('marketTrendChart').getContext('2d');
        const marketTrendChart = new Chart(marketCtx, {
            type: 'line',
            data: {
                labels: ['24年7月', '8月', '9月', '10月', '11月', '12月', '25年1月', '2月', '3月', '4月', '5月', '6月'],
                datasets: [
                    {
                        label: '销量 (辆)',
                        data: [882999, 1012070, 1105428, 1182133, 1242798, 1293189, 703643, 656305, 969226, 870670, 964767, 1109724],
                        borderColor: 'rgba(59, 130, 246, 1)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        borderWidth: 2,
                        tension: 0.3,
                        yAxisID: 'y'
                    },
                    {
                        label: '同比 (%)',
                        data: [44.9, 48.8, 57.8, 64.7, 61.4, 45.2, 5.9, 74.7, 38.8, 29.3, 22.9, 33.3],
                        borderColor: 'rgba(16, 185, 129, 1)',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        borderWidth: 2,
                        borderDash: [5, 5],
                        tension: 0.3,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                    }
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: '销量 (辆)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: '同比 (%)'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                        min: 0
                    }
                }
            }
        });

        // Li Auto Trend Chart
        const liAutoCtx = document.getElementById('liAutoTrendChart').getContext('2d');
        const liAutoTrendChart = new Chart(liAutoCtx, {
            type: 'line',
            data: {
                labels: ['24年7月', '8月', '9月', '10月', '11月', '12月', '25年1月', '2月', '3月', '4月', '5月', '6月'],
                datasets: [
                    {
                        label: '销量 (辆)',
                        data: [50790, 47980, 52997, 50211, 50518, 58411, 30772, 26007, 36252, 34777, 44489, 35844],
                        borderColor: 'rgba(139, 92, 246, 1)',
                        backgroundColor: 'rgba(139, 92, 246, 0.1)',
                        borderWidth: 2,
                        tension: 0.3,
                        yAxisID: 'y'
                    },
                    {
                        label: '同比 (%)',
                        data: [51.7, 41.6, 45.7, 25.2, 20.7, 15.9, -3.5, 24.2, 11.1, 28.4, 31.4, -24.6],
                        borderColor: 'rgba(236, 72, 153, 1)',
                        backgroundColor: 'rgba(236, 72, 153, 0.1)',
                        borderWidth: 2,
                        borderDash: [5, 5],
                        tension: 0.3,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                    }
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: '销量 (辆)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: '同比 (%)'
                        },
                        grid: {
                            drawOnChartArea: false,
                        }
                    }
                }
            }
        });

        // Model Distribution Chart
        const modelCtx = document.getElementById('modelDistributionChart').getContext('2d');
        const modelDistributionChart = new Chart(modelCtx, {
            type: 'doughnut',
            data: {
                labels: ['理想L6', '理想L7', '理想L9', '理想L8', '理想MEGA', '理想i8'],
                datasets: [{
                    data: [16296, 8056, 4852, 4335, 2294, 11],
                    backgroundColor: [
                        'rgba(99, 102, 241, 0.8)',
                        'rgba(139, 92, 246, 0.8)',
                        'rgba(217, 70, 239, 0.8)',
                        'rgba(236, 72, 153, 0.8)',
                        'rgba(244, 63, 94, 0.8)',
                        'rgba(120, 113, 108, 0.8)'
                    ],
                    borderColor: [
                        'rgba(99, 102, 241, 1)',
                        'rgba(139, 92, 246, 1)',
                        'rgba(217, 70, 239, 1)',
                        'rgba(236, 72, 153, 1)',
                        'rgba(244, 63, 94, 1)',
                        'rgba(120, 113, 108, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.label || '';
                                let value = context.raw || 0;
                                let total = context.dataset.data.reduce((a, b) => a + b, 0);
                                let percentage = Math.round((value / total) * 100);
                                return `${label}: ${value}辆 (${percentage}%)`;
                            }
                        }
                    }
                },
                cutout: '65%'
            }
        });
    </script>
</body>
</html>