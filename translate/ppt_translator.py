#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPT翻译工具
用于将PPT中的中文内容翻译成英文
"""

import os
import re
import json
import time
import requests
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PPTTranslator:
    def __init__(self, api_endpoint, api_key, model_name="gpt-4o-mini"):
        """
        初始化PPT翻译器
        
        Args:
            api_endpoint: API端点
            api_key: API密钥
            model_name: 模型名称
        """
        self.api_endpoint = api_endpoint
        self.api_key = api_key
        self.model_name = model_name
        self.session = requests.Session()
        
    def is_chinese_text(self, text):
        """
        检测文本是否包含中文
        
        Args:
            text: 待检测的文本
            
        Returns:
            bool: 是否包含中文
        """
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        return bool(chinese_pattern.search(text))
    
    def translate_text(self, text, max_retries=3, delay=1):
        """
        调用大模型API翻译文本
        
        Args:
            text: 待翻译的文本
            max_retries: 最大重试次数
            delay: 重试间隔（秒）
            
        Returns:
            str: 翻译后的文本
        """
        if not text.strip():
            return text
            
        # 如果不包含中文，直接返回
        if not self.is_chinese_text(text):
            return text
            
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            "model": self.model_name,
            "messages": [
                {
                    "role": "system",
                    "content": "你是一个专业的翻译助手。请将中文翻译成英文，保持原文的格式和风格。如果遇到专业术语，请使用准确的英文表达。只返回翻译结果，不要添加额外的解释。"
                },
                {
                    "role": "user",
                    "content": f"请将以下中文翻译成英文：\n{text}"
                }
            ],
            "temperature": 0.3,
            "max_tokens": 2000
        }
        
        for attempt in range(max_retries):
            try:
                response = self.session.post(
                    self.api_endpoint,
                    headers=headers,
                    json=data,
                    timeout=30
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if 'choices' in result and len(result['choices']) > 0:
                        translated_text = result['choices'][0]['message']['content'].strip()
                        logger.info(f"翻译成功: {text[:50]}... -> {translated_text[:50]}...")
                        return translated_text
                    else:
                        logger.error(f"API响应格式错误: {result}")
                else:
                    logger.error(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"请求异常: {e}")
                
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析错误: {e}")
                
            if attempt < max_retries - 1:
                logger.info(f"第{attempt + 1}次尝试失败，{delay}秒后重试...")
                time.sleep(delay)
                delay *= 2  # 指数退避
                
        logger.error(f"翻译失败，已达到最大重试次数: {text}")
        return text  # 翻译失败时返回原文
    
    def process_text_frame(self, text_frame):
        """
        处理文本框中的文本
        
        Args:
            text_frame: 文本框对象
        """
        if not text_frame.text.strip():
            return
            
        # 获取原始文本
        original_text = text_frame.text
        
        # 如果包含中文，进行翻译
        if self.is_chinese_text(original_text):
            translated_text = self.translate_text(original_text)
            
            # 替换文本内容
            text_frame.clear()
            p = text_frame.paragraphs[0]
            run = p.runs[0] if p.runs else p.add_run()
            run.text = translated_text
            
            logger.info(f"文本已翻译并替换")
    
    def process_table(self, table):
        """
        处理表格中的文本
        
        Args:
            table: 表格对象
        """
        for row in table.rows:
            for cell in row.cells:
                if cell.text_frame:
                    self.process_text_frame(cell.text_frame)
    
    def process_slide(self, slide, slide_num):
        """
        处理单个幻灯片
        
        Args:
            slide: 幻灯片对象
            slide_num: 幻灯片编号
        """
        logger.info(f"正在处理第{slide_num}张幻灯片...")
        
        # 处理幻灯片中的所有形状
        for shape in slide.shapes:
            try:
                # 处理文本框
                if hasattr(shape, 'text_frame') and shape.text_frame:
                    self.process_text_frame(shape.text_frame)
                
                # 处理表格
                elif hasattr(shape, 'table') and shape.table:
                    self.process_table(shape.table)
                
                # 处理图表（如果有文本）
                elif hasattr(shape, 'chart') and shape.chart:
                    # 图表中的文本处理比较复杂，这里简化处理
                    pass
                    
            except Exception as e:
                logger.error(f"处理形状时出错: {e}")
                continue
    
    def translate_ppt(self, input_file, output_file=None):
        """
        翻译PPT文件
        
        Args:
            input_file: 输入PPT文件路径
            output_file: 输出PPT文件路径（如果不指定，则覆盖原文件）
        """
        if not os.path.exists(input_file):
            logger.error(f"输入文件不存在: {input_file}")
            return False
            
        try:
            # 加载PPT文件
            logger.info(f"正在加载PPT文件: {input_file}")
            prs = Presentation(input_file)
            
            # 处理每一张幻灯片
            total_slides = len(prs.slides)
            logger.info(f"总共有{total_slides}张幻灯片")
            
            for i, slide in enumerate(prs.slides, 1):
                self.process_slide(slide, i)
                
                # 显示进度
                progress = (i / total_slides) * 100
                logger.info(f"进度: {progress:.1f}% ({i}/{total_slides})")
            
            # 保存文件
            if output_file is None:
                output_file = input_file
                
            logger.info(f"正在保存翻译后的PPT文件: {output_file}")
            prs.save(output_file)
            
            logger.info("PPT翻译完成！")
            return True
            
        except Exception as e:
            logger.error(f"翻译PPT时出错: {e}")
            return False

def main():
    """主函数"""
    # 配置信息
    API_ENDPOINT = "https://yunwu.ai/v1/chat/completions"
    API_KEY = "sk-dwJRpPz5CpK57sJA08bvBbHJ40PKCa4IbSOi0YiPiqtYKpTY"
    MODEL_NAME = "gpt-4o-mini"
    
    # 文件路径
    input_file = r"C:\Users\<USER>\Desktop\OTA监测报告-25年6月-行业总览+用户心声.pptx"
    
    # 生成输出文件名（在原文件名后添加_translated）
    file_dir = os.path.dirname(input_file)
    file_name = os.path.basename(input_file)
    name, ext = os.path.splitext(file_name)
    output_file = os.path.join(file_dir, f"{name}_translated{ext}")
    
    # 创建翻译器实例
    translator = PPTTranslator(API_ENDPOINT, API_KEY, MODEL_NAME)
    
    # 执行翻译
    success = translator.translate_ppt(input_file, output_file)
    
    if success:
        print(f"翻译成功！输出文件: {output_file}")
    else:
        print("翻译失败！")

if __name__ == "__main__":
    main() 