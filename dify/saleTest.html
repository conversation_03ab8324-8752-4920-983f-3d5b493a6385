<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title>Solar System Simulation</title>
<style>
    body { margin: 0; overflow: hidden; background-color: black; color: white; }
    #solar-system { position: relative; width: 100vw; height: 100vh; }
    .planet, .sun { border-radius: 50%; position: absolute; top: 50%; left: 50%; transform-origin: -50% -50%; }
    .sun { width: 30px; height: 30px; background-color: yellow; box-shadow: 0 0 20px rgba(255,255,0,0.7); animation: pulse 2s infinite; }
    @keyframes pulse { 0%, 100% { transform: scale(1); } 50% { transform: scale(1.2); } }
    .orbit { position: absolute; border: 1px dashed rgba(255,255,255,0.3); border-radius: 50%; }
    .info-panel { display:none; position:absolute; bottom:10px; right:10px; padding:10px; background-color:rgba(0,0,0,0.8); }
</style>
</head>
<body>
<div id="solar-system"></div>
<div class="info-panel" id="info-panel">Planet Info</div>

<script>
const planets = [
    {name:"Mercury", distance:60, size:5, speed:5},
    {name:"Venus", distance:90, size:10, speed:4},
    {name:"Earth", distance:120, size:10, speed:3, moon:{distance:15, size:3, speed:6}},
    {name:"Mars", distance:150, size:8, speed:2.5},
    {name:"Jupiter", distance:200, size:20, speed:1.8, moons:[{distance:25, size:6, speed:8},{distance:35, size:4, speed:10},{distance:45, size:3, speed:12}]},
    {name:"Saturn", distance:250, size:18, speed:1.6, rings:true},
    {name:"Uranus", distance:300, size:14, speed:1.2},
    {name:"Neptune", distance:350, size:14, speed:1}
];

let solarSystem = document.getElementById('solar-system');
let infoPanel = document.getElementById('info-panel');

function createPlanet(data) {
    let planetDiv = document.createElement('div');
    planetDiv.className = 'planet';
    planetDiv.style.width = `${data.size}px`;
    planetDiv.style.height = `${data.size}px`;
    planetDiv.style.backgroundColor = data.color || "white";
    if (data.moon) {
        createMoon(data.moon, planetDiv);
    } else if (data.moons) {
        data.moons.forEach(moon => createMoon(moon, planetDiv));
    }
    let orbit = document.createElement('div');
    orbit.className = 'orbit';
    orbit.style.width = `${data.distance * 2}px`;
    orbit.style.height = `${data.distance * 2}px`;
    solarSystem.appendChild(orbit).appendChild(planetDiv);
    return planetDiv;
}

function createMoon(moonData, parentPlanet) {
    let moonDiv = document.createElement('div');
    moonDiv.className = 'planet';
    moonDiv.style.width = `${moonData.size}px`;
    moonDiv.style.height = `${moonData.size}px`;
    moonDiv.style.backgroundColor = "grey";
    parentPlanet.appendChild(moonDiv);
    setInterval(() => {
        let angle = (new Date().getTime() / moonData.speed % 360) * Math.PI / 180;
        moonDiv.style.transform = `translate(${Math.cos(angle) * moonData.distance}px, ${Math.sin(angle) * moonData.distance}px)`;
    }, 10);
}

planets.forEach(planet => {
    let p = createPlanet(planet);
    setInterval(() => {
        let angle = (new Date().getTime() / planet.speed % 360) * Math.PI / 180;
        p.style.transform = `translate(${Math.cos(angle) * planet.distance}px, ${Math.sin(angle) * planet.distance}px)`;
    }, 10);
});

// 添加太阳
let sun = document.createElement('div');
sun.className = 'sun';
solarSystem.appendChild(sun);

// 星星背景（简单版本）
for(let i=0;i<100;i++) {
    let star = document.createElement('div');
    star.style.position = 'absolute';
    star.style.top = `${Math.random() * 100}vh`;
    star.style.left = `${Math.random() * 100}vw`;
    star.style.width = '2px';
    star.style.height = '2px';
    star.style.backgroundColor = 'white';
    solarSystem.appendChild(star);
}
</script>
</body>
</html>