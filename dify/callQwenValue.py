import os
from openai import OpenAI
import pandas as pd

client = OpenAI(
    # 若没有配置环境变量，请用百炼API Key将下行替换为：api_key="sk-xxx",
    api_key="sk-7b9067ef70794d7286da8432be28f18c",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)

# Excel文件路径
excel_path = r'C:\Users\<USER>\Desktop\IT Demo测试问题集.xlsx'

# 读取Excel文件
df = pd.read_excel(excel_path)

with open(excel_path, 'a', encoding='utf-8') as f:
    # 存储API返回数据的列表
    output_list = []

    # 遍历Excel中的【问题】列
    for index, row in df.iterrows():
        answer = row['标准答案']  # 假设【问题】列的名称为'问题'
        modelResult = row['qwen3-30b-a3b输出']
        question = f"标准答案：{answer}\n模型输出：{modelResult}"

        completion = client.chat.completions.create(
            model="qwen-max-latest",
            # 此处以qwen-plus为例，可按需更换模型名称。模型列表：https://help.aliyun.com/zh/model-studio/getting-started/models
            messages=[
                {'role': 'system', 'content': """### 角色
                                                    基于用于提给你的标准答案和模型输出进行打分，如果有扣分并注明扣分原因。只输出总得分和扣分原因，其他不做输出
                                                    
                                                    ### 打分规则设计
                                                    
                                                    #### 1. 评估维度
                                                    打分规则将从以下四个方面评估模型的拆分效果：
                                                    - **时间（Time）**：问题中与时间相关的信息（如日期、时间段）。
                                                    - **维度（Dimension）**：问题中涉及的分析维度（如品牌、车型、厂商）。
                                                    - **指标（Metric）**：问题中需要计算或展示的指标（如SX-销量、MIX、TP、折扣）。
                                                    - **筛选（Filter）**：问题中的筛选条件（如特定时间段、特定品牌）。
                                                    
                                                    #### 2. 评分标准
                                                    每个部分将基于以下标准评分：
                                                    - **10分（完全正确）**：模型准确提取了该部分的所有信息，无遗漏、无错误。
                                                    - **5分（部分正确）**：模型提取了该部分的部分信息，但存在遗漏或错误。
                                                    - **0分（完全错误或遗漏）**：模型未能提取该部分信息，或提取完全错误。
                                                    
                                                    #### 3. 权重分配
                                                    考虑到每个部分的重要性不同，我们为它们分配以下权重：
                                                    - **时间**：20%
                                                    - **维度**：20%
                                                    - **指标**：30%
                                                    - **筛选**：30%
                                                    
                                                    **权重分配理由**：
                                                    - **指标**和**筛选**是问题的核心，直接影响后续分析结果，因此权重较高。
                                                    - **时间**和**维度**虽然重要，但在某些问题中可能不是必须的，因此权重稍低。
                                                    
                                                    #### 4. 总分计算
                                                    每个部分的得分乘以对应权重后相加，总分范围为0到10分。
                                                    
                                                    **计算公式**：
                                                    \[
                                                    \text{总分} = (\text{时间得分} \times 0.2) + (\text{维度得分} \times 0.2) + (\text{指标得分} \times 0.3) + (\text{筛选得分} \times 0.3)
                                                    \]
                                                    
                                                    #### 5. 评分细则
                                                    以下是每个部分的详细评分标准，确保评分客观一致：
                                                    
                                                    - **时间（Time）**：
                                                      - **10分**：准确提取所有时间信息（如“2023年第三季度”或“过去7天”）。
                                                      - **5分**：提取部分时间信息，但遗漏细节（如只识别“2023年”，未识别“第三季度”）。
                                                      - **0分**：未能提取时间信息，或提取错误。
                                                        
                                                    - **维度（Dimension）**：
                                                      - **10分**：准确识别所有维度（如“车型、型号名称”和“品牌”）。
                                                      - **5分**：识别部分维度，但遗漏或错误（如只识别“车型、型号名称”，未识别“品牌”）。
                                                      - **0分**：未能识别任何维度，或识别完全错误。
                                                    
                                                    - **指标（Metric）**：
                                                      - **10分**：准确提取所有指标（如“SX-销量”）。
                                                      - **5分**：提取部分指标，但遗漏或错误。
                                                      - **0分**：未能提取任何指标，或提取错误。
                                                    
                                                    - **筛选（Filter）**：
                                                      - **10分**：准确识别所有筛选条件（如“城市=北京,上海，车型=小米SU7”）。
                                                      - **5分**：识别部分筛选条件，但遗漏或错误（如只识别“北京,上海”，未识别“小米SU7”）。
                                                      - **0分**：未能识别任何筛选条件，或识别错误。
                                                      注：
                                                      - 时间：如果用户问题中说的是24年各月或每月等，提取成了月=202401,202402,202403,202404,202405,202406,202407,202408,202409,202410,202411,202412，算完全正确，不扣分
                                                      - 维度：如果用户问题中说的是小米SU7或小鹏MONA 03，提取成了SU7或者MONA 03，算完全正确，不扣分
                                                      - 指标：如果用户问题中说的销量变化，提取成了SX-销量、SX-同比、SX-环比或者SX-销量或者SX-同比、SX-环比等，算完全正确，不扣分
                                                    
                                                    #### 6. 附加考虑
                                                    - **一致性**：拆分结果应与用户意图一致。若技术上正确但偏离意图，可酌情扣分。
                                                    - **清晰性**：拆分后的部分应清晰易懂。
                                                    - **实用性**：拆分结果应便于后续分析或查询。
                                                    
                                                    如有需要，可在总分中加入±1分的调整项，以反映这些附加因素。
                                                    
                                                    ---
                                                    
                                                    ### 示例说明
                                                    假设用户问题是：“我想知道2023年第三季度，北京和上海的智能手机销售额是多少？”
                                                    
                                                    #### 理想拆分：
                                                    - **时间**：2023年第三季度
                                                    - **维度**：地理位置（北京、上海），产品类别（智能手机）
                                                    - **指标**：销售额
                                                    - **筛选**：地理位置=北京和上海，产品类别=智能手机
                                                    
                                                    #### 模型拆分结果1：
                                                    - **时间**：2023年第三季度（10分）
                                                    - **维度**：地理位置（北京、上海）（遗漏“智能手机”，5分）
                                                    - **指标**：销售额（10分）
                                                    - **筛选**：地理位置=北京和上海（遗漏“智能手机”，5分）
                                                    
                                                    **总分** = (10 × 0.2) + (5 × 0.2) + (10 × 0.3) + (5 × 0.3) = 2 + 1 + 3 + 1.5 = **7.5分**
                                                    
                                                    #### 模型拆分结果2：
                                                    - **时间**：2023年（遗漏“第三季度”，5分）
                                                    - **维度**：地理位置（北京、上海），产品类别（智能手机）（10分）
                                                    - **指标**：销售额（10分）
                                                    - **筛选**：地理位置=北京和上海，产品类别=智能手机（10分）
                                                    
                                                    **总分** = (5 × 0.2) + (10 × 0.2) + (10 × 0.3) + (10 × 0.3) = 1 + 2 + 3 + 3 = **9分**
                                                    
                                                    #### 模型拆分结果3：
                                                    - **时间**：无（0分）
                                                    - **维度**：地理位置（北京、上海）（遗漏“智能手机”，5分）
                                                    - **指标**：无（0分）
                                                    - **筛选**：产品类别=智能手机（遗漏“北京和上海”，5分）
                                                    
                                                    **总分** = (0 × 0.2) + (5 × 0.2) + (0 × 0.3) + (5 × 0.3) = 0 + 1 + 0 + 1.5 = **2.5分**
                                                    
                                                    
                                                    标准答案：
                                                    time_field：年
                                                    dimension_field：燃料类型-1,厂商品牌
                                                    metrics：CPCA-零售量
                                                    filter_condition：年=2025,燃料类型-1=传统能源
                                                    
                                                    模型拆分结果：
                                                    time_field：年
                                                    dimension_field：燃料类型-1,厂商品牌
                                                    metrics：CPCA-零售量
                                                    filter_condition：年=2025
                                                """},
                {'role': 'user', 'content': question}],
        )

        # completion.model_dump_json()
        # output_list.append(completion['choices'][0]['message']['content'])
        # print(completion.__getstate__())
        response_content = completion.choices[0].message.content
        output_list.append(response_content)
        print("第" + str(index + 1) + "个问题请求成功！")
    # 添加新的列（输出）
    df['qwen3-30b-a3b评分结果'] = output_list

# 将DataFrame写入Excel文件
df.to_excel(excel_path, index=False)
print("数据处理完成，结果已存入Excel文件。")