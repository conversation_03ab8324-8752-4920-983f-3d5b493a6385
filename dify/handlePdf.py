from google import genai
import PIL.Image
import tempfile
import html2text
import os
from PyPDF2 import PdfReader
from pdf2image.exceptions import PDFPageCountError
import pdf2image


def verify_pdf(pdf_path):
    """验证PDF文件是否有效"""
    try:
        reader = PdfReader(pdf_path)
        return len(reader.pages) > 0
    except Exception as e:
        raise ValueError(f"无效的PDF文件: {str(e)}")

def pdf_to_images(pdf_path, output_dir):
    """将PDF每页转换为PNG图片"""
    if not os.path.exists(pdf_path):
        raise FileNotFoundError(f"找不到PDF文件: {pdf_path}")

    if not verify_pdf(pdf_path):
        raise ValueError("PDF文件验证失败")

    try:
        images = pdf2image.convert_from_path(
            pdf_path,
            dpi=200,
            output_folder=output_dir,
            fmt="png",
            thread_count=4,
            poppler_path=r"D:\GitHub\poppler-24.08.0\Library\bin"  # 确保Poppler路径正确
        )
        return [img.filename for img in images]
    except PDFPageCountError:
        raise ValueError("无法读取PDF页数，请确保文件未损坏")
    except Exception as e:
        raise Exception(f"PDF转换失败: {str(e)}")

# Gemini解析函数
def analyze_image(client, image_path):
    """使用Gemini解析图片内容"""
    image = PIL.Image.open(image_path)
    response = client.models.generate_content(
        model="gemini-2.5-pro-exp-03-25",
        contents=["请提取图片内容，按原始排版格式用规范的markdown输出", image]
    )
    return response.text


# 主处理流程
def process_pdf(pdf_path, output_md, api_key):
    # 初始化客户端
    client = genai.Client(api_key=api_key)

    # 创建临时目录
    with tempfile.TemporaryDirectory() as tmpdir:
        # 1. PDF转图片
        print("正在转换PDF为图片...")
        image_paths = pdf_to_images(pdf_path, tmpdir)

        # 2. 处理每张图片
        all_content = []
        converter = html2text.HTML2Text()

        for i, img_path in enumerate(image_paths, 1):
            print(f"正在解析第 {i}/{len(image_paths)} 页...")
            try:
                html = analyze_image(client, img_path)
                # 将HTML转换为Markdown
                markdown = converter.handle(html)
                all_content.append(f"## 第 {i} 页\n\n{markdown}\n\n---")
            except Exception as e:
                print(f"第 {i} 页解析失败: {str(e)}")
                all_content.append(f"## 第 {i} 页\n\n[解析失败]")

        # 3. 保存结果
        with open(output_md, "w", encoding="utf-8") as f:
            f.write("\n\n".join(all_content))
        print(f"结果已保存至：{output_md}")


if __name__ == "__main__":
    # 配置参数
    PDF_PATH = r"C:\Users\<USER>\Desktop\PDF测试样例\比亚迪-202503-B-关于第二代元PLUS智驾版上市营销指导的通知.pdf"  # 输入PDF路径
    OUTPUT_MD = r"C:\Users\<USER>\Desktop\PDF测试样例\比亚迪-202503-B-关于第二代元PLUS智驾版上市营销指导的通知.md"  # 输出Markdown路径
    API_KEY = "AIzaSyC_T7pVYvLqER5JQ-EMHXXFo6y6hxxH0_s"

    # 执行处理
    process_pdf(PDF_PATH, OUTPUT_MD, API_KEY)