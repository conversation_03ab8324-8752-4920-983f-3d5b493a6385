import pygame
import math

# 初始化pygame
pygame.init()

# 设置屏幕尺寸
width, height = 800, 600
screen = pygame.display.set_mode((width, height))
pygame.display.set_caption("2D Physics Simulation")

# 颜色定义
BLUE = (0, 0, 255)
GREEN = (0, 255, 0)
RED = (255, 0, 0)
PURPLE = (128, 0, 128)
GRAY = (200, 200, 200)

# 正六边形参数
hex_radius = 100
hex_center_left = (width // 4, height // 2)
hex_center_right = (3 * width // 4, height // 2)
rotation_speed = 0.01

# 小球参数
ball_radius = 10
ball_position = [(hex_center_left[0] + hex_center_right[0]) // 2,
                 (hex_center_left[1] + hex_center_right[1]) // 2]  # 改为列表
ball_velocity = [0, 0]
gravity = 0.1
elasticity = 0.9


# 计算正六边形顶点
def calculate_hex_vertices(center, radius):
    vertices = []
    for i in range(6):
        angle_deg = 60 * i - 30
        angle_rad = math.radians(angle_deg)
        x = center[0] + radius * math.cos(angle_rad)
        y = center[1] + radius * math.sin(angle_rad)
        vertices.append((x, y))
    return vertices


# 旋转点
def rotate_point(point, center, angle):
    s = math.sin(angle)
    c = math.cos(angle)
    px, py = point
    cx, cy = center
    # Translate point back to origin
    px -= cx
    py -= cy
    # Rotate point
    new_x = px * c - py * s
    new_y = px * s + py * c
    # Translate point back
    px = new_x + cx
    py = new_y + cy
    return (px, py)


# 检查碰撞并处理
def check_collision(ball_pos, ball_vel, hex_vertices):
    closest_distance = float('inf')
    collision_vertex = None
    collision_edge = None

    for i in range(len(hex_vertices)):
        v1 = hex_vertices[i]
        v2 = hex_vertices[(i + 1) % len(hex_vertices)]

        # 向量v1v2
        edge_vector = (v2[0] - v1[0], v2[1] - v1[1])
        # 向量v1p
        point_vector = (ball_pos[0] - v1[0], ball_pos[1] - v1[1])

        # 投影长度
        dot_product = point_vector[0] * edge_vector[0] + point_vector[1] * edge_vector[1]
        edge_length_squared = edge_vector[0] ** 2 + edge_vector[1] ** 2

        if edge_length_squared == 0:
            continue

        t = dot_product / edge_length_squared

        if t < 0:
            closest_point = v1
        elif t > 1:
            closest_point = v2
        else:
            closest_point = (v1[0] + t * edge_vector[0], v1[1] + t * edge_vector[1])

        distance = math.sqrt((closest_point[0] - ball_pos[0]) ** 2 + (closest_point[1] - ball_pos[1]) ** 2)

        if distance < closest_distance:
            closest_distance = distance
            collision_vertex = closest_point
            collision_edge = (v1, v2)

    if closest_distance <= ball_radius:
        # 计算法线向量
        normal_vector = (collision_vertex[0] - ball_pos[0], collision_vertex[1] - ball_pos[1])
        normal_magnitude = math.sqrt(normal_vector[0] ** 2 + normal_vector[1] ** 2)
        normal_vector = (normal_vector[0] / normal_magnitude, normal_vector[1] / normal_magnitude)

        # 计算相对速度
        relative_velocity = (ball_vel[0] * normal_vector[0] + ball_vel[1] * normal_vector[1]) * normal_vector

        # 弹性碰撞
        ball_vel[0] -= 2 * relative_velocity[0] * elasticity
        ball_vel[1] -= 2 * relative_velocity[1] * elasticity

        # 移动小球以避免穿透
        overlap = ball_radius - closest_distance
        ball_pos[0] += normal_vector[0] * overlap
        ball_pos[1] += normal_vector[1] * overlap

        # 计算切向力
        tangent_vector = (-normal_vector[1], normal_vector[0])
        tangential_velocity = (ball_vel[0] * tangent_vector[0] + ball_vel[1] * tangent_vector[1]) * tangent_vector

        # 应用力矩
        torque_factor = 0.1  # 调整此值以改变力矩的影响
        torque_effect = (tangential_velocity[0] * torque_factor, tangential_velocity[1] * torque_factor)
        ball_vel[0] += torque_effect[0]
        ball_vel[1] += torque_effect[1]


# 主循环
clock = pygame.time.Clock()
running = True
trajectory_points = []

while running:
    screen.fill((255, 255, 255))

    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False

    # 更新时间步长
    dt = clock.tick(60) / 1000.0

    # 更新小球位置
    ball_position[0] += ball_velocity[0] * dt
    ball_position[1] += ball_velocity[1] * dt
    ball_velocity[1] += gravity

    # 计算旋转角度
    left_angle = rotation_speed * pygame.time.get_ticks() / 1000.0
    right_angle = -rotation_speed * pygame.time.get_ticks() / 1000.0

    # 获取旋转后的正六边形顶点
    left_vertices = [rotate_point(v, hex_center_left, left_angle) for v in
                     calculate_hex_vertices(hex_center_left, hex_radius)]
    right_vertices = [rotate_point(v, hex_center_right, right_angle) for v in
                      calculate_hex_vertices(hex_center_right, hex_radius)]

    # 检测碰撞
    check_collision(ball_position, ball_velocity, left_vertices)
    check_collision(ball_position, ball_velocity, right_vertices)

    # 绘制轨迹
    trajectory_points.append((int(ball_position[0]), int(ball_position[1])))
    if len(trajectory_points) > 1000:
        trajectory_points.pop(0)
    for i in range(1, len(trajectory_points)):
        pygame.draw.line(screen, GRAY, trajectory_points[i - 1], trajectory_points[i], 1)

    # 绘制正六边形
    pygame.draw.polygon(screen, BLUE, left_vertices, 2)
    pygame.draw.polygon(screen, GREEN, right_vertices, 2)

    # 绘制重叠区域（简化为紫色背景）
    overlay_vertices = [
                           rotate_point(v, hex_center_left, left_angle) for v in
                           calculate_hex_vertices(hex_center_left, hex_radius)
                       ] + [
                           rotate_point(v, hex_center_right, right_angle) for v in
                           reversed(calculate_hex_vertices(hex_center_right, hex_radius))
                       ]
    pygame.draw.polygon(screen, PURPLE, overlay_vertices, 0)

    # 绘制小球
    pygame.draw.circle(screen, RED, (int(ball_position[0]), int(ball_position[1])), ball_radius)

    pygame.display.flip()

pygame.quit()



