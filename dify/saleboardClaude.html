<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>汽车销量分析看板</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        :root {
            --color1: #405DF9;
            --color2: #6C63F0;
            --color3: #7342CC;
            --color4: #B750BE;
            --color5: #ED589D;
            --color6: #FB466C;
            --color7: #FFC159;
            --text-color: rgba(0, 0, 0, 0.85);
            --divider: rgba(0, 0, 0, 0.20);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "HarmonyOS Sans SC", -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, Oxygen, Ubunt<PERSON>, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
            background-color: #f5f7fa;
            color: var(--text-color);
            font-size: 12px;
            letter-spacing: 0.6px;
            padding: 20px;
        }

        .dashboard {
            max-width: 1400px;
            margin: 0 auto;
        }

        .dashboard-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .dashboard-header h1 {
            font-size: 24px;
            margin-bottom: 10px;
            color: var(--color3);
        }

        .dashboard-header p {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.6);
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }

        .dashboard-row {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }

        .chart-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 15px;
            height: 350px;
        }

        .chart-container.wide {
            grid-column: span 2;
        }

        .chart-container.tall {
            height: 450px;
        }

        .chart-container.small {
            height: 250px;
        }

        .chart-title {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 10px;
            color: var(--text-color);
            text-align: center;
        }

        .chart {
            width: 100%;
            height: calc(100% - 30px);
        }

        .divider {
            height: 1px;
            background: var(--divider);
            margin: 15px 0;
        }

        .filters {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 20px;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .filter-label {
            font-size: 13px;
            color: var(--text-color);
        }

        select {
            padding: 5px 10px;
            border-radius: 5px;
            border: 1px solid var(--divider);
            font-family: inherit;
            font-size: 12px;
            background-color: white;
        }

        .kpi-container {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .kpi-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 15px;
            flex: 1;
            margin: 0 10px;
            text-align: center;
        }

        .kpi-title {
            font-size: 13px;
            color: rgba(0, 0, 0, 0.6);
            margin-bottom: 5px;
        }

        .kpi-value {
            font-size: 24px;
            font-weight: 600;
            color: var(--color3);
        }

        .kpi-change {
            font-size: 12px;
            margin-top: 5px;
        }

        .positive {
            color: #4CAF50;
        }

        .negative {
            color: #F44336;
        }

        @media (max-width: 1200px) {
            .dashboard-grid, .dashboard-row {
                grid-template-columns: 1fr;
            }

            .chart-container.wide {
                grid-column: span 1;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="dashboard-header">
            <h1>汽车销量分析看板</h1>
            <p>蔚来 VS 理想 销量数据分析 (2024年)</p>
        </div>

        <div class="kpi-container">
            <div class="kpi-card">
                <div class="kpi-title">总销量</div>
                <div class="kpi-value">42,268</div>
                <div class="kpi-change positive">↑ 23.5% 同比增长</div>
            </div>
            <div class="kpi-card">
                <div class="kpi-title">理想汽车销量</div>
                <div class="kpi-value">37,940</div>
                <div class="kpi-change positive">↑ 28.7% 同比增长</div>
            </div>
            <div class="kpi-card">
                <div class="kpi-title">蔚来汽车销量</div>
                <div class="kpi-value">4,328</div>
                <div class="kpi-change negative">↓ 3.2% 同比下降</div>
            </div>
            <div class="kpi-card">
                <div class="kpi-title">广东省销量占比</div>
                <div class="kpi-value">76.8%</div>
                <div class="kpi-change positive">↑ 5.3% 环比增长</div>
            </div>
        </div>

        <div class="dashboard-grid">
            <div class="chart-container">
                <div class="chart-title">各省份品牌销量对比 (2024Q4)</div>
                <div id="provinceBrandChart" class="chart"></div>
            </div>
            <div class="chart-container">
                <div class="chart-title">月度销量趋势 (2024年)</div>
                <div id="monthlyTrendChart" class="chart"></div>
            </div>
        </div>

        <div class="dashboard-row">
            <div class="chart-container">
                <div class="chart-title">品牌销量占比</div>
                <div id="brandPieChart" class="chart"></div>
            </div>
            <div class="chart-container">
                <div class="chart-title">广东省城市销量 (佛山市)</div>
                <div id="cityChart" class="chart"></div>
            </div>
            <div class="chart-container">
                <div class="chart-title">车型销量对比</div>
                <div id="carModelChart" class="chart"></div>
            </div>
        </div>

        <div class="dashboard-grid">
            <div class="chart-container wide tall">
                <div class="chart-title">月度销量与价格关系</div>
                <div id="priceVsSalesChart" class="chart"></div>
            </div>
        </div>
    </div>

    <script>
        // 初始化所有图表
        document.addEventListener('DOMContentLoaded', function() {
            // 数据处理
            const quarterlyData = [
                {season: '2024Q4', province: '山东省', brand: '蔚来', model: '蔚来ET7', sales: 32, price: 427295},
                {season: '2024Q4', province: '山东省', brand: '理想', model: '理想L6',588},
                {season: '2024Q4', province: '广东省', brand: '蔚来', model: '蔚来ET7', sales: 245, price: 427295},
                {season: '2024Q4', province: '广东省', brand: '理想', model: '理想L6', sales: 11683, price: 254588}
            ];

            const cityData = [
                {season: '2024Q4', province: '广东省', city: '佛山市', brand: '蔚来', sales: 649, price: 325547},
                {season: '2024Q4', province: '广东省', city: '佛山市', brand: '理想', sales: 2393, price: 308693}
            ];

            const monthlyData = [
                {month: '2024/03', province: '山东省', brand: '蔚来', model: '蔚来ET7', sales: 6, price: 410542},
                {month: '2024/03', province: '广东省', brand: '蔚来', model: '蔚来ET7', sales: 32, price: 410542},
                {month: '2024/03', province: '广东省', brand: '理想', model: '理想L6', sales: 4, price: null},
                {month: '2024/04', province: '山东省', brand: '蔚来', model: '蔚来ET7', sales: 10, price: 423157},
                {month: '2024/04', province: '山东省', brand: '理想', model: '理想L6', sales: 106, price: 256618},
                {month: '2024/04', province: '广东省', brand: '蔚来', model: '蔚来ET7', sales: 62, price: 412731},
                {month: '2024/04', province: '广东省', brand: '理想', model: '理想L6', sales: 1432, price: 256618},
                {month: '2024/05', province: '山东省', brand: '蔚来', model: '蔚来ET7', sales: 18, price: 424461},
                {month: '2024/05', province: '山东省', brand: '理想', model: '理想L6', sales: 700, price: 257189},
                {month: '2024/05', province: '广东省', brand: '蔚来', model: '蔚来ET7', sales: 134, price: 424461},
                {month: '2024/05', province: '广东省', brand: '理想', model: '理想L6', sales: 1666, price: 257189},
                {month: '2024/06', province: '山东省', brand: '蔚来', model: '蔚来ET7', sales: 11, price: 417080},
                {month: '2024/06', province: '山东省', brand: '理想', model: '理想L6', sales: 1273, price: 256433},
                {month: '2024/06', province: '广东省', brand: '蔚来', model: '蔚来ET7', sales: 124, price: 417080},
                {month: '2024/06', province: '广东省', brand: '理想', model: '理想L6', sales: 3598, price: 256433}
            ];

            const monthlyCityData = [
                {month: '2024/03', province: '广东省', city: '佛山市', brand: '蔚来', sales: 4, price: 410542},
                {month: '2024/04', province: '广东省', city: '佛山市', brand: '蔚来', sales: 4, price: 423157},
                {month: '2024/04', province: '广东省', city: '佛山市', brand: '理想', sales: 36, price: 256618},
                {month: '2024/05', province: '广东省', city: '佛山市', brand: '蔚来', sales: 18, price: 424461},
                {month: '2024/05', province: '广东省', city: '佛山市', brand: '理想', sales: 232, price: 257189},
                {month: '2024/06', province: '广东省', city: '佛山市', brand: '蔚来', sales: 20, price: 417080},
                {month: '2024/06', province: '广东省', city: '佛山市', brand: '理想', sales: 362, price: 256433}
            ];

            const monthlyBrandData = [
                {month: '2024/03', province: '山东省', brand: '蔚来', sales: 257, price: 331224},
                {month: '2024/03', province: '山东省', brand: '理想', sales: 1515, price: 374982},
                {month: '2024/03', province: '广东省', brand: '蔚来', sales: 1367, price: 331224},
                {month: '2024/03', province: '广东省', brand: '理想', sales: 7006, price: 374982},
                {month: '2024/04', province: '山东省', brand: '蔚来', sales: 360, price: 307114},
                {month: '2024/04', province: '山东省', brand: '理想', sales: 1392, price: 359269},
                {month: '2024/04', province: '广东省', brand: '蔚来', sales: 1646, price: 307885},
                {month: '2024/04', province: '广东省', brand: '理想', sales: 4099, price: 356399},
                {month: '2024/05', province: '山东省', brand: '蔚来', sales: 349, price: 318954},
                {month: '2024/05', province: '山东省', brand: '理想', sales: 1796, price: 325970},
                {month: '2024/05', province: '广东省', brand: '蔚来', sales: 2449, price: 318954},
                {month: '2024/05', province: '广东省', brand: '理想', sales: 4452, price: 325970},
                {month: '2024/06', province: '山东省', brand: '蔚来', sales: 356, price: 32006', province: '山东省', brand: '理想', sales: 2376, price: 308903},
                {month: '2024/06', province: '广东省', brand: '蔚来', sales: 2111, price: 320290},
                {month: '2024/06', province: '广东省', brand: '理想', sales: 9078, price: 308903}
            ];

            // 颜色配置
            const colors = {
                蔚来: '#405DF9',
                理想: '#FB466C',
                山东省: '#7342CC',
                广东省: '#ED589D',
                佛山市: '#FFC159'
            };

            // 图表1: 各省份品牌销量对比
            const provinceBrandChart = echarts.init(document.getElementById('provinceBrandChart'));
            const provinceBrandOption = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    想'],
                    bottom: 10
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '15%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    省']
                },
                yAxis: {
                    type: 'value',
                    name: '销量'
                },
                series: [
                    {
                        name: '蔚来',
                        type: '
                        itemStyle: {
                            color: colors.蔚来
                        }
                    },
                    {
                        name: '理想',
                        type:4368, 11683],
                        itemStyle: {
                            color: colors.理想
                        }
                    eBrandChart.setOption(provinceBrandOption);

            // 图表2: 月度销量趋势
            const monthlyTrendChart = echarts.init(document.getElementById('monthlyTrendChart'));

            // 处理月度数据
            const months = ['2024/03', '2024/04', '2024/05', '2024/06'];
            const nioBrandData = monthlyBrandData.filter(item => item.brand === '蔚来');
            const liBrandData = monthlyBrandData.filter(item => item.brand === '理想');

            const nioSdData = nioBrandData.filter(item => item.province === '山东省').map(item => item.sales);
            const nioGdData = nioBrandData.filter(item => item.province === '广东省').map(item => item.sales);
            const liSdData = liBrandData.filter(item => item.province === '山东省').map(item => item.sales);
            const liGdData = liBrandData.filter(item => item.province === '广东省').map(item => item.sales);

            const monthlyTrendOption = {
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                 '蔚来-广东', '理想-山东', '理想-广东'],
                    bottom: 10
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '15%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: months
                },
                yAxis: {
                    type: 'value',
                    name: '销量'
                },
                series: [
                    {
                        name: '蔚来-山东',
                        type:ioSdData,
                        smooth: true,
                        itemStyle: {
                            color: colors.蔚来
                        },
                        lineStyle: {
                            width: 3
                        }
                    },
                    {
                        name: '蔚来-广东',
                        type: 'line',,
                        smooth: true,
                        itemStyle: {
                            color: '#6C63F0'
                        },
                        lineStyle: {
                            width: 3
                        }
                    },
                    {
                        name: '理想-山东',
                        type: dData,
                        smooth: true,
                        itemStyle: {
                            color: '#B750BE'
                        },
                        lineStyle: {
                            width: 3
                        }
                    },
                    {
                        name: '理想-广东',
                        type: 'line',

                        itemStyle: {
                            color: colors.理想
                        },
                        lineStyle: {
                            width: 3
                        }
                    }
                ]
            };
            monthlyTrendChart.setOption(monthlyTrendOption);

            // 图表3: 品牌销量占比
            const brandPieChart = echarts.init(document.getElementById('brandPieChart'));
            const nioTotal = quarterlyData.filter(item => item.brand === '蔚来').reduce((sum, item) => sum + item.sales, 0);
            const liTotal = quarterlyData.filter(item => item.brand === '理想').reduce((sum, item) => sum + item.sales, 0);

            const brandPieOption = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    right: 10,
                    top: 来', '理想']
                },
                series: [
                    {
                        name: '品牌销量',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '18',
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: [
                            {value: nioTotal, name: '蔚来', itemStyle: {color: colors.蔚来}},
                            {value: liTotal, name: '理想', itemStyle: {color: colors.理想}}
                        ]
                    }
                ]
            };
            brandPieChart.setOption(brandPieOption);

            // 图表4: 广东省城市销量
            const cityChart = echarts.init(document.getElementById('cityChart'));

            // 处理佛山市月度数据
            const foshanMonths = [...new Set(monthlyCityData.map(item => item.month))];
            const foshanNioData = foshanMonths.map(month => {
                const item = monthlyCityData.find(d => d.month === month && d.brand === '蔚来');
                return item ? item.sales : 0;
            });

            const foshanLiData = foshanMonths.map(month => {
                const item = monthlyCityData.find(d => d.month === month && d.brand === '理想');
                return item ? item.sales : 0;
            });

            const cityChartOption = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                理想'],
                    bottom: 10
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '15%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type:},
                yAxis: {
                    type: 'value',
                    name: '销量'
                },
                series: [
                    {
                        name: '蔚来',
                        type: 'bar',
                                itemStyle: {
                            color: colors.蔚来
                        }
                    },
                    {
                        name: '理想',
                        type: 'bar',
                        data: foshanLiData,
                        itemStyle: {
                            color: colors.理想
                        }
                    }
                ]
            };
            cityChart.setOption(cityChartOption);

            // 图表5: 车型销量对比
            const carModelChart = echarts.init(document.getElementById('carModelChart'));
            const et7Data = quarterlyData.filter(item => item.model === '蔚来ET7');
            const l6Data = quarterlyData.filter(item => item.model === '理想L6');

            const carModelOption = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'horizontal',
                    bottom来ET7', '理想L6']
                },
                series: [
                    {
                        name: '车型销量',
                        type: 'pie',
                        radius: '60%',
                        center: ['50%',            {
                                value: et7Data.reduce((sum, item) => sum + item.sales, 0),
                                name: '蔚来ET7',
                                itemStyle: {color: colors.蔚来}
                            },
                            {
                                value: l6Data.reduce((sum, item) => sum + item.sales, 0),
                                name: '理想L6',
                                itemStyle: {color: colors.理想}
                            }
                        ],
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }
                ]
            };
            carModelChart.setOption(carModelOption);

            // 图表6: 月度销量与价格关系
            const priceVsSalesChart = echarts.init(document.getElementById('priceVsSalesChart'));

            // 处理数据，提取有效的价格和销量数据
            const scatterData = monthlyData.filter(item => item.price !== null && item.price !== '-').map(item => {
                return {
                    value: [item.price / 10000, item.sales],
                    name: `${item.month} ${item.province} ${item.brand} ${item.model}`,
                    itemStyle: {
                        color: colors[item.brand]
                    }
                };
            });

            const priceVsSalesOption = {
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        return params.name + '<br/>价格: ' + params.value[0] + '万元<br/>销量: ' + params.value[1];
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '15%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    name: '价格 (万元)',
                    nameLocation: 'middle',
                    nameGap: 30
                },
                yAxis: {
                    type: 'value',
                    name: '销量',
                    nameLocation: 'middle',
                    nameGap: 30
                },
                series: [
                    {
                        name: '销量-价格关系',
                        type:atterData,
                        symbolSize: function(data) {
                            return Math.sqrt(data[1]) * 1.5;
                        },
                        markLine: {
                            lineStyle: {
                                type: 'solid'
                            },
                            data: [
                                {
                                    type: 'average',
                                    name: '平均价格',
                                    xAxis: 'average'
                                },
                                {
                                    type: 'average',
                                    name: '平均销量',
                                    yAxis: 'average'
                                }
                            ]
                        }
                    }
                ]
            };
            priceVsSalesChart.setOption(priceVsSalesOption);

            // 响应窗口大小变化
            window.addEventListener('resize', function() {
                provinceBrandChart.resize();
                monthlyTrendChart.resize();
                brandPieChart.resize();
                cityChart.resize();
                carModelChart.resize();
                priceVsSalesChart.resize();
            });
        });
    </script>
</body>
</html>