<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车型比较</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            text-align: center;
            color: #2c3e50;
        }
        .comparison-container {
            display: flex;
            justify-content: space-between;
        }
        .car-model {
            width: 48%;
        }
        .car-model h2 {
            background-color: #3498db;
            color: white;
            padding: 10px;
            border-radius: 5px;
        }
        .category {
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
        .category-header {
            background-color: #f2f2f2;
            padding: 10px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .category-header:hover {
            background-color: #e6e6e6;
        }
        .category-content {
            display: none;
            padding: 10px;
        }
        .feature {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        .feature-name {
            font-weight: bold;
        }
        .feature-value {
            color: #2980b9;
        }
        .toggle-icon {
            font-size: 18px;
        }
    </style>
</head>
<body>
    <h1>车型比较</h1>
    <div class="comparison-container">
        <div class="car-model">
            <h2>比亚迪 秦PLUS DM-i 2025 1.5L E-CVT 智驾版 55KM领先型</h2>
            <div id="model1"></div>
        </div>
        <div class="car-model">
            <h2>比亚迪 秦PLUS DM-i 2025 1.5L E-CVT 智驾版 120KM超越型</h2>
            <div id="model2"></div>
        </div>
    </div>

    <script>
        const carData = {
            "基本信息": {
                "厂商指导价(元)": ["79,800", "103,800"],
                "成交价(元)": ["79,133", "103,133"],
                "销售状态": ["在售", "在售"],
                "编码": ["VC202502110000000053767", "VC202502110000000053769"],
                "生产商": ["比亚迪", "比亚迪"],
                "品牌": ["比亚迪", "比亚迪"],
                "英文品牌": ["BYD", "BYD"],
                "车型": ["秦", "秦"],
                "车型英文": ["Qin", "Qin"],
                "子车型": ["秦PLUS DM-i", "秦PLUS DM-i"],
                "型号": ["1.5L E-CVT 智驾版 55KM领先型", "1.5L E-CVT 智驾版 120KM超越型"],
                "英文型号": ["1.5L E-CVT ADAS 55KM Vantage", "1.5L E-CVT ADAS 120KM Chaoyue"],
                "车身形式": ["三厢", "三厢"],
                "车辆级别": ["A", "A"],
                "细分级别": ["A-M", "A-M"],
                "上市日期": ["2025-02-10", "2025-02-10"],
                "变化描述": ["换代", "换代"],
                "上市年款": ["2025", "2025"],
                "是否进口": ["自主", "自主"],
                "动力类型": ["插电式混合动力", "插电式混合动力"]
            },
            "车身": {
                "长度(mm)": ["4780", "4780"],
                "宽度(mm)": ["1837", "1837"],
                "高度(mm)": ["1515", "1515"],
                "轴距(mm)": ["2718", "2718"],
                "前轮距(mm)": ["1580", "1580"],
                "后轮距(mm)": ["1590", "1590"],
                "整备质量(kg)": ["1480", "1580"],
                "满载质量(kg)": ["1855", "1995"]
            },
            "发动机": {
                "发动机型号": ["BYD472QC", "BYD472QC"],
                "排量(cc)": ["1498", "1498"],
                "进气形式": ["自然吸气", "自然吸气"],
                "气缸数": ["4", "4"],
                "最大功率(kW)": ["74", "74"],
                "最大扭矩(N·m)": ["126", "126"]
            },
            "变速箱": {
                "变速箱类型": ["E-CVT", "E-CVT"],
                "挡位个数": ["无级变速", "无级变速"]
            },
            "底盘转向": {
                "驱动方式": ["前轮驱动", "前轮驱动"],
                "前悬架类型": ["麦弗逊式独立悬架", "麦弗逊式独立悬架"],
                "后悬架类型": ["可变扭力梁式后悬架", "可变扭力梁式后悬架"],
                "助力类型": ["电动助力", "电动助力"]
            },
            "车轮制动": {
                "前制动器类型": ["通风盘式", "通风盘式"],
                "后制动器类型": ["实心盘式", "实心盘式"],
                "驻车制动类型": ["电子驻车", "电子驻车"],
                "前轮胎规格": ["225/60 R16", "225/60 R16"],
                "后轮胎规格": ["225/60 R16", "225/60 R16"]
            },
            "安全装备": {
                "主/副驾驶安全气囊": ["●", "●"],
                "前/后排侧气囊": ["●", "●"],
                "前/后排头部气帘": ["●", "●"],
                "胎压监测": ["●", "●"],
                "车身稳定控制": ["●", "●"],
                "ABS防抱死": ["●", "●"],
                "牵引力控制": ["●", "●"],
                "自动驻车": ["●", "●"]
            },
            "辅助/操控配置": {
                "定速巡航": ["●", "-"],
                "自适应巡航": ["-", "●"],
                "自动泊车": ["-", "●"],
                "倒车影像": ["●", "●"],
                "360度全景影像系统": ["-", "●"]
            },
            "外部配置": {
                "LED日间行车灯": ["●", "●"],
                "LED尾灯": ["●", "●"],
                "前雾灯": ["-", "-"],
                "电动后视镜": ["●", "●"],
                "后视镜加热": ["●", "●"]
            },
            "内部配置": {
                "方向盘调节": ["手动调节", "手动调节"],
                "多功能方向盘": ["●", "●"],
                "定速巡航": ["●", "-"],
                "空调": ["自动空调", "自动空调"],
                "后排出风口": ["●", "●"]
            },
            "座椅配置": {
                "座椅材质": ["仿皮", "仿皮"],
                "主驾驶座电动调节": ["-", "●"],
                "副驾驶座电动调节": ["-", "-"],
                "前排座椅加热": ["-", "-"],
                "后排座椅比例放倒": ["●", "●"]
            },
            "多媒体配置": {
                "中控屏尺寸": ["10.1英寸", "12.8英寸"],
                "导航系统": ["●", "●"],
                "蓝牙/车载电话": ["●", "●"],
                "USB接口": ["●", "●"],
                "扬声器数量": ["6", "6"]
            },
            "灯光配置": {
                "近光灯": ["LED", "LED"],
                "远光灯": ["LED", "LED"],
                "日间行车灯": ["LED", "LED"],
                "大灯高度可调": ["●", "●"],
                "大灯延时关闭": ["●", "●"]
            }
        };

        function createCategoryElement(categoryName, categoryData, modelIndex) {
            const categoryElement = document.createElement('div');
            categoryElement.className = 'category';

            const headerElement = document.createElement('div');
            headerElement.className = 'category-header';
            headerElement.innerHTML = `
                <span>${categoryName}</span>
                <span class="toggle-icon">▼</span>
            `;
            headerElement.addEventListener('click', () => {
                const content = categoryElement.querySelector('.category-content');
                content.style.display = content.style.display === 'none' ? 'block' : 'none';
                headerElement.querySelector('.toggle-icon').textContent = content.style.display === 'none' ? '▼' : '▲';
            });

            const contentElement = document.createElement('div');
            contentElement.className = 'category-content';

            for (const [featureName, featureValues] of Object.entries(categoryData)) {
                const featureElement = document.createElement('div');
                featureElement.className = 'feature';
                featureElement.innerHTML = `
                    <span class="feature-name">${featureName}</span>
                    <span class="feature-value">${featureValues[modelIndex]}</span>
                `;
                contentElement.appendChild(featureElement);
            }

            categoryElement.appendChild(headerElement);
            categoryElement.appendChild(contentElement);
            return categoryElement;
        }

        function renderCarModel(modelId, modelIndex) {
            const modelElement = document.getElementById(modelId);
            for (const [categoryName, categoryData] of Object.entries(carData)) {
                const categoryElement = createCategoryElement(categoryName, categoryData, modelIndex);
                modelElement.appendChild(categoryElement);
            }
        }

        renderCarModel('model1', 0);
        renderCarModel('model2', 1);
    </script>
</body>
</html>