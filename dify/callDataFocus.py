import requests

url = "https://cloud001.datafocus.ai/df/rest/gpt/start"

headers = {
    "Authorization": "Bearer OTFkYTk3MmZlOGE4NDFhODljOTQ1MTE5M2Q4NDRiNDguZmY5ZWQxZjI3ZTYxNDA0MTk0N2RjNGRkM2JjOGYwNTc=",
    "Content-Type": "application/json"
}

data = {
    "language": "chinese",
    "model": {
        "type": "mysql",
        "version": "8.0",
        "tables": [
            {
                "tableDisplayName": "test",
                "tableName": "test",
                "columns": [
                    {
                        "columnDisplayName": "name",
                        "columnName": "name",
                        "dataType": "string",
                        "aggregation": "",
                        "samples": ["张医师", "陶医师", "毛医师"]
                    },
                    {
                        "columnDisplayName": "address",
                        "columnName": "address",
                        "dataType": "string",
                        "aggregation": "",
                        "samples": ["内科-传染病科", "内科-呼吸内科", "内科-神经内科"]
                    },
                    {
                        "columnDisplayName": "age",
                        "columnName": "age",
                        "dataType": "int",
                        "aggregation": "SUM"
                    },
                    {
                        "columnDisplayName": "date",
                        "columnName": "date",
                        "dataType": "timestamp",
                        "aggregation": "",
                        "samples": [
                            "1921-08-10 00:00:00.000",
                            "1922-10-24 00:00:00.000",
                            "1926-10-24 00:00:00.000"
                        ]
                    }
                ]
            }
        ],
        "relations": []
    }
}

try:
    response = requests.post(url, headers=headers, json=data)
    response.raise_for_status()  # 自动抛出HTTP错误
except requests.exceptions.HTTPError as http_err:
    print(f"❌ HTTP错误: {http_err}")
    print(f"响应内容: {response.text}")  # 即使不是JSON也打印原始内容
except Exception as err:
    print(f"❌ 其他错误: {err}")
else:
    try:
        response_json = response.json()
        if response_json.get("success"):
            print("✅ 请求成功")
            print("Session ID:", response_json["data"])
            print("Session ID:", response_json)
        else:
            print("⚠️ 请求未成功:", response_json.get("msgParams"))
    except requests.exceptions.JSONDecodeError:
        print("❗ 响应不是有效JSON，原始内容：")
        print(response.text)