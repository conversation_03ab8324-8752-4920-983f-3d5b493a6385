import re

def main(input_string) -> dict:
    # 初始化变量
    time_field = ""
    dimension_field = []
    metrics = []
    filter_condition_str = ""

    # 按行分割输入字符串
    lines = input_string.strip().split("\n")

    for line in lines:
        line = line.strip()
        if line.startswith("time_field："):
            time_field = line.split("：")[1].strip()
        elif line.startswith("dimension_field："):
            dimension_field = [x.strip() for x in line.split("：")[1].split(',')]
        elif line.startswith("metrics："):
            metrics = [x.strip() for x in line.split("：")[1].split(',')]
        elif line.startswith("filter_condition："):
            filter_condition_str = line.split("：")[1].strip()

    # 构造查询数组
    query_array = [time_field] + dimension_field + metrics

    # 解析 filter_condition 字符串为字典
    filter_dict = {}
    if filter_condition_str:
        # 使用正则表达式匹配键值对
        pattern = re.compile(r'(\w+)=([^=]+?)(?=,\s*\w+=|$)', re.UNICODE)
        matches = pattern.findall(filter_condition_str)
        for key, value_part in matches:
            key = key.strip()
            values = [v.strip() for v in value_part.split(',')]
            if key in {'年', '季', '月'}:
                # 排序并取最小最大值
                sorted_values = sorted(values)
                filter_dict[key] = [sorted_values[0], sorted_values[-1]]
            else:
                filter_dict[key] = values

    # 组合成最终字典
    result = {
        "query": query_array,
        "filters": filter_dict
    }

    return {"result": result}

# 测试用例
print(main("""```text
time_field：月
dimension_field：品牌属性
metrics：CPCA-批发量, CPCA-批发量同比变化
filter_condition：月=202401,202402,202403,202404,202405,202406, 品牌属性ID=3,4,5
```"""))