<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电动汽车销量分析看板</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        :root {
            --color1: #405DF9;
            --color2: #6C63F0;
            --color3: #7342CC;
            --color4: #B750BE;
            --color5: #ED589D;
            --color6: #FB466C;
            --color7: #FFC159;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "HarmonyOS Sans SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            color: rgba(0, 0, 0, 0.85);
            font-size: 12px;
            font-weight: 400;
            letter-spacing: 0.6px;
            line-height: normal;
            background-color: #f9f9f9;
            padding: 20px;
        }

        .dashboard {
            display: flex;
            flex-direction: column;
            gap: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 16px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.20);
        }

        .dashboard-title {
            font-size: 24px;
            font-weight: bold;
            color: var(--color1);
        }

        .section {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 16px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            color: rgba(0, 0, 0, 0.85);
        }

        .chart-container {
            width: 100%;
            height: 350px;
        }

        .cards-container {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
        }

        .metric-card {
            flex: 1;
            min-width: 200px;
            padding: 16px;
            border-radius: 8px;
            background: linear-gradient(135deg, var(--color1), var(--color2));
            color: white;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .metric-value {
            font-size: 28px;
            font-weight: 700;
            margin: 8px 0;
        }

        .metric-label {
            font-size: 14px;
            opacity: 0.9;
        }

        .analysis-container {
            margin-top: 16px;
            padding: 16px;
            background-color: rgba(0, 0, 0, 0.02);
            border-radius: 8px;
            border-left: 4px solid var(--color1);
        }

        .analysis-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--color1);
        }

        .analysis-content {
            font-size: 12px;
            line-height: 1.6;
        }

        .analysis-subsection {
            margin-top: 8px;
        }

        .analysis-subsection-title {
            font-weight: 600;
            margin-bottom: 4px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 16px;
        }

        th, td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        th {
            background-color: rgba(0, 0, 0, 0.03);
            font-weight: 600;
        }

        tr:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }

        .divider {
            height: 1px;
            background: rgba(0, 0, 0, 0.20);
            margin: 16px 0;
        }

        @media (max-width: 768px) {
            .cards-container {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="dashboard-header">
            <div class="dashboard-title">电动汽车销量分析看板</div>
        </div>

        <!-- 指标卡片 -->
        <div class="section">
            <div class="section-title">销量概览</div>
            <div class="cards-container">
                <div class="metric-card" style="background: linear-gradient(135deg, var(--color1), var(--color2));">
                    <div class="metric-label">总销量</div>
                    <div class="metric-value">16,328</div>
                    <div class="metric-label">台</div>
                </div>
                <div class="metric-card" style="background: linear-gradient(135deg, var(--color3), var(--color4));">
                    <div class="metric-label">理想L6销量</div>
                    <div class="metric-value">16,051</div>
                    <div class="metric-label">台 (98.3%)</div>
                </div>
                <div class="metric-card" style="background: linear-gradient(135deg, var(--color5), var(--color6));">
                    <div class="metric-label">蔚来ET7销量</div>
                    <div class="metric-value">277</div>
                    <div class="metric-label">台 (1.7%)</div>
                </div>
                <div class="metric-card" style="background: linear-gradient(135deg, var(--color6), var(--color7));">
                    <div class="metric-label">理想L6销售增长</div>
                    <div class="metric-value">906%</div>
                    <div class="metric-label">月环比(3-6月)</div>
                </div>
            </div>
        </div>

        <!-- 季度销量数据表格 -->
        <div class="section">
            <div class="section-title">季度销量数据表</div>
            <table>
                <thead>
                    <tr>
                        <th>季度</th>
                        <th>省份</th>
                        <th>品牌</th>
                        <th>车型</th>
                        <th>销量</th>
                        <th>TP</th>
                    </tr>
                </thead>
                <tbody id="quarterlyTable">
                    <!-- 表格数据将由JavaScript填充 -->
                </tbody>
            </table>
        </div>

        <!-- 季度销量对比图 -->
        <div class="section">
            <div class="section-title">2024Q4季度销量对比</div>
            <div class="chart-container" id="quarterlyChart"></div>
            <div class="analysis-container">
                <div class="analysis-title">数据分析与建议</div>
                <div class="analysis-content">
                    <div class="analysis-subsection">
                        <div class="analysis-subsection-title">数据表观：</div>
                        <p>- 数据显示不同品牌在不同省份的销量差异明显，销量分布不均衡</p>
                        <p>- 理想L6在广东省和山东省均占据明显的销量优势，广东省的销量(11,683台)远高于山东省(4,368台)</p>
                        <p>- 蔚来ET7销量相对较低，广东省(245台)表现略好于山东省(32台)</p>
                    </div>
                    <div class="analysis-subsection">
                        <div class="analysis-subsection-title">推理/建议：</div>
                        <p>- 理想L6的销量优势可能源于其更符合市场需求的定位和价格策略</p>
                        <p>- 广东省作为经济发达地区，消费能力和对新能源车接受度可能更高</p>
                        <p>- 建议蔚来在山东市场加强营销和渠道建设，可考虑针对当地消费特点调整促销策略</p>
                        <p>- 理想可进一步挖掘山东市场潜力，缩小与广东的销量差距</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 月度销量数据表格 -->
        <div class="section">
            <div class="section-title">月度销量数据表</div>
            <table>
                <thead>
                    <tr>
                        <th>月份</th>
                        <th>省份</th>
                        <th>品牌</th>
                        <th>城市</th>
                        <th>车型</th>
                        <th>销量</th>
                        <th>TP</th>
                    </tr>
                </thead>
                <tbody id="monthlyTable">
                    <!-- 表格数据将由JavaScript填充 -->
                </tbody>
            </table>
        </div>

        <!-- 月度销量趋势图 -->
        <div class="section">
            <div class="section-title">广东省佛山市月度销量趋势</div>
            <div class="chart-container" id="monthlyTrendChart"></div>
            <div class="analysis-container">
                <div class="analysis-title">数据分析与建议</div>
                <div class="analysis-content">
                    <div class="analysis-subsection">
                        <div class="analysis-subsection-title">数据表观：</div>
                        <p>- 理想L6销量呈现显著上升趋势，从4月的36台迅速增长到6月的362台</p>
                        <p>- 蔚来ET7销量虽有波动但基本保持在较低水平，整体未见明显增长</p>
                        <p>- 两款车型销量差距逐月扩大，显示市场偏好正在进一步倾向于理想L6</p>
                    </div>
                    <div class="analysis-subsection">
                        <div class="analysis-subsection-title">推理/建议：</div>
                        <p>- 理想L6销量快速增长可能与其加大促销力度、口碑传播以及产品定位精准有关</p>
                        <p>- 蔚来ET7销量平稳但未见增长，可能面临更激烈的高端市场竞争</p>
                        <p>- 建议蔚来针对佛山市场进行消费者调研，了解需求差异，调整产品策略</p>
                        <p>- 理想可把握增长势头，在品牌宣传和售后服务方面进一步发力，巩固市场优势</p>
                        <p>- 若趋势持续，预计未来几个月理想L6销量还将继续增长，可能达到每月500台以上</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 品牌份额对比图 -->
        <div class="section">
            <div class="section-title">品牌市场份额分析</div>
            <div class="chart-container" id="marketShareChart"></div>
            <div class="analysis-container">
                <div class="analysis-title">数据分析与建议</div>
                <div class="analysis-content">
                    <div class="analysis-subsection">
                        <div class="analysis-subsection-title">数据表观：</div>
                        <p>- 理想在总体销量中占据绝对优势，市场份额高达98.3%</p>
                        <p>- 蔚来ET7的市场份额仅为1.7%，处于明显的劣势地位</p>
                        <p>- 两个品牌的市场份额差距极大，市场结构呈现高度集中状态</p>
                    </div>
                    <div class="analysis-subsection">
                        <div class="analysis-subsection-title">推理/建议：</div>
                        <p>- 理想L6的绝对优势可能与其在价格、性能和市场定位方面的精准把握有关</p>
                        <p>- 蔚来ET7可能因价格较高或产品定位偏离大众需求而导致份额较低</p>
                        <p>- 建议蔚来重新评估其市场定位和价格策略，考虑推出更符合当前市场需求的产品或促销策略</p>
                        <p>- 理想需防止市场过度依赖单一车型，可通过扩充产品线来应对可能的市场变化</p>
                        <p>- 对经销商而言，目前可优先配置理想L6资源，但也应关注市场变化，避免错失蔚来可能的反弹机会</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- TP与销量关系图 -->
        <div class="section">
            <div class="section-title">TP与销量关系分析</div>
            <div class="chart-container" id="tpSalesChart"></div>
            <div class="analysis-container">
                <div class="analysis-title">数据分析与建议</div>
                <div class="analysis-content">
                    <div class="analysis-subsection">
                        <div class="analysis-subsection-title">数据表观：</div>
                        <p>- 理想L6以较低的TP(约25万)实现了较高的销量</p>
                        <p>- 蔚来ET7尽管TP较高(约42万)，但销量表现不佳</p>
                        <p>- 数据显示TP与销量之间并不存在正相关关系，反而呈现负相关趋势</p>
                    </div>
                    <div class="analysis-subsection">
                        <div class="analysis-subsection-title">推理/建议：</div>
                        <p>- 消费者对价格敏感度较高，理想L6的性价比优势可能是其销量成功的关键因素</p>
                        <p>- 高TP策略并不必然带来高销量，产品定位与市场需求的匹配度更为重要</p>
                        <p>- 建议蔚来考虑是否调整价格策略，或更清晰地传达产品的溢价价值</p>
                        <p>- 理想可继续保持其价格优势，同时提升产品附加值以支撑可能的价格提升</p>
                        <p>- 经销商制定销售策略时，应关注不同价位区间的消费者需求差异，针对性提供服务</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 季度销量数据
        const quarterlyData = [
            { season: "2024Q4", province: "山东省", brand: "蔚来", model: "蔚来ET7", sales: 32, tp: 427295 },
            { season: "2024Q4", province: "山东省", brand: "理想", model: "理想L6", sales: 4368, tp: 254588 },
            { season: "2024Q4", province: "广东省", brand: "蔚来", model: "蔚来ET7", sales: 245, tp: 427295 },
            { season: "2024Q4", province: "广东省", brand: "理想", model: "理想L6", sales: 11683, tp: 254588 }
        ];

        // 月度销量数据
        const monthlyData = [
            { month: "2024/03", province: "广东省", brand: "蔚来", city: "佛山市", model: "蔚来ET7", sales: 4, tp: 410542 },
            { month: "2024/04", province: "广东省", brand: "蔚来", city: "佛山市", model: "蔚来ET7", sales: 4, tp: 423157 },
            { month: "2024/04", province: "广东省", brand: "理想", city: "佛山市", model: "理想L6", sales: 36, tp: 256618 },
            { month: "2024/05", province: "广东省", brand: "蔚来", city: "佛山市", model: "蔚来ET7", sales: 18, tp: 424461 },
            { month: "2024/05", province: "广东省", brand: "理想", city: "佛山市", model: "理想L6", sales: 232, tp: 257189 },
            { month: "2024/06", province: "广东省", brand: "蔚来", city: "佛山市", model: "蔚来ET7", sales: 20, tp: 417080 },
            { month: "2024/06", province: "广东省", brand: "理想", city: "佛山市", model: "理想L6", sales: 362, tp: 256433 }
        ];

        // 填充季度数据表格
        const quarterlyTable = document.getElementById('quarterlyTable');
        quarterlyData.forEach(item => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.season}</td>
                <td>${item.province}</td>
                <td>${item.brand}</td>
                <td>${item.model}</td>
                <td>${item.sales.toLocaleString()}</td>
                <td>${item.tp.toLocaleString()}</td>
            `;
            quarterlyTable.appendChild(row);
        });

        // 填充月度数据表格
        const monthlyTable = document.getElementById('monthlyTable');
        monthlyData.forEach(item => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.month}</td>
                <td>${item.province}</td>
                <td>${item.brand}</td>
                <td>${item.city}</td>
                <td>${item.model}</td>
                <td>${item.sales.toLocaleString()}</td>
                <td>${item.tp.toLocaleString()}</td>
            `;
            monthlyTable.appendChild(row);
        });

        // 初始化图表
        const quarterlyChart = echarts.init(document.getElementById('quarterlyChart'));
        const monthlyTrendChart = echarts.init(document.getElementById('monthlyTrendChart'));
        const marketShareChart = echarts.init(document.getElementById('marketShareChart'));
        const tpSalesChart = echarts.init(document.getElementById('tpSalesChart'));

        // 季度销量对比图表
        const quarterlyOption = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {
                data: ['蔚来ET7', '理想L6']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['山东省', '广东省']
            },
            yAxis: {
                type: 'value',
                name: '销量'
            },
            series: [
                {
                    name: '蔚来ET7',
                    type: 'bar',
                    stack: 'total',
                    emphasis: {
                        focus: 'series'
                    },
                    data: [32, 245],
                    itemStyle: {
                        color: '#405DF9'
                    }
                },
                {
                    name: '理想L6',
                    type: 'bar',
                    stack: 'total',
                    emphasis: {
                        focus: 'series'
                    },
                    data: [4368, 11683],
                    itemStyle: {
                        color: '#FB466C'
                    }
                }
            ]
        };

        // 月度销量趋势图表
        const monthlyTrendOption = {
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['蔚来ET7', '理想L6']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: ['2024/03', '2024/04', '2024/05', '2024/06']
            },
            yAxis: {
                type: 'value',
                name: '销量'
            },
            series: [
                {
                    name: '蔚来ET7',
                    type: 'line',
                    data: [4, 4, 18, 20],
                    smooth: true,
                    itemStyle: {
                        color: '#405DF9'
                    },
                    lineStyle: {
                        width: 3
                    }
                },
                {
                    name: '理想L6',
                    type: 'line',
                    data: [0, 36, 232, 362],
                    smooth: true,
                    itemStyle: {
                        color: '#FB466C'
                    },
                    lineStyle: {
                        width: 3
                    }
                }
            ]
        };

        // 市场份额图表
        const totalNioSales = quarterlyData.filter(item => item.brand === "蔚来").reduce((sum, item) => sum + item.sales, 0);
        const totalLiSales = quarterlyData.filter(item => item.brand === "理想").reduce((sum, item) => sum + item.sales, 0);
        const totalSales = totalNioSales + totalLiSales;

        const marketShareOption = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'vertical',
                left: 10,
                data: ['蔚来ET7', '理想L6']
            },
            series: [
                {
                    name: '销量份额',
                    type: 'pie',
                    radius: ['50%', '70%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: 20,
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: [
                        {
                            value: totalNioSales,
                            name: '蔚来ET7',
                            itemStyle: { color: '#405DF9' }
                        },
                        {
                            value: totalLiSales,
                            name: '理想L6',
                            itemStyle: { color: '#FB466C' }
                        }
                    ]
                }
            ]
        };

        // TP与销量关系图表
        const tpSalesOption = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'value',
                name: 'TP (元)',
                axisLabel: {
                    formatter: '{value}'
                }
            },
            yAxis: {
                type: 'value',
                name: '总销量'
            },
            series: [
                {
                    type: 'scatter',
                    symbolSize: function (data) {
                        return Math.sqrt(data[1]) / 3 + 10;
                    },
                    data: [
                        [427295, totalNioSales, '蔚来ET7'],
                        [254588, totalLiSales, '理想L6']
                    ],
                    itemStyle: {
                        color: function(params) {
                            return params.data[2] === '蔚来ET7' ? '#405DF9' : '#FB466C';
                        }
                    },
                    label: {
                        show: true,
                        formatter: function(params) {
                            return params.data[2];
                        },
                        position: 'top'
                    }
                }
            ]
        };

        // 渲染图表
        quarterlyChart.setOption(quarterlyOption);
        monthlyTrendChart.setOption(monthlyTrendOption);
        marketShareChart.setOption(marketShareOption);
        tpSalesChart.setOption(tpSalesOption);

        // 窗口调整大小时重绘图表
        window.addEventListener('resize', function() {
            quarterlyChart.resize();
            monthlyTrendChart.resize();
            marketShareChart.resize();
            tpSalesChart.resize();
        });
    </script>
</body>
</html>