import os
from openai import OpenAI

client = OpenAI(
    api_key="sk-7b9067ef70794d7286da8432be28f18c",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)

# Markdown文件目录和输出文件路径
md_dir = r'D:\06-AI 测试文件\md'
output_txt = r'D:\06-AI 测试文件\output.txt'


def process_markdown_files():
    # 获取目录下所有md文件
    md_files = [f for f in os.listdir(md_dir) if f.endswith('.md')]

    with open(output_txt, 'w', encoding='utf-8') as output_file:
        for idx, filename in enumerate(md_files):
            file_path = os.path.join(md_dir, filename)

            # 读取Markdown文件内容
            with open(file_path, 'r', encoding='utf-8') as md_file:
                md_content = md_file.read()

            # 调用API处理内容
            completion = client.chat.completions.create(
                model="qwen-max-latest",
                messages=[{'role': 'system', 'content': """
                                                    # 知识库结构化解析指令
                                
                                                    ## 核心任务
                                                    请将提供的营销文档内容转化为结构化知识图谱，需包含文本摘要、关键数据提取、多模态资源关联三部分。
                                                    
                                                    ## 处理规范
                                                    
                                                    ### 1. 文档框架解析
                                                    - 识别章节层级关系（示例）：
                                                      主标题：全民智驾 元力全开 → 二级标题：九大机会 → 三级条目：机会一、机会二...
                                                    - 建立章节ID映射表：
                                                      sec_opportunities → 九大机会
                                                      sec_strategy → 营销六大打法
                                                    
                                                    ### 2. 文本智能摘要
                                                    - 关键信息提取标准：
                                                      √ 核心卖点：「2升1降」策略
                                                      √ 重要数据：月均2.2万台销量
                                                      √ 独特优势：116个国家覆盖
                                                      × 忽略过渡性描述语句
                                                    
                                                    ### 3. 多模态数据处理
                                                    [表格处理]
                                                    - 结构化转换示例：
                                                      | 策略类型 | 执行要点 | 时间节点 |
                                                      |----------|----------|----------|
                                                      → {"策略类型":"线上传播","执行要点":"抖音直播3次/周","时间区间":"3.5-4.30"}
                                                    
                                                    [图片处理]
                                                    - 创建视觉索引：
                                                      ![](image_url) → 
                                                      {
                                                        "img_id":"fig1",
                                                        "描述":"智能泊车场景演示图",
                                                        "关联章节":["sec_strategy","sec_technology"],
                                                        "关键元素":["窄车位","遥控泊车","女性用户"]
                                                      }
                                                    
                                                    ### 4. 关联网络构建
                                                    - 创建知识节点链接：
                                                      机会九 → 关联 → 营销打法三 → 引用 → 表格tbl_strategy3
                                                      智能泊车 → 关联 → 图片fig5/fig7 → 延伸 → 用户场景#3
                                                    
                                                    ### 5. 动态元数据标注
                                                    - 时效性标记：
                                                      "价格策略有效期：2024.3-2024.5"
                                                    - 数据来源标注：
                                                      "销量数据来源：中国乘联会2024Q1报告"
                                                    - 置信度标注：
                                                      "竞品分析(置信度90%) vs 市场预测(置信度75%)"
                                                    - 政策发布厂商：
                                                      "比亚迪"
                    """},
                    {'role': 'user', 'content': md_content}
                ]
            )

            # 获取处理结果
            processed_content = completion.choices[0].message.content

            # 写入处理结果到txt文件
            output_file.write(f"文件名称：{filename}\n")
            output_file.write(processed_content + "\n")
            output_file.write("\n****\n\n")  # 添加分隔符

            print(f"已处理 {filename} ({idx + 1}/{len(md_files)})")


if __name__ == "__main__":
    process_markdown_files()
    print("所有文件处理完成，结果已保存至", output_txt)