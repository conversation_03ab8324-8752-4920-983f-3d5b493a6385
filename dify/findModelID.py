import re


class VehicleMatcher:
    def __init__(self, vehicles):
        # 数据标准化预处理
        self.vehicles = []
        for v in vehicles:
            standardized = {
                "id": v["id"],
                "name": v["name"].lower().replace("-", "").replace(" ", ""),
                "aliases": [alias.lower().replace("-", "").replace(" ", "") for alias in v["aliases"]],
                "brand": v.get("brand", "").lower(),
                "series": v.get("series", "").lower(),
                "year": v.get("year", "")
            }
            self.vehicles.append(standardized)

    def _exact_match(self, input_str):
        """第一层：精确匹配"""
        cleaned = input_str.lower().replace(" ", "").replace("-", "")
        for vehicle in self.vehicles:
            if cleaned == vehicle["name"] or cleaned in vehicle["aliases"]:
                return vehicle
        return None

    def _parse_attributes(self, input_str):
        """第二层：关键词解析"""
        patterns = {
            "brand": r"(宝马|五菱|奔驰|byd|特斯拉)",
            "series": r"(\d+系|[a-zA-Z]\d+|[\\u4e00-\\u9fa5]+)",
            "year": r"(20\d{2})款?",
            "spec": r"(pro|max|plus|豪华版|尊享版)"
        }

        attrs = {}
        for key, pattern in patterns.items():
            match = re.search(pattern, input_str.lower())
            if match:
                attrs[key] = match.group(1).replace(" ", "")
        return attrs

    def _fuzzy_match(self, attrs):
        """属性组合查询"""
        candidates = []
        for vehicle in self.vehicles:
            match_score = 0
            # 品牌必须匹配
            if "brand" in attrs and attrs["brand"] != vehicle["brand"]:
                continue

            # 车系匹配（支持部分匹配）
            if "series" in attrs:
                if attrs["series"] in vehicle["series"]:
                    match_score += 2
                elif any(attrs["series"] in alias for alias in vehicle["aliases"]):
                    match_score += 1

            # 年份匹配
            if "year" in attrs and attrs["year"] == vehicle["year"]:
                match_score += 1

            # 特殊配置
            if "spec" in attrs and attrs["spec"] in vehicle["name"]:
                match_score += 1

            if match_score > 0:
                candidates.append((vehicle, match_score))

        # 按匹配分数排序
        return sorted(candidates, key=lambda x: -x[1])

    def match(self, input_str):
        """执行完整匹配流程"""
        # 1. 精确匹配
        exact_result = self._exact_match(input_str)
        if exact_result:
            return {"status": "exact_match", "result": exact_result}

        # 2. 属性解析
        attrs = self._parse_attributes(input_str)

        # 3. 模糊匹配
        candidates = self._fuzzy_match(attrs)

        if len(candidates) == 0:
            return {"status": "not_found"}
        elif len(candidates) == 1:
            return {"status": "fuzzy_match", "result": candidates[0][0]}
        else:
            return {
                "status": "need_clarify",
                "candidates": [c[0] for c in candidates[:3]],  # 最多显示3个候选
                "hints": self._generate_hints(candidates)
            }

    def _generate_hints(self, candidates):
        """生成澄清提示"""
        hints = set()
        for c in candidates[:3]:
            vehicle = c[0]
            hint_parts = []
            if vehicle["year"]:
                hint_parts.append(f"{vehicle['year']}款")
            hint_parts.append(vehicle["series"].upper())
            hints.add(" ".join(hint_parts))
        return list(hints)


class VehicleAgent:
    def __init__(self, matcher):
        self.matcher = matcher

    def process_input(self, user_input):
        result = self.matcher.match(user_input)

        if result["status"] == "exact_match":
            return f"找到精确匹配：{result['result']['id']}"

        elif result["status"] == "fuzzy_match":
            return f"找到最接近匹配：{result['result']['id']}"

        elif result["status"] == "need_clarify":
            return "请确认：\n" + "\n".join(
                f"{i + 1}. {hint}"
                for i, hint in enumerate(result["hints"])
            )  # 修复括号关闭


# 示例数据
vehicles_data = [
    {
        "id": "V001",
        "name": "宝马5系",
        "aliases": ["bmw5", "宝马5", "五系"],
        "brand": "宝马",
        "series": "5系",
        "year": "2021"
    },
    {
        "id": "V002",
        "name": "五菱宏光S",
        "aliases": ["wulinghongguangs", "宏光s"],
        "brand": "五菱",
        "series": "宏光S",
        "year": "2020"
    },
    {
        "id": "V003",
        "name": "特斯拉Model3",
        "aliases": ["毛豆3", "teslam3"],
        "brand": "特斯拉",
        "series": "model3",
        "year": "2023"
    }
]

# 初始化匹配器和Agent
matcher = VehicleMatcher(vehicles_data)
agent = VehicleAgent(matcher)

# 测试用例
test_cases = [
    "宝马5系",  # 精确匹配
    "我要找毛豆3",  # 别名匹配
    "五菱的2020款车",  # 属性匹配
    "特斯拉",  # 需要澄清
    "2023款宝马",  # 多候选
    "不存在的车型"  # 无结果
]

# 运行测试
for case in test_cases:
    print(f"用户输入：{case}")
    print(agent.process_input(case))
    print("-" * 50)