<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>比亚迪秦PLUS DM-i 2025 两车型配置对比</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <style>
        body {
            background: #f7f8fa;
            color: #222;
            font-family: "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
            margin: 0;
        }
        header {
            background: linear-gradient(90deg, #0066ff 0%, #00c2ff 100%);
            color: #fff;
            padding: 24px 0 20px 0;
            text-align: center;
            box-shadow: 0 2px 6px rgba(0,0,0,0.03);
        }
        header h1 {
            margin: 0;
            font-size: 2rem;
            letter-spacing: 0.03em;
        }
        header h2 {
            margin: 10px 0 0 0;
            font-size: 1.1rem;
            font-weight: normal;
        }

        #container {
            max-width: 1100px;
            margin: 30px auto;
            background: #fff;
            border-radius: 14px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.09);
            padding: 32px 20px 20px 20px;
        }

        .category {
            margin-top: 30px;
            border-radius: 10px;
            border: 1px solid #e7eaf2;
            overflow: hidden;
            background: #fafcff;
        }

        .category-header {
            font-size: 1.15rem;
            color: #0072ff;
            font-weight: bold;
            padding: 14px 22px;
            background: #f1f8ff;
            cursor: pointer;
            display: flex;
            align-items: center;
            user-select: none;
            border-bottom: 1px solid #e7eaf2;
            position: relative;
        }
        .category-header .arrow {
            display: inline-block;
            margin-right: 10px;
            transition: transform 0.25s;
            font-size: 1.3rem;
        }
        .category.collapsed .category-header .arrow {
            transform: rotate(-90deg);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.98rem;
        }
        thead {
            background: #f5f7fa;
        }
        th, td {
            padding: 10px 10px;
            text-align: center;
            border-bottom: 1px solid #e7eaf2;
        }
        th {
            color: #444;
            font-weight: 600;
            background: #f6faff;
        }
        .item-name {
            text-align: left;
            color: #1060c4;
            font-weight: 500;
            background: #fafdff;
        }
        .diff {
            background: #fffbe7;
        }
        .icon-cell {
            font-size: 1.2em;
            letter-spacing: 0.05em;
        }
        .icon-dot {
            color: #3bb955; /* 标配：绿色 */
            font-weight: bold;
        }
        .icon-circle {
            color: #feac2f; /* 选配：橙色 */
            font-weight: bold;
        }
        .icon-dash {
            color: #c5c8cc; /* 无：灰色 */
            font-weight: bold;
        }
        /* 配置不同高亮行 */
        .diff {
            background: #fff5e3 !important;
        }
        /* 可选配高亮 */
        .optional {
            background: #fef9ee;
        }
        /* 悬浮行 */
        tbody tr:hover {
            background: #f2f8ff;
        }
        /* 响应式适配 */
        @media (max-width: 850px) {
            #container {padding: 12px 2px;}
            .category-header {font-size: 1rem; padding: 11px 10px;}
            th, td {padding: 7px 3px; font-size: 0.93rem;}
            .item-name {font-size: 0.98rem;}
        }
        @media (max-width: 660px) {
            header {padding: 7vw 0;}
            h1 {font-size: 1.2rem;}
            th,td{font-size:0.90rem;}
        }
    </style>
</head>
<body>
<header>
    <h1>比亚迪秦PLUS DM-i 2025 两车型配置对比</h1>
    <h2>1.5L E-CVT 智驾版 55KM领先型 &nbsp;VS&nbsp; 1.5L E-CVT 智驾版 120KM超越型</h2>
</header>
<div id="container">
    <!-- 动态内容插入 -->
</div>
<script>
/**
 * 数据源：建议以后直接 JSON 化，但本页面已手动结构化。
 * 按大类分组，部分字段合并/优化，便于前端展示。
 * 表格支持：不同高亮、可收起、icon视觉。
 */
// 车型名称
const modelNames = [
    "1.5L E-CVT 智驾版 55KM领先型",
    "1.5L E-CVT 智驾版 120KM超越型"
];

// 配置大类和细项
const carData = [
    {
        category: "价格与销售状态",
        items: [
            { name: "厂商指导价(元)", values: ["79,800", "103,800"] },
            { name: "成交价(元)", values: ["79,133", "103,133"] },
            { name: "销售状态", values: ["在售", "在售"] }
        ]
    },
    {
        category: "基本信息",
        items: [
            { name: "生产商", values: ["比亚迪", "比亚迪"]},
            { name: "品牌", values: ["比亚迪", "比亚迪"]},
            { name: "车型", values: ["秦", "秦"]},
            { name: "子车型", values: ["秦PLUS DM-i", "秦PLUS DM-i"]},
            { name: "型号", values: ["1.5L E-CVT 智驾版 55KM领先型", "1.5L E-CVT 智驾版 120KM超越型"]},
            { name: "上市日期", values: ["2025-02-10", "2025-02-10"]},
            { name: "动力类型", values: ["插电式混合动力", "插电式混合动力"] },
            { name: "发动机型号", values: ["BYD472QC","BYD472QC"] },
            { name: "内饰颜色", values: ["黑色/暮云灰、黑色/砂金米", "黑色/暮云灰、黑色/砂金米"] },
            { name: "车身颜色", values: ["冰珀青、白釉青、雪域白、时光灰", "冰珀青、白釉青、雪域白、时光灰"] },
            { name: "质保期(年)", values: ["6", "6"] },
            { name: "质保期(万公里)", values: ["15", "15"] }
        ]
    },
    {
        category: "技术参数",
        items: [
            { name: "总长(mm)", values: ["4780","4780"] },
            { name: "总宽(mm)", values: ["1837","1837"] },
            { name: "总高(mm)", values: ["1515","1515"] },
            { name: "轴距(mm)", values: ["2718", "2718"] },
            { name: "整备质量(kg)", values: ["1480", "1580"] },
            { name: "油箱容积(L)", values: ["65", "65"] },
            { name: "动力类型", values: ["插电式混合动力", "插电式混合动力"] },
            { name: "排量", values: ["1.5", "1.5"] },
            { name: "气缸数", values: ["4", "4"] },
            { name: "最大功率(kW)", values: ["74", "74"] },
            { name: "最大扭矩(Nm)", values: ["126", "126"] },
            { name: "变速箱", values: [renderIcon("●", "E-CVT"), renderIcon("●", "E-CVT")] },
            { name: "纯电续航(WLTC/CLTC)", values: ["43/55 km", "90/120 km"] },
            { name: "电池容量(kWh)", values: ["7.68", "15.8"] },
            { name: "慢充/快充功率(kW)", values: ["3.3/-", "3.3/15"] },
            { name: "充电接口", values: [renderIcon("●", "交流"), renderIcon("●", "直流+交流")] },
            { name: "电机类型", values: [renderIcon("●", "永磁同步"), renderIcon("●", "永磁同步")] },
            { name: "前电机最大功率(kW)", values: ["120", "120"] },
            { name: "前电机最大扭矩(Nm)", values: ["210", "210"] },
            { name: "最高车速(km/h)", values: ["180", "180"] },
            { name: "0-100km/h加速(s)", values: ["7.6", "7.6"] },
            { name: "综合工况油耗(WLTC,L/100km)", values: ["1.74", "1.11"] }
        ]
    },
    {
        category: "外部配置",
        items: [
            { name: "远近光全LED大灯", values: [renderIcon("●"), renderIcon("●")] },
            { name: "手动前大灯光线水平调节", values: [renderIcon("●"), renderIcon("●")] },
            { name: "前大灯自动感光开闭", values: [renderIcon("●"), renderIcon("●")] },
            { name: "LED日间行车灯", values: [renderIcon("●"), renderIcon("●")] },
            { name: "LED尾灯", values: [renderIcon("●"), renderIcon("●")] },
            { name: "流水转向灯", values: [renderIcon("●"), renderIcon("●")] },
            { name: "大灯自动切换", values: [renderIcon("-"), renderIcon("●")] },
            { name: "外后视镜加热", values: [renderIcon("●"), renderIcon("●")] },
            { name: "外后视镜电动折叠", values: [renderIcon("-"), renderIcon("●")] },
            { name: "外后视镜锁车自动折叠", values: [renderIcon("-"), renderIcon("●")] },
            { name: "鲨鱼鳍式天线", values: [renderIcon("-"), renderIcon("●")] },
            { name: "普通天窗", values: [renderIcon("-"), renderIcon("●")] },
            { name: "电动天窗操控", values: [renderIcon("-"), renderIcon("●")] },
            { name: "隔热式/防紫外线车窗", values: [renderIcon("●"), renderIcon("●")] },
        ]
    },
    {
        category: "内部与座椅",
        items: [
            { name: "座位数", values: [renderIcon("●", "5"), renderIcon("●", "5")] },
            { name: "仿皮座椅材质", values: [renderIcon("●"), renderIcon("●")] },
            { name: "主驾驶座椅调节", values: [renderIcon("●", "手动"), renderIcon("●", "电动")] },
            { name: "主驾驶座椅电动调节数", values: [renderIcon("-"), renderIcon("●", "6")] },
            { name: "主驾驶座椅高低调节", values: [renderIcon("●"), renderIcon("●")] },
            { name: "副驾驶座椅调节", values: [renderIcon("●"), renderIcon("●")] },
            { name: "后排中央扶手", values: [renderIcon("-"), renderIcon("●")] },
            { name: "后排中央扶手带杯架", values: [renderIcon("-"), renderIcon("●")] },
            { name: "分体第二排座椅折叠", values: [renderIcon("●"), renderIcon("●")] },
            { name: "前排USB接口", values: [renderIcon("●"), renderIcon("●")] },
            { name: "后排USB接口", values: [renderIcon("●"), renderIcon("●")] },
            { name: "USB/Type-C接口数", values: ["3", "4"] },
        ]
    },
    {
        category: "智能座舱&多媒体",
        items: [
            { name: "液晶中控大屏(英寸)", values: [renderIcon("●", "10.1"), renderIcon("●", "12.8")] },
            { name: "全液晶仪表盘", values: [renderIcon("-"), renderIcon("●", "8.8")] },
            { name: "车机系统内存(GB)", values: ["4", "8"] },
            { name: "车机系统存储(GB)", values: ["16", "256"] },
            { name: "扬声器数", values: [renderIcon("●", "6"), renderIcon("●", "6")] },
            { name: "高保真喇叭", values: [renderIcon("●"), renderIcon("●")] },
            { name: "原厂导航", values: [renderIcon("●", "腾讯"), renderIcon("●", "百度")] },
            { name: "车载蓝牙", values: [renderIcon("●"), renderIcon("●")] },
            { name: "网络(4G/5G)", values: [renderIcon("●", "4G"), renderIcon("●", "5G")] },
            { name: "车载Wifi热点", values: [renderIcon("●"), renderIcon("●")] },
            { name: "应用商店", values: [renderIcon("●"), renderIcon("●")] },
            { name: "智能AI语音助手", values: [renderIcon("●"), renderIcon("●")] },
            { name: "语音免唤醒", values: [renderIcon("-"), renderIcon("●")] },
            { name: "语音连续对话", values: [renderIcon("-"), renderIcon("●")] }
        ]
    },
    {
        category: "安全与辅助驾驶",
        items: [
            { name: "前/后驻车雷达", values: [renderIcon("-", "前") + "<br>" + renderIcon("●", "后"), renderIcon("●", "前后")] },
            { name: "倒车影像", values: [renderIcon("●"), renderIcon("●")] },
            { name: "前/侧/后摄像头", values: [renderIcon("-", "前侧") + "<br>" + renderIcon("●", "后"), renderIcon("●", "前侧后")] },
            { name: "气囊数", values: ["6", "6"] },
            { name: "前排侧气囊", values: [renderIcon("●"), renderIcon("●")] },
            { name: "前后一体侧气帘", values: [renderIcon("●"), renderIcon("●")] },
            { name: "被动式胎压监测", values: [renderIcon("●"), renderIcon("-")] },
            { name: "直接式胎压监测", values: [renderIcon("-"), renderIcon("●")] },
            { name: "车身稳定系统(ESP)", values: [renderIcon("●"), renderIcon("●")] },
            { name: "上坡辅助", values: [renderIcon("●"), renderIcon("●")] },
            { name: "紧急制动提醒", values: [renderIcon("●"), renderIcon("●")] },
            { name: "电子驻车/Auto Hold", values: [renderIcon("●"), renderIcon("●")] }
        ]
    },
    {
        category: "智能驾驶辅助",
        items: [
            { name: "定速巡航", values: [renderIcon("●"), renderIcon("-")] },
            { name: "全速域自适应巡航", values: [renderIcon("-"), renderIcon("●")] },
            { name: "L2级辅助驾驶", values: [renderIcon("-"), renderIcon("●")] },
            { name: "前向碰撞预警(FCW)", values: [renderIcon("-"), renderIcon("●")] },
            { name: "自动紧急制动(AEB)", values: [renderIcon("-"), renderIcon("●")] },
            { name: "车道保持/偏离预警", values: [renderIcon("-", "无"), renderIcon("●")] },
            { name: "盲区监测(BSD)", values: [renderIcon("-"), renderIcon("●")] },
            { name: "3D全景影像", values: [renderIcon("-"), renderIcon("●")] },
            { name: "全自动泊车(APA)", values: [renderIcon("-"), renderIcon("●")] },
            { name: "车外摄像头总数", values: ["1", "12"] },
            { name: "超声波雷达数量", values: ["4", "12"] },
            { name: "前毫米波雷达数量", values: ["0", "1"] }
        ]
    },
    {
        category: "远程IOT功能",
        items: [
            { name: "远程车辆控制", values: [renderIcon("●"), renderIcon("●")] },
            { name: "远程解锁(门、窗、后备箱)", values: [renderIcon("●"), renderIcon("●")] },
            { name: "远程控制空调", values: [renderIcon("●"), renderIcon("●")] },
            { name: "远程车辆诊断(OBD)", values: [renderIcon("●"), renderIcon("●")] },
            { name: "远程定位追踪", values: [renderIcon("●"), renderIcon("●")] },
            { name: "远程充电控制", values: [renderIcon("●"), renderIcon("●")] }
        ]
    }
];

// 可视化标配/选配/无
function renderIcon(ico, text='') {
    let html = '';
    if (ico === '●') html = `<span class="icon-dot icon-cell" title="标配">●</span>`;
    else if (ico === '○') html = `<span class="icon-circle icon-cell" title="选配">○</span>`;
    else if (ico === '-') html = `<span class="icon-dash icon-cell" title="无">-</span>`;
    else html = ico;
    // 如果有描述
    if (text) html += `<span style="font-size:0.95em;color:#555;margin-left:2px;">${text}</span>`;
    return html;
}

// 渲染表格内容
function createCategoryTable(cat) {
    // 判断哪些行是差异项（两列不同）
    return `
        <div class="category" data-collapsed="false">
            <div class="category-header"><span class="arrow">▼</span>${cat.category}</div>
            <div class="category-body">
                <table>
                    <thead>
                        <tr>
                            <th style="width:34%;">配置项</th>
                            <th>${modelNames[0]}</th>
                            <th>${modelNames[1]}</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${cat.items.map(row => {
                            const isDiff = (row.values[0] !== row.values[1]);
                            // 若为选配，额外加色
                            let clz = '';
                            // 高亮不同项
                            if (isDiff) clz += 'diff ';
                            // 若选配
                            if (row.values.some(v=>/○/.test(v))) clz += 'optional ';
                            return `<tr class="${clz.trim()}">
                                <td class="item-name">${row.name}</td>
                                <td>${row.values[0]}</td>
                                <td>${row.values[1]}</td>
                            </tr>`;
                        }).join("")}
                    </tbody>
                </table>
            </div>
        </div>
    `;
}

// 渲染全部
document.getElementById('container').innerHTML = carData.map(createCategoryTable).join("");

// 展开/收起功能
document.querySelectorAll('.category-header').forEach(header => {
    header.addEventListener('click', function(){
        const cat = this.parentElement;
        cat.classList.toggle('collapsed');
        const body = cat.querySelector('.category-body');
        if(cat.classList.contains('collapsed')) {
            body.style.display = 'none';
        } else {
            body.style.display = '';
        }
    });
});

// 默认只展开前3类
document.querySelectorAll('.category').forEach((cat,i)=>{
    if(i>2){
        cat.classList.add('collapsed');
        cat.querySelector('.category-body').style.display = 'none';
    }
});
</script>
</body>
</html>