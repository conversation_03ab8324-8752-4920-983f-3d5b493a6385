import pygame
import pymunk
import pymunk.pygame_util
import math
import numpy as np
from collections import deque

# 初始化Pygame
pygame.init()

# 屏幕设置
WIDTH, HEIGHT = 800, 600
screen = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption("交叉旋转正六边形物理模拟")

# 颜色定义
BLUE = (0, 0, 255)
GREEN = (0, 255, 0)
RED = (255, 0, 0)
PURPLE = (128, 0, 128)
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
GRAY = (200, 200, 200)

# 物理世界设置
space = pymunk.Space()
space.gravity = (0, 500)  # 向下的重力
draw_options = pymunk.pygame_util.DrawOptions(screen)

# 正六边形参数
HEX_RADIUS = 120
HEX_SIDES = 6
HEX_LEFT_POS = (WIDTH / 2 - 80, HEIGHT / 2)
HEX_RIGHT_POS = (WIDTH / 2 + 80, HEIGHT / 2)
ROTATION_SPEED = 0.5  # 旋转速度（弧度/秒）

# 小球参数
BALL_RADIUS = 10
BALL_MASS = 1
BALL_ELASTICITY = 0.8  # 弹性系数

# 轨迹记录
trail_length = 100
ball_trail = deque(maxlen=trail_length)


def create_regular_polygon(position, radius, sides, color, space, static=True):
    """创建正多边形"""
    angle = 0
    vertices = []

    # 计算顶点
    for i in range(sides):
        angle = 2 * math.pi * i / sides
        x = position[0] + radius * math.cos(angle)
        y = position[1] + radius * math.sin(angle)
        vertices.append((x, y))

    if static:
        body = pymunk.Body(body_type=pymunk.Body.KINEMATIC)
    else:
        moment = pymunk.moment_for_poly(1, vertices)
        body = pymunk.Body(1, moment)

    body.position = position
    shape = pymunk.Poly(body, vertices)
    shape.collision_type = 1
    shape.elasticity = 0.9
    shape.friction = 0.5
    shape.color = color

    space.add(body, shape)
    return body, shape


def create_ball(position, radius, mass, space):
    """创建小球"""
    moment = pymunk.moment_for_circle(mass, 0, radius)
    body = pymunk.Body(mass, moment)
    body.position = position

    shape = pymunk.Circle(body, radius)
    shape.elasticity = BALL_ELASTICITY
    shape.friction = 0.5
    shape.collision_type = 2

    space.add(body, shape)
    return body, shape


def calculate_overlap_polygon(hex1_vertices, hex2_vertices):
    """计算两个多边形的重叠区域"""
    # 使用凸多边形裁剪算法计算重叠区域
    # 这里使用简化的方法：通过matplotlib的Path进行点包含测试
    try:
        from matplotlib.path import Path

        path1 = Path(hex1_vertices)
        path2 = Path(hex2_vertices)

        # 创建一个细密的点网格覆盖整个区域
        x_min = min(min(v[0] for v in hex1_vertices), min(v[0] for v in hex2_vertices))
        x_max = max(max(v[0] for v in hex1_vertices), max(v[0] for v in hex2_vertices))
        y_min = min(min(v[1] for v in hex1_vertices), min(v[1] for v in hex2_vertices))
        y_max = max(max(v[1] for v in hex1_vertices), max(v[1] for v in hex2_vertices))

        step = 5
        x_grid, y_grid = np.meshgrid(np.arange(x_min, x_max, step), np.arange(y_min, y_max, step))
        points = np.vstack([x_grid.ravel(), y_grid.ravel()]).T

        # 找出同时位于两个多边形内的点
        mask1 = path1.contains_points(points)
        mask2 = path2.contains_points(points)
        overlap_points = points[mask1 & mask2]

        return overlap_points
    except ImportError:
        # 如果matplotlib不可用，返回空列表
        return []


def apply_rotational_effect(ball_body, hex_body, rotation_speed, collision_point):
    """应用旋转面对小球的角动量影响"""
    # 计算旋转向量
    rotation_dir = 1 if rotation_speed > 0 else -1

    # 计算碰撞点的切向速度
    r_vector = collision_point - hex_body.position
    r_perp = pymunk.Vec2d(-r_vector.y, r_vector.x).normalized()
    tangential_velocity = r_perp * rotation_speed * r_vector.length

    # 施加切向力
    force_magnitude = 1000  # 可调整的力大小
    ball_body.apply_impulse_at_world_point(tangential_velocity * force_magnitude * rotation_dir, collision_point)


# 创建正六边形
hex_left_body, hex_left_shape = create_regular_polygon(HEX_LEFT_POS, HEX_RADIUS, HEX_SIDES, BLUE, space)
hex_right_body, hex_right_shape = create_regular_polygon(HEX_RIGHT_POS, HEX_RADIUS, HEX_SIDES, GREEN, space)


# 获取重叠区域中心
def get_overlap_center():
    hex_left_vertices = []
    for v in hex_left_shape.get_vertices():
        hex_left_vertices.append(hex_left_body.position + v.rotated(hex_left_body.angle))

    hex_right_vertices = []
    for v in hex_right_shape.get_vertices():
        hex_right_vertices.append(hex_right_body.position + v.rotated(hex_right_body.angle))

    # 使用更简单的方法：取两个中心点的平均值作为重叠区域中心
    return ((hex_left_body.position.x + hex_right_body.position.x) / 2,
            (hex_left_body.position.y + hex_right_body.position.y) / 2)


# 创建小球
overlap_center = get_overlap_center()
ball_body, ball_shape = create_ball(overlap_center, BALL_RADIUS, BALL_MASS, space)


# 碰撞处理函数
def collision_handler(arbiter, space, data):
    shapes = arbiter.shapes
    ball_shape = None
    hex_shape = None

    if isinstance(shapes[0], pymunk.Circle):
        ball_shape = shapes[0]
        hex_shape = shapes[1]
    elif isinstance(shapes[1], pymunk.Circle):
        ball_shape = shapes[1]
        hex_shape = shapes[0]

    if ball_shape and hex_shape:
        collision_point = arbiter.contact_point_set.points[0].point_a

        # 确定是哪个六边形
        if hex_shape == hex_left_shape:
            apply_rotational_effect(ball_body, hex_left_body, -ROTATION_SPEED, collision_point)
        elif hex_shape == hex_right_shape:
            apply_rotational_effect(ball_body, hex_right_body, ROTATION_SPEED, collision_point)

    return True


# 添加碰撞处理
handler = space.add_collision_handler(1, 2)
handler.pre_solve = collision_handler


# 限制小球在重叠区域
def constrain_ball():
    """确保小球不会离开重叠区域"""
    hex_left_vertices = []
    for v in hex_left_shape.get_vertices():
        hex_left_vertices.append(hex_left_body.position + v.rotated(hex_left_body.angle))

    hex_right_vertices = []
    for v in hex_right_shape.get_vertices():
        hex_right_vertices.append(hex_right_body.position + v.rotated(hex_right_body.angle))

    try:
        from matplotlib.path import Path

        path_left = Path(hex_left_vertices)
        path_right = Path(hex_right_vertices)

        # 检查小球是否在两个六边形的重叠区域内
        ball_pos = (ball_body.position.x, ball_body.position.y)
        in_left = path_left.contains_point(ball_pos)
        in_right = path_right.contains_point(ball_pos)

        if not (in_left and in_right):
            # 如果小球离开重叠区域，将其位置重置到重叠中心
            overlap_center = get_overlap_center()
            ball_body.position = overlap_center
            ball_body.velocity = (0, 0)
    except ImportError:
        # 如果matplotlib不可用，使用简单的距离检查
        if (ball_body.position - hex_left_body.position).length > HEX_RADIUS or \
                (ball_body.position - hex_right_body.position).length > HEX_RADIUS:
            overlap_center = get_overlap_center()
            ball_body.position = overlap_center
            ball_body.velocity = (0, 0)


# 主循环
clock = pygame.time.Clock()
running = True

while running:
    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False

    # 更新旋转
    hex_left_body.angle += -ROTATION_SPEED / 60  # 逆时针旋转
    hex_right_body.angle += ROTATION_SPEED / 60  # 顺时针旋转

    # 约束小球在重叠区域
    constrain_ball()

    # 记录小球轨迹
    ball_trail.append((int(ball_body.position.x), int(ball_body.position.y)))

    # 物理更新
    space.step(1 / 60.0)

    # 绘制
    screen.fill(WHITE)

    # 绘制六边形
    # 左侧六边形（蓝色）
    hex_left_vertices = []
    for v in hex_left_shape.get_vertices():
        hex_left_vertices.append(hex_left_body.position + v.rotated(hex_left_body.angle))
    pygame.draw.polygon(screen, BLUE, hex_left_vertices, 2)

    # 右侧六边形（绿色）
    hex_right_vertices = []
    for v in hex_right_shape.get_vertices():
        hex_right_vertices.append(hex_right_body.position + v.rotated(hex_right_body.angle))
    pygame.draw.polygon(screen, GREEN, hex_right_vertices, 2)

    # 尝试绘制重叠区域
    overlap_points = calculate_overlap_polygon(hex_left_vertices, hex_right_vertices)
    if len(overlap_points) > 0:
        for point in overlap_points:
            pygame.draw.circle(screen, PURPLE, (int(point[0]), int(point[1])), 1)

    # 绘制小球轨迹
    for i, pos in enumerate(ball_trail):
        alpha = int(255 * i / len(ball_trail))
        color = (min(255, RED[0] * alpha // 255),
                 min(255, RED[1] * alpha // 255),
                 min(255, RED[2] * alpha // 255))
        pygame.draw.circle(screen, color, pos, 1)

    # 绘制小球
    pygame.draw.circle(screen, RED,
                       (int(ball_body.position.x), int(ball_body.position.y)),
                       BALL_RADIUS)

    # 显示物理参数
    font = pygame.font.Font(None, 24)
    velocity_text = font.render(f"速度: {ball_body.velocity.length:.1f}", True, BLACK)
    screen.blit(velocity_text, (10, 10))

    rotation_text = font.render(f"左六边形角度: {math.degrees(hex_left_body.angle):.1f}°", True, BLACK)
    screen.blit(rotation_text, (10, 40))

    rotation_text = font.render(f"右六边形角度: {math.degrees(hex_right_body.angle):.1f}°", True, BLACK)
    screen.blit(rotation_text, (10, 70))

    pygame.display.flip()
    clock.tick(60)

pygame.quit()