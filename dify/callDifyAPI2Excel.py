
import requests
import json
import pandas as pd

# API请求的URL，注意替换为你的实际端口号（如未修改端口，默认不需要加端口号）
url = 'http://10.10.29.56/v1/chat-messages'

# 应用密钥
api_key = "app-GAWdsa5xaHiTU8LbVlOkF2Jf"

# 请求头
headers = {
    'Authorization': f'Bearer {api_key}',
    'Content-Type': 'application/json',
}

# Excel文件路径
excel_path = r'C:\Users\<USER>\Desktop\问题列表.xlsx'

# 读取Excel文件
df = pd.read_excel(excel_path)

# 打开TXT文件准备写入（追加模式）
with open(excel_path, 'a', encoding='utf-8') as f:
    # 存储API返回数据的列表
    output_list = []

    # 遍历Excel中的【问题】列
    for index, row in df.iterrows():
        question = row['问题']  # 假设【问题】列的名称为'问题'

        # 请求数据
        data = {
            "inputs": {},
            "query": question,
            "response_mode": "blocking",
            "conversation_id": "",
            "user": "abc-123"
        }

        # 发送POST请求
        response = requests.post(url, headers=headers, data=json.dumps(data))

        # 检查响应状态码
        if response.status_code == 200:
            output_list.append(response.json().get('answer'))
            print("第" + str(index + 1) +"个问题请求成功！")
        else:
            print(f"第{index + 1}个问题请求失败，状态码：{response.status_code}")
            output_list.append(None)

    # 添加新的列（输出）
    df['Qwen-Max匹配车型ID输出'] = output_list

# 将DataFrame写入Excel文件
df.to_excel(excel_path, index=False)
print("数据处理完成，结果已存入Excel文件。")

