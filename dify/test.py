import os
import base64
from openai import OpenAI
import json

def image_to_base64(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

# 图片路径
image_path = r"C:\Users\<USER>\Desktop\长城-2.png"
image_base64 = image_to_base64(image_path)

client = OpenAI(
    api_key="sk-7b9067ef70794d7286da8432be28f18c",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)

completion = client.chat.completions.create(
    model="qwen-vl-plus",
    messages=[{
        "role": "user",
        "content": [
            {"type": "text", "text": "提取图片内容，并按原格式用html输出"},
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/png;base64,{image_base64}"
                }
            }
        ]
    }]
)


mid_result = completion.model_dump_json()
result = json.loads(mid_result)  # 解析 JSON 字符串

# 检查结果是否包含有效响应

if 'choices' in result and len(result['choices']) > 0:
    result = result['choices'][0]['message']['content']
else:
    result = "未收到有效响应"

dir_path = r"D:\PycahrmProjects\AutoGen\dify"
file_name = os.path.splitext(os.path.basename(image_path))[0]
save_path = os.path.join(dir_path, f"{file_name}.html")
print(f"\n结果已保存至：{save_path}")
print(result)