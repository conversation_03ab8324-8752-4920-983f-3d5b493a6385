import os
from openai import OpenAI
import pandas as pd

client = OpenAI(
    # 若没有配置环境变量，请用百炼API Key将下行替换为：api_key="sk-xxx",
    api_key="sk-7b9067ef70794d7286da8432be28f18c",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)

# Excel文件路径
excel_path = r'C:\Users\<USER>\Desktop\IT Demo测试问题集.xlsx'

# 读取Excel文件
df = pd.read_excel(excel_path)

with open(excel_path, 'a', encoding='utf-8') as f:
    # 存储API返回数据的列表
    output_list = []

    # 遍历Excel中的【问题】列
    for index, row in df.iterrows():
        question = row['问题描述']  # 假设【问题】列的名称为'问题'

        try:
            completion = client.chat.completions.create(
                model="qwen3-30b-a3b",
                # 此处以qwen-plus为例，可按需更换模型名称。模型列表：https://help.aliyun.com/zh/model-studio/getting-started/models
                messages=[
                    {'role': 'system', 'content': """## 角色  
                                                    你是一名遵循严格数据规范的分析专家，能够精准结构化解析用户查询。
                                                    
                                                    ## 任务要求  
                                                    将用户问题拆解为以下四部分，**严格按格式输出**：  
                                                    
                                                    ### 1. 时间周期类型（time_field）  
                                                    - **定义**：用户问题中时间颗粒度（年/季/月/半月/周）  
                                                    - **规则**：  
                                                      ▫️ 仅输出时间周期类型，不包含具体时间值  
                                                      ▫️ 示例：  
                                                      ```text  
                                                      用户问"2025年Q3数据" → 输出"季"  
                                                      用户问"最近三个月" → 输出"月"  
                                                      ```  
                                                    
                                                    ### 2. 维度类型（dimension_field）  
                                                    - **定义**：从预置维度列表匹配（完整列表见下文）  
                                                    - **规则**：  
                                                      ▫️ 必须完全匹配以下字段：
                                                        - 车型（如Model 3、CR-V）
                                                        - 品牌（如特斯拉、比亚迪）
                                                        - 月份（如三月、12月）
                                                        - 年份（如2024年、25年）
                                                        - 季度（如24年Q4、25年第一季度）
                                                        - 大区（如华北区、东北区、华东区、华中区、华南区）
                                                        - 省份（如广东省、浙江省、江西省）
                                                        - 城市（如北京市、广州市、深圳市）
                                                        - 一级细分市场（如小型轿车、紧凑型轿车、中型轿车、中大型轿车、大型轿车、小型SUV、紧凑型SUV、中型SUV、中大型SUV、紧凑型MPV、中大型MPV、跑车）
                                                        - 二级细分市场（如A00、A0-L、A0-H、A-L、A-M）
                                                        - 系别（如德系、法系、韩系、美系、欧系、日系、自主、其他）
                                                        - 品牌（如蔚来、理想、小鹏、特斯拉、小米）
                                                        - 厂商（如比亚迪、阿维塔）
                                                        - 厂商品牌（如东风日产-日产、东风风行）
                                                        - 市场属性（）
                                                        - 产地属性（如合资、自主、进口、合资自主、外商独资）
                                                        - 品牌属性（如豪华、非豪华）
                                                        - 汽车类型（如MPV、SUV、轿车、跑车）
                                                        - 车身形式（如SUV、厢式轿车、跑车、MPV、两厢、敞篷跑车、三厢）
                                                        - 燃料类型-1（如传统能源、新能源、油车、电车）
                                                        - 燃料类型-2（如汽油、柴油、非插电式增程型电动、双燃料、非插电式混合动力、非插电式轻混合动力、纯电动、插电式混合动力、增程型电动、燃料电池）
                                                        - 车型（如Model Y、理想L7、轩逸）
                                                        - 型号名称（如理想L6 四驱Pro、SU7 长续航智驾版）
                                                        - 型号类型（如入门型号、顶配型号、主销型号、其他）
                                                        - TP价格分段（如TP价1万一段、TP 2万一个段）
                                                        - MSRP价格分段（如MSRP价1万一段、官方指导价2万一个段）
                                                    
                                                    ### 3. 指标类型（metrics）  
                                                    - **定义**：从预置指标列表匹配（完整列表见下文）  
                                                    - **规则**：  
                                                      ▫️ 必须完全匹配以下字段：  
                                                    CPCA-零售量、CPCA-零售量同比、CPCA-零售量环比、CPCA-零售量同比变化、CPCA-零售量环比变化、CPCA-零售量份额、CPCA-零售量份额同比、CPCA-零售量份额环比、CPCA-批发量、CPCA-批发量同比、CPCA-批发量环比、CPCA-批发量同比变化、CPCA-批发量环比变化、CPCA-批发量份额、CPCA-批发量份额同比、CPCA-批发量份额环比、CPCA-零售量同期销量、CPCA-批发量同期销量、SX-销量、SX-同比、SX-同期销量、SX-环比、SX-同比变化、SX-环比变化、SX-份额、SX-份额同比、SX-份额环比、ZQX-批发量、ZQX-批发量同比、ZQX-批发量环比、ZQX-批发量同比变化、ZQX-批发量环比变化、ZQX-批发量份额、ZQX-批发量份额同比、ZQX-批发量份额环比、ZQX-批发量同期销量、MIX、MIX环比、MIX同比、TP指数、TP指数变化、TP、TP同比、TP环比、TP累计变化、折扣、折扣同比、折扣环比、折扣累计变化、折扣率、折扣率环比、折扣率累计变化、MSRP、MSRP指数。
                                                    注：CPCA指乘联会，SX指上险，ZQX指中汽协，TP指成交价
                                                    
                                                    ### 4. 筛选条件（filter_condition）  
                                                    - **必须包含两类数据**：  
                                                      **a) 具体时间值**  
                                                      ```text  
                                                      ▫️ 字段命名规则：  
                                                        年 → 年=yyyy  
                                                        季 → 季=yyyyQN（例：2025Q1）  
                                                        月 → 月=yyyyMM（例：202502）  
                                                        周 → 周=yyyyww（例：202507）  
                                                      ```  
                                                    
                                                      **b) 维度**  
                                                      ```text  
                                                      ▫️ 格式：维度字段名=维度名称  
                                                      ▫️ 示例：  
                                                        用户问"秦L" → 车型=秦L  
                                                        用户问"上海市" → 城市=上海市
                                                        用户问"比亚迪 海鸥 2023 EV 电机 飞翔版", "比亚迪 海鸥 2023 EV 电机 自由版"→ 型号名称=比亚迪 海鸥 2023 EV 电机 飞翔版，比亚迪 海鸥 2023 EV 电机 自由版
                                                    
                                                      ```  
                                                    
                                                    ## 知识库约束  
                                                    {{#context#}}  
                                                    1. **强匹配原则**：  
                                                       - 所有筛选值必须与知识库中的ID/名称**完全一致**  
                                                       - 禁止编造不存在的数据  
                                                    
                                                    2. **双重校验机制**：  
                                                       ▫️ 第一步：检查维度/指标是否在预置列表中  
                                                       ▫️ 第二步：检查筛选值是否存在于知识库  
                                                    
                                                    ## 输出格式  
                                                    ```text  
                                                    time_field：[时间周期类型 | 空]  
                                                    dimension_field：[维度类型 | 空]  
                                                    metrics：[指标类型 | 空]  
                                                    filter_condition：[字段1=筛选值1, 字段2=筛选值2,筛选值3| 空]  
                                                    ```  
                                                    
                                                    ---
                                                    
                                                    ## 验证案例  
                                                    **案例1**  
                                                    问题：2025年1到6月秦L的销量是多少？  
                                                    知识库：车型表包含`秦L → 车型ID=秦L`  
                                                    ```text  
                                                    time_field：月  
                                                    dimension_field：车型  
                                                    metrics：SX-销量  
                                                    filter_condition：月=202501,202506, 车型=秦L  
                                                    ```  
                                                    
                                                    **案例2**  
                                                    问题：2024Q3浙江省新能源车上险量TOP5品牌  
                                                    ```text  
                                                    time_field：季  
                                                    dimension_field：省份,品牌  
                                                    metrics：SX-销量  
                                                    filter_condition：季=2024Q3, 省份=浙江省, 燃料类型-1=1  
                                                    ```  
                                                    
                                                    ## 执行原则  
                                                    1. **严格性**：未明确提及的字段不输出  
                                                    2. **原子性**：每个字段独立判断，不合并处理  
                                                    3. **可追溯性**：所有筛选值必须能映射到知识库  
                                                            """},
                    {'role': 'user', 'content': question}],
                stream=True,
                # enable_thinking=True
            )
        except Exception as e:
            print("请求失败，错误信息：", str(e))

        # completion.model_dump_json()
        # output_list.append(completion['choices'][0]['message']['content'])
        # print(completion.__getstate__())
        # response_content = completion.choices[0].message.content
        # response_content = completion.model_dump_json()
        # output_list.append(response_content)
        response_content = ""
        for chunk in completion:
            if hasattr(chunk, "choices") and chunk.choices:
                response_content += chunk.choices[0].delta.content or ""

        output_list.append(response_content)
        print("第" + str(index + 1) + "个问题请求成功！")
        print("模型输出：" + response_content)
    # 添加新的列（输出）
    df['qwen3-30b-a3b输出'] = output_list

# 将DataFrame写入Excel文件
df.to_excel(excel_path, index=False)
print("数据处理完成，结果已存入Excel文件。")





