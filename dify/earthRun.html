<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <title>太阳系模拟动画</title>
  <style>
    html, body {
      height: 100%;
      margin: 0;
    }
    body {
      background: #080f1c;
      overflow: hidden;
      font-family: "Segoe UI", "PingFang SC", Arial, sans-serif;
      user-select: none;
    }
    #solarSystemCanvas {
      display: block;
      position: absolute;
      left: 0; top: 0;
      width: 100vw; height: 100vh;
      background: radial-gradient(ellipse at center, #0b1630 0%, #00081e 100%);
      z-index: 0;
      cursor: grab;
    }
    #controls {
      position: absolute;
      left: 20px; top: 24px;
      background: rgba(16,25,35,0.85);
      color: #fff;
      border-radius: 10px;
      box-shadow: 0 2px 10px #0004;
      padding: 18px 20px 12px 20px;
      z-index: 10;
      min-width: 180px;
      font-size: 1rem;
      user-select: none;
    }
    #controls label {
      margin-right: 12px;
      cursor: pointer;
    }
    #controls .btn {
      background: #1e2942;
      color: #fff;
      border: none;
      border-radius: 5px;
      font-size: 1rem;
      padding: 4px 12px;
      margin: 0 4px;
      cursor: pointer;
      box-shadow: 0 1px 3px #0002;
      transition: background 0.2s;
    }
    #controls .btn:hover {
      background: #254577;
    }
    #infoPanel {
      position: absolute;
      right: 25px;
      top: 32px;
      min-width: 190px;
      max-width: 300px;
      background: rgba(16,26,40,0.93);
      color: #ffe;
      border-radius: 12px;
      box-shadow: 0 3px 16px #0007;
      z-index: 20;
      display: none;
      padding: 22px 24px 18px 24px;
      font-size: 1.07rem;
      pointer-events: none;
    }
    #infoPanel h2 {
      margin: 0 0 10px 0;
      font-size: 1.4em;
      letter-spacing: .06em;
      color: #71b3ff;
      font-weight: normal;
    }
    #infoPanel p {
      margin: 0 0 7px 0;
      line-height: 1.6;
    }
    #closeInfoBtn {
      position: absolute;
      right: 13px; top: 7px;
      border: none;
      background: none;
      color: #ff8080;
      font-size: 1.3em;
      cursor: pointer;
      z-index: 30;
      pointer-events: auto;
      font-weight: bold;
    }
    @media (max-width: 600px) {
      #controls {font-size: 0.95rem; padding: 11px 10px 6px 10px;}
      #infoPanel {right:6vw;top:13vw;max-width:96vw;padding:10px 10px 6px 12px;font-size:0.98rem;}
    }
  </style>
</head>
<body>
<canvas id="solarSystemCanvas"></canvas>
<div id="controls">
  <div><label><input type="checkbox" id="showOrbits" checked> 显示轨道</label></div>
  <div style="margin-top:10px;">
    <button class="btn" id="zoomInBtn">放大</button>
    <button class="btn" id="zoomOutBtn">缩小</button>
    <span id="resetViewBtn" class="btn">重置</span>
  </div>
  <div style="margin-top:10px; font-size:0.93em;color:#cae1ff88;">鼠标拖动画布，滚轮缩放</div>
</div>
<div id="infoPanel">
  <button id="closeInfoBtn">&times;</button>
  <h2 id="infoTitle">行星名称</h2>
  <p id="infoText">行星信息</p>
</div>
<script>
/**
 * 太阳系天体参数（比例适当缩放，非真实值）
 * 距离和尺寸均为视觉缩放值
 */
const PLANETS = [
  // name, color, orbital radius(px), diameter(px), revolution period(天), satellites, info
  {
    name: "水星", color: "#bcbcbc", orbit: 60, size: 4, period: 87.97, satellites: [],
    info: "水星(Mercury)是距离太阳最近的行星，体积最小，无卫星。"
  },
  {
    name: "金星", color: "#efc76b", orbit: 90, size: 7, period: 224.70, satellites: [],
    info: "金星(Venus)与地球相似，但表面高温、气压大、无卫星。"
  },
  {
    name: "地球", color: "#3399ff", orbit: 120, size: 8, period: 365.26,
    satellites: [
      {name:"月球", color:"#bbb", orbit:12, size:2.5, period:27.32, info:"月球是地球唯一的自然卫星。"}
    ],
    info: "地球(Earth)是太阳系唯一存在生命的星球，拥有一颗卫星——月球。"
  },
  {
    name: "火星", color: "#d66d3b", orbit: 160, size: 6, period: 686.98, satellites: [],
    info: "火星(Mars)被称为“红色星球”，有两颗小卫星（未显示）。"
  },
  {
    name: "木星", color: "#e1a04a", orbit: 220, size: 14, period: 4332.59,
    satellites: [
      {name:"木卫一", color:"#d8d8a3", orbit:18, size:2, period:1.77, info:"木卫一(Io)是太阳系火山最活跃的天体。"},
      {name:"木卫二", color:"#b0e0e6", orbit:24, size:2.3, period:3.55, info:"木卫二(Europa)可能有地下海洋。"},
      {name:"木卫三", color:"#e9c088", orbit:30, size:2.9, period:7.15, info:"木卫三(Ganymede)是太阳系最大卫星。"},
      {name:"木卫四", color:"#a07e6e", orbit:37, size:2.4, period:16.69, info:"木卫四(Callisto)表面多环形山。"},
    ],
    info: "木星(Jupiter)是太阳系最大行星，拥有79颗卫星。"
  },
  {
    name: "土星", color: "#dfbe81", orbit: 285, size: 12, period: 10759.22,
    satellites: [
      {name:"泰坦", color:"#e4cc94", orbit:22, size:3, period:15.95, info:"泰坦(Titan)是土星最大卫星，有浓厚大气。"}
    ],
    info: "土星(Saturn)以壮观的光环著称，卫星众多。"
  },
  {
    name: "天王星", color: "#8fd9e6", orbit: 340, size: 10, period: 30688.5, satellites: [],
    info: "天王星(Uranus)自转轴几乎平躺，呈青绿色。"
  },
  {
    name: "海王星", color: "#486efc", orbit: 390, size: 10, period: 60182, satellites: [],
    info: "海王星(Neptune)最远离太阳，呈深蓝色。"
  }
];

// 小行星带参数
const ASTEROID_BELT = {
  count: 180, // 小行星数量
  inner: 180, // 内圈
  outer: 215, // 外圈
  minSize: 1, maxSize: 2.2
};

const BG_STAR_COUNT = 180; // 背景星星数量

// 太阳参数
const SUN = {
  size: 28, // 视觉半径(px)
  color1: "#ffe27d", // 外辉
  color2: "#fff8b8", // 中层
  color3: "#fffbf0", // 中心
  pulseSpeed: 2.2 // 脉动速度系数
};

// 缩放与拖拽参数
let scale = 1; // 画布缩放
let offsetX = 0, offsetY = 0; // 拖拽偏移
let dragging = false, dragStart = null, lastOffset = null;
const MIN_SCALE = 0.5, MAX_SCALE = 2.5;

// 轨道显示
let showOrbits = true;

// 画布与上下文
const canvas = document.getElementById('solarSystemCanvas');
const ctx = canvas.getContext('2d');
let W = window.innerWidth, H = window.innerHeight;

// 信息面板
const infoPanel = document.getElementById('infoPanel');
const infoTitle = document.getElementById('infoTitle');
const infoText = document.getElementById('infoText');
const closeInfoBtn = document.getElementById('closeInfoBtn');

// 生成背景星
const BG_STARS = [];
for(let i=0;i<BG_STAR_COUNT;++i){
  BG_STARS.push({
    x: Math.random(),
    y: Math.random(),
    r: 0.32+Math.random() * 1.3,
    a: 0.5+Math.random()*0.5
  });
}

// 小行星带
const ASTEROIDS = [];
for(let i=0;i<ASTEROID_BELT.count;++i){
  let angle = Math.random()*2*Math.PI;
  let radius = ASTEROID_BELT.inner + Math.random()*(ASTEROID_BELT.outer-ASTEROID_BELT.inner);
  let size = ASTEROID_BELT.minSize + Math.random()*(ASTEROID_BELT.maxSize-ASTEROID_BELT.minSize);
  let speed = 0.14 + Math.random()*0.12; // 转速分布
  ASTEROIDS.push({
    baseAngle: angle,
    radius: radius,
    size: size,
    speed: speed * (Math.random()>0.5?1:-1),
    color: Math.random()<0.12?'#d3c3b2':'#8b8378'
  });
}

// --- 动画主循环 ---
function draw(){
  ctx.clearRect(0,0,W,H);

  // 背景星空
  for(const s of BG_STARS){
    ctx.save();
    ctx.globalAlpha = s.a;
    ctx.beginPath();
    ctx.arc(s.x*W, s.y*H, s.r, 0, 2*Math.PI);
    ctx.fillStyle = "#fff";
    ctx.shadowColor = "#fff";
    ctx.shadowBlur = 4.7;
    ctx.fill();
    ctx.restore();
  }

  // 变换中心
  ctx.save();
  ctx.translate(W/2+offsetX, H/2+offsetY);
  ctx.scale(scale, scale);

  // 时间参数
  const now = Date.now()/1000;
  const t = now/5; // 时间基准缩放, 1秒=5地球天，整体更快

  // 绘制轨道
  if(showOrbits){
    // 各行星轨道
    for(const p of PLANETS){
      ctx.save();
      ctx.beginPath();
      ctx.strokeStyle = "rgba(170,200,255,0.21)";
      ctx.lineWidth = 1.12/scale;
      ctx.arc(0,0,p.orbit,0,2*Math.PI);
      ctx.stroke();
      ctx.restore();

      // 卫星轨道
      if(p.satellites){
        for(const m of p.satellites){
          ctx.save();
          ctx.beginPath();
          ctx.strokeStyle = "rgba(200,210,255,0.17)";
          ctx.lineWidth = 0.7/scale;
          ctx.arc(
            Math.cos(2*Math.PI*(t/p.period))*p.orbit,
            Math.sin(2*Math.PI*(t/p.period))*p.orbit,
            m.orbit,0,2*Math.PI);
          ctx.stroke();
          ctx.restore();
        }
      }
    }
    // 小行星带朦胧
    ctx.save();
    ctx.strokeStyle = "rgba(160,170,200,0.11)";
    ctx.lineWidth = 9/scale;
    ctx.beginPath();
    ctx.arc(0,0, (ASTEROID_BELT.inner+ASTEROID_BELT.outer)/2,
      0,2*Math.PI);
    ctx.stroke();
    ctx.restore();
  }

  // 绘制小行星带
  for(let ast of ASTEROIDS){
    // 动态角度
    let angle = ast.baseAngle + t * ast.speed * 0.05;
    let px = Math.cos(angle)*ast.radius;
    let py = Math.sin(angle)*ast.radius;
    ctx.save();
    ctx.globalAlpha = 0.45 + Math.random()*0.4;
    ctx.beginPath();
    ctx.arc(px, py, ast.size, 0, 2*Math.PI);
    ctx.fillStyle = ast.color;
    ctx.shadowColor = ast.color;
    ctx.shadowBlur = Math.random()>0.8?8:3;
    ctx.fill();
    ctx.restore();
  }

  // 绘制太阳
  drawSun(now);

  // 绘制行星及其卫星
  for(let pIdx=0;pIdx<PLANETS.length;++pIdx){
    const p = PLANETS[pIdx];
    // 计算当前行星位置
    let angle = 2*Math.PI*(t/p.period);
    let px = Math.cos(angle)*p.orbit;
    let py = Math.sin(angle)*p.orbit;

    // 轨道上发光点
    ctx.save();
    ctx.shadowColor = "#fff";
    ctx.shadowBlur = 10+6*p.size;
    ctx.globalAlpha = 0.74;
    ctx.beginPath();
    ctx.arc(px, py, p.size*1.13+2, 0, 2*Math.PI);
    ctx.fillStyle = p.color;
    ctx.fill();
    ctx.restore();

    // 行星本体
    ctx.save();
    ctx.beginPath();
    ctx.arc(px, py, p.size, 0, 2*Math.PI);
    ctx.fillStyle = p.color;
    ctx.shadowColor = p.color;
    ctx.shadowBlur = 8;
    ctx.globalAlpha = 0.97;
    ctx.fill();
    ctx.restore();

    // 土星光环
    if(p.name==="土星"){
      ctx.save();
      ctx.globalAlpha = 0.54;
      ctx.strokeStyle = "#ebdfb5";
      ctx.lineWidth = 2.6/scale;
      ctx.beginPath();
      ctx.ellipse(px, py, p.size*1.8, p.size*1.2, angle/1.7, 0, 2*Math.PI);
      ctx.stroke();
      ctx.restore();
    }

    // 卫星们
    if(p.satellites){
      for(let mIdx=0;mIdx<p.satellites.length;++mIdx){
        const m = p.satellites[mIdx];
        let moonAng = 2*Math.PI*(t/m.period + mIdx/3);
        let mx = px + Math.cos(moonAng)*m.orbit;
        let my = py + Math.sin(moonAng)*m.orbit;

        // 卫星阴影环
        ctx.save();
        ctx.globalAlpha = 0.18;
        ctx.beginPath();
        ctx.arc(mx, my, m.size*2.1, 0, 2*Math.PI);
        ctx.fillStyle = "#fff";
        ctx.fill();
        ctx.restore();

        // 卫星本体
        ctx.save();
        ctx.shadowColor = "#fff";
        ctx.shadowBlur = 4.2;
        ctx.globalAlpha = 0.86;
        ctx.beginPath();
        ctx.arc(mx, my, m.size, 0, 2*Math.PI);
        ctx.fillStyle = m.color;
        ctx.fill();
        ctx.restore();
      }
    }
  }

  ctx.restore();

  requestAnimationFrame(draw);
}

// 绘制太阳（发光脉动）
function drawSun(now){
  let pulse = 0.82 + Math.sin(now * SUN.pulseSpeed) * 0.07;
  ctx.save();
  // 外层光晕
  let r = SUN.size*2.8*pulse;
  let gradient = ctx.createRadialGradient(0,0,0,0,0,r);
  gradient.addColorStop(0, SUN.color2);
  gradient.addColorStop(0.25, SUN.color1+'99');
  gradient.addColorStop(0.85, 'rgba(255,240,200,0.08)');
  ctx.beginPath();
  ctx.arc(0,0, r, 0, 2*Math.PI);
  ctx.fillStyle = gradient;
  ctx.globalAlpha = 0.7;
  ctx.fill();
  // 核心
  ctx.beginPath();
  ctx.arc(0,0, SUN.size*1.1*pulse, 0, 2*Math.PI);
  ctx.fillStyle = SUN.color2;
  ctx.globalAlpha = 0.9;
  ctx.shadowColor = "#fffbb3";
  ctx.shadowBlur = 15;
  ctx.fill();
  ctx.beginPath();
  ctx.arc(0,0, SUN.size*0.68*pulse, 0, 2*Math.PI);
  ctx.fillStyle = SUN.color3;
  ctx.globalAlpha = 1;
  ctx.shadowColor = "#fffddc";
  ctx.shadowBlur = 28;
  ctx.fill();
  ctx.restore();
}

// 画布自适应
function resize(){
  W = window.innerWidth;
  H = window.innerHeight;
  canvas.width = W * window.devicePixelRatio;
  canvas.height = H * window.devicePixelRatio;
  canvas.style.width = W + "px";
  canvas.style.height = H + "px";
  ctx.setTransform(1,0,0,1,0,0);
  ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
}
window.addEventListener('resize', resize);

// 鼠标拖动缩放
canvas.addEventListener('mousedown', e=>{
  dragging = true;
  dragStart = {x: e.clientX, y: e.clientY};
  lastOffset = {x: offsetX, y: offsetY};
  canvas.style.cursor = 'grabbing';
});
window.addEventListener('mousemove', e=>{
  if(dragging){
    offsetX = lastOffset.x + (e.clientX-dragStart.x);
    offsetY = lastOffset.y + (e.clientY-dragStart.y);
  }
});
window.addEventListener('mouseup', ()=>{
  dragging = false;
  canvas.style.cursor = 'grab';
});

// 触摸拖动
canvas.addEventListener('touchstart', e=>{
  if(e.touches.length==1){
    dragging = true;
    dragStart = {x: e.touches[0].clientX, y: e.touches[0].clientY};
    lastOffset = {x: offsetX, y: offsetY};
  }
});
window.addEventListener('touchmove', e=>{
  if(dragging && e.touches.length==1){
    offsetX = lastOffset.x + (e.touches[0].clientX-dragStart.x);
    offsetY = lastOffset.y + (e.touches[0].clientY-dragStart.y);
  }
});
window.addEventListener('touchend', ()=>{
  dragging = false;
});

// 缩放: 鼠标滚轮/双指
canvas.addEventListener('wheel', e=>{
  let oldScale = scale;
  if(e.deltaY<0) scale *= 1.12;
  else scale /= 1.12;
  scale = Math.max(MIN_SCALE, Math.min(MAX_SCALE, scale));
  // 保持鼠标附近为中心缩放
  let mx = e.clientX - W/2 - offsetX;
  let my = e.clientY - H/2 - offsetY;
  offsetX -= mx*(scale/oldScale-1);
  offsetY -= my*(scale/oldScale-1);
  e.preventDefault();
}, {passive:false});

// 控制面板
document.getElementById('showOrbits').addEventListener('change',e=>{
  showOrbits = e.target.checked;
});
document.getElementById('zoomInBtn').onclick = ()=>{
  scale = Math.min(MAX_SCALE, scale*1.2);
};
document.getElementById('zoomOutBtn').onclick = ()=>{
  scale = Math.max(MIN_SCALE, scale/1.2);
};
document.getElementById('resetViewBtn').onclick = ()=>{
  offsetX = 0; offsetY = 0; scale = 1;
};

// 点击行星/卫星显示信息
canvas.addEventListener('click', function(e){
  // 坐标转换到太阳系中心
  let x = (e.clientX-W/2-offsetX)/scale;
  let y = (e.clientY-H/2-offsetY)/scale;
  // 查找行星/卫星
  let showObj = null;
  let planetIdx = -1;
  // 卫星优先
  for(let pIdx=0;pIdx<PLANETS.length;++pIdx){
    const p = PLANETS[pIdx];
    let t = Date.now()/1000/5;
    let ang = 2*Math.PI*(t/p.period);
    let px = Math.cos(ang)*p.orbit;
    let py = Math.sin(ang)*p.orbit;
    if(p.satellites){
      for(let mIdx=0;mIdx<p.satellites.length;++mIdx){
        const m = p.satellites[mIdx];
        let moonAng = 2*Math.PI*(t/m.period + mIdx/3);
        let mx = px + Math.cos(moonAng)*m.orbit;
        let my = py + Math.sin(moonAng)*m.orbit;
        let d = Math.hypot(x-mx, y-my);
        if(d < m.size*1.8){
          showObj = m;
          showObj.parent = p.name;
          planetIdx = pIdx;
        }
      }
    }
  }
  // 行星（没有点击到卫星）
  if(!showObj){
    for(let pIdx=0;pIdx<PLANETS.length;++pIdx){
      const p = PLANETS[pIdx];
      let t = Date.now()/1000/5;
      let ang = 2*Math.PI*(t/p.period);
      let px = Math.cos(ang)*p.orbit;
      let py = Math.sin(ang)*p.orbit;
      let d = Math.hypot(x-px, y-py);
      if(d < p.size*2){
        showObj = p;
        planetIdx = pIdx;
      }
    }
  }
  // 太阳
  if(!showObj){
    let d = Math.hypot(x, y);
    if(d < SUN.size*1.25){
      infoTitle.textContent = "太阳";
      infoText.innerHTML = "太阳(Sun)是太阳系的中心恒星，质量占整个太阳系的99.86%。";
      showInfoPanel(e.clientX, e.clientY);
      return;
    }
  }
  if(showObj){
    if(showObj.parent){
      infoTitle.textContent = showObj.name + "（"+showObj.parent+"的卫星）";
      infoText.innerHTML = showObj.info || '';
    }else{
      infoTitle.textContent = showObj.name;
      infoText.innerHTML = PLANETS[planetIdx].info || '';
    }
    showInfoPanel(e.clientX, e.clientY);
  }else{
    infoPanel.style.display = "none";
  }
});
function showInfoPanel(x, y){
  infoPanel.style.display = "";
  // 靠右吸附
  let pw = infoPanel.offsetWidth, ph = infoPanel.offsetHeight;
  let ix = Math.min(x+36, W-pw-16);
  let iy = Math.min(y-18, H-ph-5);
  infoPanel.style.left = ix+"px";
  infoPanel.style.top  = iy+"px";
  infoPanel.style.pointerEvents = "auto";
}
closeInfoBtn.onclick = ()=>{
  infoPanel.style.display = "none";
  infoPanel.style.pointerEvents = "none";
};

// 点击空白关闭信息
canvas.addEventListener('dblclick', function(){
  infoPanel.style.display = "none";
  infoPanel.style.pointerEvents = "none";
});

// 初始化
resize();
draw();

</script>
</body>
</html>