<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>汽车品牌销量分析看板</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- ECharts CDN -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        :root {
            --color1: #405DF9;
            --color2: #6C63F0;
            --color3: #7342CC;
            --color4: #B750BE;
            --color5: #ED589D;
            --color6: #FB466C;
            --color7: #FFC159;
            --text: rgba(0, 0, 0, 0.85);
            --divider: rgba(0, 0, 0, 0.20);
        }
        html, body {
            background: #f8fafd;
            margin: 0;
            padding: 0;
            font-family: "HarmonyOS Sans SC", "Microsoft YaHei", Arial, sans-serif;
            color: var(--text);
        }
        .dashboard-container {
            max-width: 1200px;
            margin: 32px auto;
            background: #fff;
            border-radius: 14px;
            box-shadow: 0 2px 18px rgba(0,0,0,0.07);
            padding: 28px 32px 24px 32px;
        }
        .dashboard-title {
            text-align: center;
            font-size: 25px;
            font-weight: 700;
            margin-bottom: 2px;
            letter-spacing: 2px;
        }
        .dashboard-desc {
            text-align: center;
            font-size: 15px;
            color: #888;
            margin-bottom: 18px;
            letter-spacing: 1px;
        }
        .divider {
            height: 1px;
            margin: 20px 0 24px 0;
            background: var(--divider);
            border: none;
        }
        .chart-row {
            display: flex;
            flex-wrap: wrap;
            gap: 24px;
            margin-bottom: 24px;
        }
        .chart-block {
            flex: 1 1 340px;
            min-width: 340px;
            background: #fafbff;
            border-radius: 10px;
            box-shadow: 0 1px 6px 0 rgba(100,80,255,0.05);
            padding: 16px 12px 8px 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .chart-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--color3);
            text-align: center;
            letter-spacing: 1.2px;
        }
        .echart {
            width: 98%;
            min-width: 260px;
            height: 275px;
        }
        @media (max-width: 900px) {
            .chart-row {
                flex-direction: column;
                gap: 16px;
            }
            .chart-block {
                min-width: 250px;
            }
            .dashboard-container {
                padding: 10px 4px 16px 4px;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="dashboard-title">2024年上半年汽车品牌销量分析看板</div>
        <div class="dashboard-desc">
            数据涵盖蔚来、理想两大品牌在山东省、广东省及佛山市的月度与季度销量表现。<br>
            重点关注2024年Q2（3-6月），并可洞察各车型销量趋势和区域市场对比。
        </div>
        <hr class="divider">

        <div class="chart-row">
            <div class="chart-block">
                <div class="chart-title">省份-品牌月度累计销量对比</div>
                <div id="provinceBrandBar" class="echart"></div>
            </div>
            <div class="chart-block">
                <div class="chart-title">省份-品牌销量趋势（2024/03-2024/06）</div>
                <div id="provinceBrandLine" class="echart"></div>
            </div>
        </div>

        <div class="chart-row">
            <div class="chart-block">
                <div class="chart-title">佛山市分品牌月度销量趋势</div>
                <div id="foshanBrandLine" class="echart"></div>
            </div>
            <div class="chart-block">
                <div class="chart-title">蔚来ET7 vs 理想L6（佛山）月度销量对比</div>
                <div id="foshanModelBar" class="echart"></div>
            </div>
        </div>

        <div class="chart-row">
            <div class="chart-block">
                <div class="chart-title">蔚来ET7与理想L6 各省月度销量对比</div>
                <div id="modelProvinceRadar" class="echart"></div>
            </div>
            <div class="chart-block">
                <div class="chart-title">2024/06省份-品牌销量&TP（均价）分析</div>
                <div id="juneScatter" class="echart"></div>
            </div>
        </div>
    </div>
    <script>
        // 色板
        const colors = [
            "#405DF9", "#6C63F0", "#7342CC", "#B750BE", "#ED589D", "#FB466C", "#FFC159"
        ];

        // ===================== 数据整理 =====================
        // 省份-品牌-月度总销量对比 (2024/03~2024/06)
        const provinceBrandMonth = [
            // 月, 省份, 品牌, SX-销量, TP
            // 山东省
            {month:'2024/03', province:'山东省', brand:'蔚来', sales:257, TP:331224},
            {month:'2024/03', province:'山东省', brand:'理想', sales:1515, TP:374982},
            {month:'2024/04', province:'山东省', brand:'蔚来', sales:360, TP:307114},
            {month:'2024/04', province:'山东省', brand:'理想', sales:1392, TP:359269},
            {month:'2024/05', province:'山东省', brand:'蔚来', sales:349, TP:318954},
            {month:'2024/05', province:'山东省', brand:'理想', sales:1796, TP:325970},
            {month:'2024/06', province:'山东省', brand:'蔚来', sales:356, TP:320290},
            {month:'2024/06', province:'山东省', brand:'理想', sales:2376, TP:308903},
            // 广东省
            {month:'2024/03', province:'广东省', brand:'蔚来', sales:1367, TP:331224},
            {month:'2024/03', province:'广东省', brand:'理想', sales:7006, TP:374982},
            {month:'2024/04', province:'广东省', brand:'蔚来', sales:1646, TP:307885},
            {month:'2024/04', province:'广东省', brand:'理想', sales:4099, TP:356399},
            {month:'2024/05', province:'广东省', brand:'蔚来', sales:2449, TP:318954},
            {month:'2024/05', province:'广东省', brand:'理想', sales:4452, TP:325970},
            {month:'2024/06', province:'广东省', brand:'蔚来', sales:2111, TP:320290},
            {month:'2024/06', province:'广东省', brand:'理想', sales:9078, TP:308903}
        ];
        const months = ["2024/03","2024/04","2024/05","2024/06"];
        const provinceList = ["山东省", "广东省"];
        const brandList = ["蔚来","理想"];

        // 佛山市分品牌月度销量
        const foshanBrandMonth = [
            // 月, 品牌, SX-销量
            {month:'2024/03', brand:'蔚来', sales:4},
            {month:'2024/04', brand:'蔚来', sales:4},
            {month:'2024/04', brand:'理想', sales:36},
            {month:'2024/05', brand:'蔚来', sales:18},
            {month:'2024/05', brand:'理想', sales:232},
            {month:'2024/06', brand:'蔚来', sales:20},
            {month:'2024/06', brand:'理想', sales:362}
        ];
        // 佛山分车型销量（蔚来ET7、理想L6）
        const foshanModelMonth = [
            // 月, 品牌, 车型, SX-销量
            {month:'2024/03', brand:'蔚来', model:'蔚来ET7', sales:4},
            {month:'2024/04', brand:'蔚来', model:'蔚来ET7', sales:4},
            {month:'2024/04', brand:'理想', model:'理想L6', sales:36},
            {month:'2024/05', brand:'蔚来', model:'蔚来ET7', sales:18},
            {month:'2024/05', brand:'理想', model:'理想L6', sales:232},
            {month:'2024/06', brand:'蔚来', model:'蔚来ET7', sales:20},
            {month:'2024/06', brand:'理想', model:'理想L6', sales:362}
        ];
        // 单车型分省份销量（蔚来ET7, 理想L6）
        const modelProvinceMonth = [
            // 月, 省份, 品牌, 车型, SX-销量
            {month:'2024/03', province:'山东省', brand:'蔚来', model:'蔚来ET7', sales:6},
            {month:'2024/03', province:'广东省', brand:'蔚来', model:'蔚来ET7', sales:32},
            {month:'2024/03', province:'山东省', brand:'理想', model:'理想L6', sales:0}, // 数据无，补零
            {month:'2024/03', province:'广东省', brand:'理想', model:'理想L6', sales:4},
            {month:'2024/04', province:'山东省', brand:'蔚来', model:'蔚来ET7', sales:10},
            {month:'2024/04', province:'山东省', brand:'理想', model:'理想L6', sales:106},
            {month:'2024/04', province:'广东省', brand:'蔚来', model:'蔚来ET7', sales:62},
            {month:'2024/04', province:'广东省', brand:'理想', model:'理想L6', sales:1432},
            {month:'2024/05', province:'山东省', brand:'蔚来', model:'蔚来ET7', sales:18},
            {month:'2024/05', province:'山东省', brand:'理想', model:'理想L6', sales:700},
            {month:'2024/05', province:'广东省', brand:'蔚来', model:'蔚来ET7', sales:134},
            {month:'2024/05', province:'广东省', brand:'理想', model:'理想L6', sales:1666},
            {month:'2024/06', province:'山东省', brand:'蔚来', model:'蔚来ET7', sales:11},
            {month:'2024/06', province:'山东省', brand:'理想', model:'理想L6', sales:1273},
            {month:'2024/06', province:'广东省', brand:'蔚来', model:'蔚来ET7', sales:124},
            {month:'2024/06', province:'广东省', brand:'理想', model:'理想L6', sales:3598}
        ];
        // 2024/06省份品牌销量&TP
        const juneScatterData = [
            {province:'山东省', brand:'蔚来', sales:356, TP:320290},
            {province:'山东省', brand:'理想', sales:2376, TP:308903},
            {province:'广东省', brand:'蔚来', sales:2111, TP:320290},
            {province:'广东省', brand:'理想', sales:9078, TP:308903}
        ];

        // ===================== 图表1：省份-品牌月度累计销量对比（柱状图） =====================
        (function(){
            const chart = echarts.init(document.getElementById('provinceBrandBar'));
            // 按省份/品牌/月份整理累计
            function sumSales(province, brand) {
                return provinceBrandMonth.filter(d => d.province === province && d.brand===brand)
                    .reduce((s,d) => s + d.sales, 0);
            }
            const data = [
                {province:"山东省", brand:"蔚来", sales:sumSales("山东省","蔚来")},
                {province:"山东省", brand:"理想", sales:sumSales("山东省","理想")},
                {province:"广东省", brand:"蔚来", sales:sumSales("广东省","蔚来")},
                {province:"广东省", brand:"理想", sales:sumSales("广东省","理想")}
            ];
            const option = {
                color: [colors[0], colors[1]],
                tooltip: { trigger:'axis', axisPointer:{type:'shadow'}},
                grid: {left:'5%', right:'5%', bottom:'10%', top:'18%'},
                legend: {
                    data:brandList,
                    top:0,
                    right:'8%',
                    itemWidth:16,
                    itemHeight:16,
                    textStyle: {
                        fontFamily: 'HarmonyOS Sans SC', color: 'rgba(0,0,0,0.7)'
                    }
                },
                xAxis: {
                    type:'category',
                    data:provinceList,
                    axisLabel:{fontFamily:"HarmonyOS Sans SC"}
                },
                yAxis: {
                    type:'value',
                    name:'累计销量',
                    axisLabel: {fontFamily:"HarmonyOS Sans SC"}
                },
                series: brandList.map((brand, idx) => ({
                    name: brand,
                    type:'bar',
                    barWidth:'30%',
                    data: provinceList.map(p => data.find(d => d.province===p && d.brand===brand)?.sales || 0),
                    emphasis: {focus:'series'},
                    label: {
                        show: true,
                        position: 'top',
                        fontFamily: "HarmonyOS Sans SC",
                        color: colors[idx]
                    }
                }))
            };
            chart.setOption(option);
            window.addEventListener('resize', ()=>chart.resize());
        })();

        // ===================== 图表2：省份-品牌销量趋势（折线图） =====================
        (function(){
            const chart = echarts.init(document.getElementById('provinceBrandLine'));
            const series = [];
            provinceList.forEach((province,pidx)=>{
                brandList.forEach((brand,bidx)=>{
                    series.push({
                        name: province+"-"+brand,
                        type: 'line',
                        data: months.map(m=> {
                            const found = provinceBrandMonth.find(d=>d.month===m && d.province===province && d.brand===brand);
                            return found ? found.sales : 0;
                        }),
                        symbol: 'circle',
                        symbolSize:8,
                        lineStyle:{width:3, color: colors[pidx*2+bidx]},
                        itemStyle: {color: colors[pidx*2+bidx]}
                    });
                });
            });
            const option = {
                color: colors,
                tooltip: {trigger:'axis'},
                legend: {
                    data: provinceList.flatMap(p=>brandList.map(b=>p+'-'+b)),
                    top:0,
                    left:'center',
                    itemWidth:14,
                    itemHeight:14,
                    textStyle: {
                        fontFamily: 'HarmonyOS Sans SC', color: 'rgba(0,0,0,0.7)'
                    }
                },
                grid: {left:'6%', right:'6%', bottom:'10%', top:'20%'},
                xAxis: {
                    type:'category',
                    data: months,
                    axisLabel:{fontFamily:"HarmonyOS Sans SC"}
                },
                yAxis: {
                    type:'value',
                    name:'销量',
                    axisLabel: {fontFamily:"HarmonyOS Sans SC"}
                },
                series
            };
            chart.setOption(option);
            window.addEventListener('resize', ()=>chart.resize());
        })();

        // ===================== 图表3：佛山市品牌月度销量趋势 =====================
        (function(){
            const chart = echarts.init(document.getElementById('foshanBrandLine'));
            const monthsF = ["2024/03","2024/04","2024/05","2024/06"];
            const brands = ["蔚来","理想"];
            const series = brands.map((brand,idx)=>({
                name: brand,
                type:'line',
                data: monthsF.map(m=>{
                    // 佛山理想3月无数据
                    const d = foshanBrandMonth.find(e=>e.month===m && e.brand===brand);
                    return d ? d.sales : 0;
                }),
                symbol:'circle',
                symbolSize:8,
                lineStyle:{width:3, color: colors[4+idx]},
                itemStyle:{color: colors[4+idx]}
            }));
            const option = {
                color,
                tooltip:{trigger:'axis'},
                legend: {
                    data: brands,
                    top:0,
                    right:'15%',
                    itemWidth:14,
                    itemHeight:14,
                    textStyle: {fontFamily: 'HarmonyOS Sans SC', color: 'rgba(0,0,0,0.7)'}
                },
                grid: {left:'8%', right:'8%', bottom:'12%', top:'20%'},
                xAxis: {
                    type:'category',
                    data: monthsF,
                    axisLabel:{fontFamily:"HarmonyOS Sans SC"}
                },
                yAxis: {
                    type:'value',
                    name:'销量',
                    axisLabel: {fontFamily:"HarmonyOS Sans SC"}
                },
                series
            };
            chart.setOption(option);
            window.addEventListener('resize', ()=>chart.resize());
        })();

        // ===================== 图表4：佛山蔚来ET7 vs 理想L6月度销量对比（柱状） =====================
        (function(){
            const chart = echarts.init(document.getElementById('foshanModelBar'));
            const monthsF = ["2024/03","2024/04","2024/05","2024/06"];
            // 车型
            const models = ["蔚来ET7","理想L6"];
            const series = models.map((model,idx)=>({
                name: model,
                type:'bar',
                barWidth:'32%',
                data: monthsF.map(m=>{
                    const d = foshanModelMonth.find(e=>e.month===m && e.model===model);
                    return d ? d.sales : 0;
                }),
                emphasis: {focus:'series'},
                itemStyle: {color: colors[5-idx], borderRadius: [8,8,0,0]},
                label: {
                    show: true,
                    position: 'top',
                    fontFamily: "HarmonyOS Sans SC",
                    color: colors[5-idx]
                }
            }));
            const option = {
                tooltip:{trigger:'axis',axisPointer:{type:'shadow'}},
                legend:{
                    data:models,
                    top:0,
                    right:'16%',
                    itemWidth:15,
                    itemHeight:15,
                    textStyle:{fontFamily:'HarmonyOS Sans SC', color:'rgba(0,0,0,0.7)'}
                },
                grid:{left:'8%', right:'8%', bottom:'13%', top:'17%'},
                xAxis:{
                    type:'category',
                    data: monthsF,
                    axisLabel:{fontFamily:'HarmonyOS Sans SC'}
                },
                yAxis:{
                    type:'value',
                    name:'销量',
                    axisLabel:{fontFamily:'HarmonyOS Sans SC'}
                },
                series
            };
            chart.setOption(option);
            window.addEventListener('resize', ()=>chart.resize());
        })();

        // ===================== 图表5：蔚来ET7与理想L6各省月度销量对比（雷达图） =====================
        (function(){
            const chart = echarts.init(document.getElementById('modelProvinceRadar'));
            // 合计每省每车型销量
            function getSum(model, province) {
                return modelProvinceMonth.filter(d=>d.model===model&&d.province===province)
                    .reduce((s,d)=>s+d.sales,0);
            }
            const indicator = [
                {name:"蔚来ET7-山东", max:50},
                {name:"理想L6-山东", max:1600},
                {name:"蔚来ET7-广东", max:200},
                {name:"理想L6-广东", max:4000}
            ];
            const data = [
                getSum("蔚来ET7","山东省"),
                getSum("理想L6","山东省"),
                getSum("蔚来ET7","广东省"),
                getSum("理想L6","广东省")
            ];
            const option = {
                tooltip: { trigger:'item' },
                radar: {
                    indicator,
                    radius: '70%',
                    splitArea: {areaStyle: {color:['#fff'],opacity:1}},
                    splitLine: {lineStyle:{color:'#eee'}},
                    axisLine: {lineStyle:{color:'#ccc'}}
                },
                series: [{
                    name: "车型-省份累计销量",
                    type: "radar",
                    data: [
                        {
                            value: data,
                            name: "2024/03-2024/06"
                        }
                    ],
                    itemStyle:{color:colors[2]},
                    areaStyle:{color:colors[2], opacity:0.18},
                    lineStyle:{color:colors[2], width:3},
                    symbol: 'circle',
                    symbolSize:8,
                    label: {show: true, color:"#222", fontFamily: "HarmonyOS Sans SC"}
                }]
            };
            chart.setOption(option);
            window.addEventListener('resize', ()=>chart.resize());
        })();

        // ===================== 图表6：2024/06省份-品牌销量&TP均价分析（气泡图） =====================
        (function(){
            const chart = echarts.init(document.getElementById('juneScatter'));
            const scatterData = juneScatterData.map((d,idx)=>({
                name: d.province+"-"+d.brand,
                value: [d.sales, d.TP],
                symbolSize: Math.sqrt(d.sales)*3.5+18,
                itemStyle:{
                    color: colors[idx+2]
                }
            }));
            const option = {
                tooltip: {
                    trigger: 'item',
                    formatter: d=>`${d.name}<br>销量: <b>${d.value[0]}</b><br>TP均价: <b>${d.value[1].toLocaleString()}</b>`
                },
                grid:{left:'8%', right:'8%', bottom:'12%', top:'18%'},
                xAxis:{
                    name:'销量',
                    axisLabel:{fontFamily:'HarmonyOS Sans SC'}
                },
                yAxis:{
                    name:'TP（均价）',
                    axisLabel:{formatter:v=>v.toLocaleString(), fontFamily:'HarmonyOS Sans SC'}
                },
                series: [{
                    type:'scatter',
                    data: scatterData,
                    emphasis: {focus: 'series'}
                }]
            };
            chart.setOption(option);
            window.addEventListener('resize', ()=>chart.resize());
        })();
    </script>
</body>
</html>