CREATE TABLE `fa_sale_city_sub_modl_ym` (
  `SALE_TYP` int DEFAULT NULL COMMENT '数据源:上险量-4，中汽协批发量-5，乘联会批发量-7，乘联会零售量-8',
  `BASE_YM` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '年月',
  `PROV_ID` int DEFAULT NULL COMMENT '省份ID',
  `CITY_ID` int DEFAULT NULL COMMENT '城市ID',
  `MANF_ID` int DEFAULT NULL COMMENT '生产商ID',
  `MANF_PPT` int DEFAULT NULL COMMENT '生产商属性ID',
  `ORIG_PPT` int DEFAULT NULL COMMENT '产地属性',
  `BRND_ID` int DEFAULT NULL COMMENT '品牌ID',
  `BRND_BLOD_CD` int DEFAULT NULL COMMENT '品牌血统ID',
  `BRND_SERI_CD` int DEFAULT NULL COMMENT '品牌系别ID',
  `CNTY_CD` int DEFAULT NULL COMMENT '品牌国家ID',
  `MANF_BRND_ID` int DEFAULT NULL COMMENT '生产商品牌ID',
  `MANF_BRND_PPT` int DEFAULT NULL COMMENT '生产商品牌属性ID',
  `MODL_ID` int DEFAULT NULL COMMENT '车型ID',
  `SUB_MODL_ID` int DEFAULT NULL COMMENT '子车型ID',
  `VECH_FUNC_DIV` int DEFAULT NULL COMMENT '汽车功能ID',
  `BODY_TYP` int DEFAULT NULL COMMENT '车身形式ID',
  `SALE_DEST_ID` int DEFAULT NULL COMMENT '销售流向ID',
  `FUEL_TYP` int DEFAULT NULL COMMENT '燃料类型ID',
  `ENGN_TYP` int DEFAULT NULL COMMENT '发动机ID（排量）',
  `TRAN_TYP` int DEFAULT NULL COMMENT '变速箱ID（排档）',
  `COLR_GROP_ID` int DEFAULT NULL COMMENT '颜色',
  `AGE_GROP_DIV` int DEFAULT NULL COMMENT '年龄组',
  `SEX_TYP` int DEFAULT NULL COMMENT '性别',
  `PAY_TYP` int DEFAULT NULL COMMENT '购买方式',
  `ONWR_SHIP_TYP` int DEFAULT NULL COMMENT '产权',
  `USE_PROP_TYP` int DEFAULT NULL COMMENT '使用性质',
  `VECH_TYP` int DEFAULT NULL COMMENT '汽车类别',
  `SALE_QTY` decimal(18,6) DEFAULT NULL COMMENT '销量',
  `MOM_SALE_QTY` decimal(18,6) DEFAULT NULL COMMENT '环比销量',
  `YOY_SALE_QTY` decimal(18,6) DEFAULT NULL COMMENT '同比销量',
  `REGI_DT` datetime DEFAULT NULL COMMENT '注册时间',
  KEY `IDX_fa_sale_sub_modl_ym_01` (`BASE_YM`,`SALE_TYP`,`MANF_BRND_PPT`),
  KEY `IDX_fa_sale_sub_modl_ym_02` (`BASE_YM`,`SALE_TYP`,`CITY_ID`),
  KEY `IDX_fa_sale_sub_modl_ym_03` (`BASE_YM`,`SALE_TYP`,`SUB_MODL_ID`),
  KEY `IDX_fa_sale_sub_modl_ym_04` (`BASE_YM`,`SALE_TYP`,`CITY_ID`,`SUB_MODL_ID`),
  KEY `IDX_fa_sale_sub_modl_ym_05` (`BASE_YM`,`SALE_TYP`,`MANF_BRND_PPT`,`BODY_TYP`),
  KEY `IDX_fa_sale_city_sub_modl_ym_06` (`SALE_TYP`,`BASE_YM`,`VECH_FUNC_DIV`,`BRND_SERI_CD`),
  KEY `idx_fa_sale_city_sub_modl_ym_07` (`SUB_MODL_ID`,`SALE_TYP`,`BASE_YM`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='月度城市子车型销量表';

CREATE TABLE `ma_brnd` (
  `BRND_ID` int NOT NULL COMMENT '品牌ID',
  `BRND_NM` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '品牌名称',
  `BRND_BLOD_CD` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '品牌血统',
  `BRND_SERI_CD` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '品牌系别',
  `CNTY_CD` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '品牌国家',
  `BRND_GROP_CD` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '品牌集团',
  `BRND_EN_NM` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '品牌英文名称',
  `PINY_INIT` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '拼音首字母',
  `PIC_URL` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '图片',
  `NEW_POWR_YN` int DEFAULT NULL COMMENT '是否新势力',
  `REGI_DT` datetime DEFAULT NULL COMMENT '注册时间',
  `REGI_ID` int DEFAULT NULL COMMENT '注册ID',
  `MOD_DT` datetime DEFAULT NULL COMMENT '修改时间',
  `MOD_ID` int DEFAULT NULL COMMENT '修改ID',
  PRIMARY KEY (`BRND_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='品牌';

CREATE TABLE `ma_manf` (
  `MANF_ID` int NOT NULL COMMENT '生产商ID',
  `MANF_NM` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '生产商名称',
  `MANF_GROP_CD` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '生产商集团',
  `MANF_PPT` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '生产商属性',
  `MANF_EN_NM` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '生产商英文名称',
  `PINY_INIT` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '拼音首字母',
  `PIC_URL` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '图片',
  `REGI_DT` datetime DEFAULT NULL COMMENT '注册时间',
  `REGI_ID` int DEFAULT NULL COMMENT '注册ID',
  `MOD_DT` datetime DEFAULT NULL COMMENT '修改时间',
  `MOD_ID` int DEFAULT NULL COMMENT '修改ID',
  PRIMARY KEY (`MANF_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='生产商';

CREATE TABLE `ma_manf_brnd` (
  `MANF_BRND_ID` int NOT NULL COMMENT '生产商品牌ID',
  `MANF_BRND_NM` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '生产商品牌名称',
  `MANF_ID` int NOT NULL COMMENT '生产商ID',
  `BRND_ID` int NOT NULL COMMENT '品牌ID',
  `MANF_BRND_PPT` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '生产商品牌属性',
  `MANF_BRND_EN_NM` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '生产商品牌英文名称',
  `PINY_INIT` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '拼音首字母',
  `REGI_DT` datetime DEFAULT NULL COMMENT '注册时间',
  `REGI_ID` int DEFAULT NULL COMMENT '注册ID',
  `MOD_DT` datetime DEFAULT NULL COMMENT '修改时间',
  `MOD_ID` int DEFAULT NULL COMMENT '修改ID',
  PRIMARY KEY (`MANF_BRND_ID`) USING BTREE,
  KEY `IDX_MA_MANF_BRND_01` (`MANF_ID`) USING BTREE,
  KEY `IDX_MA_MANF_BRND_02` (`BRND_ID`) USING BTREE,
  KEY `IDX_MA_MANF_BRND_03` (`MANF_ID`,`MANF_BRND_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='生产商品牌';

CREATE TABLE `ma_sub_modl` (
  `SUB_MODL_ID` int NOT NULL COMMENT '子车型ID',
  `SUB_MODL_NM` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '子车型名称',
  `MODL_ID` int DEFAULT NULL COMMENT '车型ID',
  `MANF_BRND_ID` int DEFAULT NULL COMMENT '生产商品牌ID',
  `BODY_TYP` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '车身形式',
  `VECH_TYPE_DIV` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '汽车类型',
  `VECH_FUNC_DIV` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '汽车类别',
  `NEW_ENRG_YN` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否新能源',
  `PINY_INIT` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '拼音首字母',
  `BRND_ID` int DEFAULT NULL COMMENT '品牌ID',
  `SUB_MODL_NM_EN` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '英文子车型名称',
  `PIC_URL` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '图片地址',
  `REGI_DT` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
  `REGI_ID` int DEFAULT NULL COMMENT '注册ID',
  `MOD_DT` datetime DEFAULT NULL COMMENT '修改时间',
  `MOD_ID` int DEFAULT NULL COMMENT '修改ID',
  PRIMARY KEY (`SUB_MODL_ID`) USING BTREE,
  KEY `IDX_MA_SUB_MODL_01` (`MODL_ID`) USING BTREE,
  KEY `IDX_MA_SUB_MODL_02` (`SUB_MODL_ID`,`VECH_FUNC_DIV`),
  KEY `IDX_MA_SUB_MODL_03` (`MANF_BRND_ID`),
  KEY `IDX_MA_SUB_MODL_04` (`BRND_ID`),
  KEY `IDX_MA_SUB_MODL_05` (`BRND_ID`,`MANF_BRND_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='子车型';

CREATE TABLE `ma_city` (
  `CITY_ID` int NOT NULL COMMENT '城市ID',
  `CITY_NM` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '城市名称',
  `PROV_ID` int DEFAULT NULL COMMENT '省份ID',
  `CITY_SIZE_TYP` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '城市规模',
  `ZIP_CD` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '邮政编码',
  `PHON_CD` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '电话区号',
  `CITY_PY` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '城市拼音',
  `CAR_NUM_NM` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '车牌代码',
  `AREA_SZ` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '面积',
  `TP_CITY_YN` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否TP城市',
  `REGI_DT` datetime DEFAULT NULL COMMENT '注册时间',
  `REGI_ID` int DEFAULT NULL COMMENT '注册ID',
  `MOD_DT` datetime DEFAULT NULL COMMENT '修改时间',
  `MOD_ID` int DEFAULT NULL COMMENT '修改ID',
  PRIMARY KEY (`CITY_ID`),
  KEY `R_22` (`PROV_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='城市';

CREATE TABLE `ma_prov` (
  `PROV_ID` int NOT NULL COMMENT '省份ID',
  `PROV_NM` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '省份名称',
  `PROV_SHOR_NM` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '简写',
  `PROV_ABBR_NM` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '简称',
  `PROV_EN_NM` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '省份名称英文',
  `PROV_CITY_ID` int DEFAULT NULL COMMENT '省会城市ID',
  `REGI_DT` datetime DEFAULT NULL COMMENT '注册时间',
  `REGI_ID` int DEFAULT NULL COMMENT '注册ID',
  `MOD_DT` datetime DEFAULT NULL COMMENT '修改时间',
  `MOD_ID` int DEFAULT NULL COMMENT '修改ID',
  PRIMARY KEY (`PROV_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='省份';

上险量：fa_sale_city_sub_modl_ym.SALE_TYP = 4
中汽协批发量：fa_sale_city_sub_modl_ym.SALE_TYP = 5
乘联会批发量：fa_sale_city_sub_modl_ym.SALE_TYP = 7
乘联会零售量：fa_sale_city_sub_modl_ym.SALE_TYP = 8
车型列表：凯美瑞、Model Y，调用 ma_sub_modl.SUB_MODL_NM like '%凯美瑞%',ma_sub_modl.SUB_MODL_NM like '%Model Y%'
月份的格式：YYYYMM,如：202401
年月的格式：YYYY，如：2024
