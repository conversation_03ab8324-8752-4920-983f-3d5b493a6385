import os
from openai import OpenAI
import json

class Text2SQL:
    def __init__(self):
        self.client = OpenAI(
            api_key="sk-882e296067b744289acf27e6e20f3ec0",
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
        )

        # 读取SQL表定义
        with open('./create_sql.txt', 'r', encoding='utf-8') as f:
            self.schema = f.read()

    def generate_sql(self, question):
        # 构建 prompt
        prompt = f"""-- language: SQL
### Question: {question}
### Input: {self.schema}
### Response:
Here is the SQL query I have generated to answer the question `{question}`:
```sql

### Note: When extracting keywords from the user's question, you do not need to translate the keywords into English.
"""

        try:
            completion = self.client.chat.completions.create(
                model="qwen-max",  # qwen-coder-plus-latest
                messages=[
                    {"role": "system", "content": "You are an SQL expert, responsible for converting natural language into SQL query statements."},
                    {"role": "user", "content": prompt}
                ]
            )

            # 提取SQL语句
            sql = completion.choices[0].message.content
            return sql.strip()

        except Exception as e:
            print(f"Error generating SQL: {str(e)}")
            return None

def main():
    # 使用示例
    t2s = Text2SQL()

    questions = [
        "24年1月广汽丰田的乘联会销量、同环比是多少",
        "24年1到12月广丰在广东省的上险量、同环比是多少"
    ]

    for q in questions:
        print("\n问题:", q)
        sql = t2s.generate_sql(q)
        print("生成的SQL:", sql)

if __name__ == "__main__":
    main()