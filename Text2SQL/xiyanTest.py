import re
import pandas as pd
import mysql.connector
from mysql.connector import Error
from openai import OpenAI

# MySQL数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'password',
    'database': 'chatdb'
}

# Excel文件路径
EXCEL_PATH = r'C:\Users\<USER>\Desktop\车型基础信息AGENT问题集.xlsx'

client = OpenAI(
    base_url='https://api-inference.modelscope.cn/v1/',
    api_key='68d99c93-abae-4ef5-9f5e-0aebe5937f4e',  # 替换为自己的API_KEY
)

def generate_sql(question):
    nl2sqlite_prompt = f"""你是一名MySQL专家，现在需要阅读并理解下面的【数据库schema】描述，以及可能用到的【参考信息】，并运用MySQL知识生成可执行的mysql语句回答【用户问题】。
    【用户问题】
    {question}

    【数据库schema】
    CREATE TABLE `vehicle_versions` (
      `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
      `version_id` bigint NOT NULL COMMENT '车型版本ID，样例: 515013, 515747, 518023',
      `version_code` varchar(50) NOT NULL COMMENT '车型版本编码，样例: VC201901240000000015582, VC201902270000000016378, VC201907050000000019211',
      `manf_id` int NOT NULL COMMENT '制造商ID，样例: 256, 256, 100',
      `manf_name` varchar(100) NOT NULL COMMENT '制造商名称，样例: 兰博基尼(进口), 兰博基尼(进口), 上汽通用五菱',
      `manf_ename` varchar(100) DEFAULT NULL COMMENT '制造商英文名称，样例: Automobili Lamborghini S.p.A(import), Automobili Lamborghini S.p.A(import), SGMW',
      `brand_id` int NOT NULL COMMENT '品牌ID，样例: 125, 125, 89',
      `brand_name` varchar(50) NOT NULL COMMENT '品牌名称，样例: 兰博基尼, 兰博基尼, 五菱',
      `brand_ename` varchar(100) DEFAULT NULL COMMENT '品牌英文名称，样例: Automobili Lamborghini S.p.A, Automobili Lamborghini S.p.A, Wuling',
      `manf_brand_id` int NOT NULL COMMENT '制造商品牌ID，样例: 195, 195, 123',
      `manf_brand_name` varchar(100) NOT NULL COMMENT '制造商品牌名称，样例: 兰博基尼(进口), 兰博基尼(进口), 上汽通用五菱-五菱',
      `manf_brand_ename` varchar(100) DEFAULT NULL COMMENT '制造商品牌英文名称，样例: Automobili Lamborghini S.p.A(import), Automobili Lamborghini S.p.A(import), SGMW-Wuling',
      `manf_brand_py_first_letter` varchar(10) DEFAULT NULL COMMENT '制造商品牌拼音首字母，样例: L, L, S',
      `model_id` int NOT NULL COMMENT '车型ID，样例: 2723, 2723, 3500',
      `model_name` varchar(100) NOT NULL COMMENT '车型名称，样例: Huracan, Huracan, 五菱宏光S3',
      `model_ename` varchar(100) DEFAULT NULL COMMENT '车型英文名称，样例: Huracan, Huracan, Wuling Hongguang S3',
      `model_py_first_letter` varchar(10) DEFAULT NULL COMMENT '车型拼音首字母，样例: H, H, W',
      `sub_model_id` int NOT NULL COMMENT '子车型ID，样例: 2772, 3051, 4570',
      `sub_model_name` varchar(100) NOT NULL COMMENT '子车型名称，样例: Huracan, Huracan Spyder, 五菱宏光S3',
      `sub_model_ename` varchar(100) DEFAULT NULL COMMENT '子车型英文名称，样例: Huracan, Huracan Spyder, Wuling Hongguang S3',
      `version_name` varchar(200) NOT NULL COMMENT '版本名称，样例: 5.2L 双离合 四驱 EVO, 5.2L 双离合 四驱 EVO, 1.5L 手动 标准型',
      `version_ename` varchar(200) DEFAULT NULL COMMENT '版本英文名称，样例: 5.2L DCT 4WD EVO, 5.2L DCT 4WD EVO, 1.5L MT Standard',
      `version_full_name` varchar(300) NOT NULL COMMENT '版本完整名称，样例: 兰博基尼 Huracan 2019 5.2L 双离合 四驱 EVO, 兰博基尼 Huracan Spyder 2019 5.2L 双离合 四驱 EVO, 五菱 五菱宏光S3 2019 1.5L 手动 标准型',
      `version_full_ename` varchar(300) DEFAULT NULL COMMENT '版本完整英文名称，样例: AUTOMOBILI LAMBORGHINI S.P.A Huracan 2019 5.2L DCT 4WD EVO, AUTOMOBILI LAMBORGHINI S.P.A Huracan Spyder 2019 5.2L DCT 4WD EVO, WULING Wuling Hongguang S3 2019 1.5L MT Standard',
      `launch_date` int DEFAULT NULL COMMENT '上市日期，样例: 20190107, 20190227, 20190702',
      `halt_product_date` varchar(20) DEFAULT NULL COMMENT '停产日期，样例: 在产在销, 在产在销, 在产在销',
      `halt_sale_date` int DEFAULT NULL COMMENT '停售日期，样例: 2019, 2019, 2019',
      `production_and_marketing` int DEFAULT NULL COMMENT '生产销售状态，样例: 2030, 2030, 2019',
      `model_year` varchar(20) DEFAULT NULL COMMENT '车型年款，样例: 2019, 2019, 2019',
      `segment_id` varchar(20) DEFAULT NULL COMMENT '细分市场ID，样例: 2012, 2001, 2002',
      `segment_name` varchar(50) DEFAULT NULL COMMENT '细分市场名称，样例: 跑车, 跑车, B-SUV-L',
      `segment_ename` varchar(50) DEFAULT NULL COMMENT '细分市场英文名称，样例: 1005, 1001, 1002',
      `parent_segment_id` varchar(20) DEFAULT NULL COMMENT '上级细分市场ID，样例: 1005, 1001, 1002'',
      `parent_segment_name` varchar(50) DEFAULT NULL COMMENT '上级细分市场名称，样例: 跑车, 跑车, B-SUV',
      `parent_segment_ename` varchar(50) DEFAULT NULL COMMENT '上级细分市场英文名称，样例: Coupe, CabrioRoadster, SUV',
      `drive_type_name` varchar(20) DEFAULT NULL COMMENT '驱动类型名称，样例: 四驱, 四驱, 前驱',
      `drive_type_ename` varchar(20) DEFAULT NULL COMMENT '驱动类型英文名称，样例: 4WD, 4WD, FWD',
      `engine_type_id` int DEFAULT NULL COMMENT '发动机类型ID，样例: 5, 5, 2',
      `engine_type_name` varchar(20) DEFAULT NULL COMMENT '发动机类型名称，样例: 5.2L, 5.2L, 1.5L',
      `engine_type_ename` varchar(20) DEFAULT NULL COMMENT '发动机类型英文名称，样例: 5.2L, 5.2L, 1.5L',
      `tran_type_id` int DEFAULT NULL COMMENT '变速箱类型ID，样例: 5, 5, 5',
      `tran_type_name` varchar(20) DEFAULT NULL COMMENT '变速箱类型名称，样例: 双离合, 双离合, 手动',
      `tran_type_ename` varchar(20) DEFAULT NULL COMMENT '变速箱类型英文名称，样例: DCT, DCT, MT',
      `fuel_type_id` int DEFAULT NULL COMMENT '燃料类型ID，样例: 5, 5, 5',
      `fuel_type_name` varchar(20) DEFAULT NULL COMMENT '燃料类型名称，样例: 汽油, 汽油, 汽油',
      `fuel_type_ename` varchar(20) DEFAULT NULL COMMENT '燃料类型英文名称，样例: Gasoline, Gasoline, Gasoline',
      `body_type_id` int DEFAULT NULL COMMENT '车身类型ID，样例: 5, 3, 3',
      `body_type_name` varchar(50) DEFAULT NULL COMMENT '车身类型名称，样例: 跑车, 敞篷跑车, SUV',
      `body_type_ename` varchar(50) DEFAULT NULL COMMENT '车身类型英文名称，样例: Coupe, CabrioRoadster, SUV',
      `changes_id` int DEFAULT NULL COMMENT '变化类型ID，样例: 1, 1, 7',
      `changes_name` varchar(50) DEFAULT NULL COMMENT '变化类型名称，样例: 改款, 新型号, 新型号',
      `changes_ename` varchar(50) DEFAULT NULL COMMENT '变化类型英文名称，样例: Face lift, New versions, New versions',
      `brand_nati_id` int DEFAULT NULL COMMENT '品牌国别ID，样例: 3, 3, 1',
      `brand_nati_name` varchar(50) DEFAULT NULL COMMENT '品牌国别名称，样例: 德系, 德系, 自主',
      `brand_nati_ename` varchar(50) DEFAULT NULL COMMENT '品牌国别英文名称，样例: GERMANY, GERMANY, LOCAL',
      `manf_prop_id` int DEFAULT NULL COMMENT '制造商属性ID，样例: 3, 3, 4',
      `manf_prop_name` varchar(50) DEFAULT NULL COMMENT '制造商属性名称，样例: 进口, 进口, 合资',
      `manf_prop_ename` varchar(50) DEFAULT NULL COMMENT '制造商属性英文名称，样例: Imported, Imported, Joint Venture',
      `manf_brand_prop_id` int DEFAULT NULL COMMENT '制造商品牌属性ID，样例: 5, 5, 2',
      `manf_brand_prop_name` varchar(50) DEFAULT NULL COMMENT '制造商品牌属性名称，样例: 进口, 进口, 合资自主',
      `manf_brand_prop_ename` varchar(50) DEFAULT NULL COMMENT '制造商品牌属性英文名称，样例: Imported, Imported, Joint Venture Domestic',
      `vehicle_type_id` int DEFAULT NULL COMMENT '车辆类型ID，样例: 1, 1, 1',
      `vehicle_type_name` varchar(50) DEFAULT NULL COMMENT '车辆类型名称，样例: 乘用车, 乘用车, 乘用车',
      `vehicle_type_ename` varchar(50) DEFAULT NULL COMMENT '车辆类型英文名称，样例: Passenger Vehicle, Passenger Vehicle, Passenger Vehicle',
      `vehicle_func_id` int DEFAULT NULL COMMENT '车辆功能ID，样例: 9, 9, 11',
      `vehicle_func_name` varchar(50) DEFAULT NULL COMMENT '车辆功能名称，样例: 四驱 EVO, 四驱 EVO, 标准型',
      `vehicle_func_ename` varchar(50) DEFAULT NULL COMMENT '车辆功能英文名称，样例: 4WD EVO, 4WD EVO, Standard',
      `version_trim_name` varchar(50) DEFAULT NULL COMMENT '版本配置名称，样例: 意大利, 意大利, 中国',
      `brand_orig_id` int DEFAULT NULL COMMENT '品牌起源ID，样例: 1, 1, 2',
      `brand_orig_name` varchar(50) DEFAULT NULL COMMENT '品牌起源名称，样例: 豪华, 豪华, 非豪华',
      `brand_orig_ename` varchar(50) DEFAULT NULL COMMENT '品牌起源英文名称，样例: Luxury, Luxury, Non-Luxury',
      `last_version_id` bigint DEFAULT NULL COMMENT '上一版本ID，样例: 507671, 0, 0',
      `brand_prop_id` int DEFAULT NULL COMMENT '品牌属性ID，样例: 1, 1, 3',
      `brand_prop_name` varchar(50) DEFAULT NULL COMMENT '品牌属性名称',
      `on_sale_flag` varchar(10) DEFAULT NULL COMMENT '在售标志',
      `on_product_flag` varchar(10) DEFAULT NULL COMMENT '在产标志',
      `market_prop_id` int DEFAULT NULL COMMENT '市场属性ID',
      PRIMARY KEY (`id`),
      KEY `idx_version_id` (`version_id`),
      KEY `idx_version_code` (`version_code`),
      KEY `idx_manf_id` (`manf_id`),
      KEY `idx_brand_id` (`brand_id`),
      KEY `idx_model_id` (`model_id`),
      KEY `idx_sub_model_id` (`sub_model_id`),
      KEY `idx_launch_date` (`launch_date`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='车型版本信息表';

    【参考资料】
    请严格按以下规则转换用户提问中的词语：
    1. 根据【映射表】进行值替换，确保系统最终使用**系统查询值**执行查询
    2. 转换规则：显示值→查询值
    3. 复合词需局部转换（见示例3）

    # 映射表
    | 标准显示值 (用户常见说法) | 系统查询值 (实际存储值) | 字段    |
    |-----------------------|---------------------|---------|
    | BYD                   |  比亚迪              | brand_name   |
    | 日系三杰               |   本田、丰田、日产      | brand_name   |
    | BBA                   |  奔驰、奥迪、宝马      | brand_name   |
    | 苏7、su7、小米SU7       | SU7                  | sub_model_name   |
    | Camry                 | 凯美瑞                | sub_model_name   |

    # 转换示例
    1. 输入："BYD有哪些车型"  
       → 输出："比亚迪有哪些车型"  
       → 说明：比亚迪(查询值)→BYD(显示值)  

    2. 输入："su7的基本信息"  
       → 输出："SU7的基本信息"  
       → 说明：su7(显示值)→SU7(查询值)  

    3. 输入："日系三杰有哪些车型"  
       → 输出："本田、丰田、日产有哪些车型"  
       → 说明：复合词中"日系三杰"→"本田、丰田、日产"
       
    注意：
    1、如果查询非型号的数据时，需要添加 DISTINCT 去重
    2、生成的sql语句需要遵循mysql的语法
    3、当需要使用 sub_model_name 和 version_name 时，需要使用 like 关键字进行模糊匹配
    ```sql"""

    # 调用API生成SQL
    response = client.chat.completions.create(
        model="XGenerationLab/XiYanSQL-QwenCoder-32B-2504",  # 修改为正确的模型名称
        messages=[
            {
                'role': 'system',
                'content': 'You are a helpful assistant.'
            },
            {
                'role': 'user',
                'content': nl2sqlite_prompt
            }
        ]
    )
    return response.choices[0].message.content

def execute_sql(sql):
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()
        cursor.execute(sql)
        results = cursor.fetchall()
        return results
    except Error as e:
        print(f"数据库错误: {e}")
        return None
    finally:
        if conn.is_connected():
            cursor.close()
            conn.close()

def main():
    # 读取Excel文件
    df = pd.read_excel(EXCEL_PATH)

    # 添加新列
    if 'SQL语句' not in df.columns:
        df['SQL语句'] = ''
    if '查询结果' not in df.columns:
        df['查询结果'] = ''

    total_questions = len(df)
    print(f"共找到 {total_questions} 个问题需要处理")

    # 处理每个问题
    for index, row in df.iterrows():
        question = row['提问']
        if pd.isna(question):
            continue

        print(f"\n正在处理第 {index + 1} 个问题: {question}")

        # 生成SQL
        print("生成SQL中...")
        sql = generate_sql(question)
        print(f"生成的SQL: {sql}")

        # 执行SQL并获取结果
        print("执行SQL查询中...")
        results = execute_sql(sql)
        print(f"查询结果: {results}")

        # 更新DataFrame
        df.at[index, 'SQL语句'] = sql
        df.at[index, '查询结果'] = str(results) if results is not None else '查询出错'

        print(f"第 {index + 1} 个问题处理完成")

        # 每处理完一个问题就保存一次，避免中途出错导致全部丢失
        df.to_excel(EXCEL_PATH, index=False)

    print("\n所有问题处理完成，结果已保存到Excel文件中。")

    # sql = generate_sql("理想L7 有什么型号、上市时间、主销车型、燃料类型....")
    # print(f"生成的SQL: {sql}")

if __name__ == "__main__":
    main()
