import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from matplotlib import font_manager

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 数据准备
data = {
    'Label': ['C-HUD，2024', 'W-HUD，2024', 'AR HUD，2024', 'C-HUD，2022', 'W-HUD，2022', 'AR HUD，2022'],
    'CPV': [810, 1620, 3000, 950, 1350, 670],
    'Importance': [8.28, 6.13, 4.39, 5.68, 6.23, 4.96],
    'EquipmentRate': [0.10, 13.10, 9.80, 0.30, 10.00, 2.50]
}

df = pd.DataFrame(data)

# 创建图表
plt.figure(figsize=(12, 8))
plt.scatter(
    x=df['CPV'],
    y=df['Importance'],
    s=df['EquipmentRate']*50,  # 放大气泡大小以便更好显示
    alpha=0.6,  # 设置透明度
    edgecolors='w',  # 设置边缘颜色
    linewidth=0.5  # 设置边缘线宽
)

# 添加标签
for i, row in df.iterrows():
    plt.text(row['CPV'], row['Importance'], row['Label'],
             fontsize=9, ha='center', va='center')

# 设置标题和轴标签
plt.title('HUD 产品分析气泡图', fontsize=15)
plt.xlabel('CPV', fontsize=12)
plt.ylabel('重要度', fontsize=12)

# 添加网格
plt.grid(True, linestyle='--', alpha=0.3)

# 显示图表
plt.tight_layout()
plt.show()