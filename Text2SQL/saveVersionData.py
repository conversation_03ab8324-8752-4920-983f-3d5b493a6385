import pandas as pd
import mysql.connector
from typing import List, Dict, Any
import sys
import numpy as np

def connect_to_mysql() -> mysql.connector.connection.MySQLConnection:
    """连接MySQL数据库"""
    try:
        connection = mysql.connector.connect(
            host='**********',
            user='root',
            password='password',  # 请修改为你的实际密码
            database='chatdb'    # 请修改为你的实际数据库名
        )
        print("Successfully connected to MySQL")
        return connection
    except mysql.connector.Error as err:
        print(f"Failed to connect to MySQL: {err}")
        sys.exit(1)

def prepare_insert_query() -> str:
    """准备插入SQL语句"""
    columns = [
        'version_id', 'version_code', 'manf_id', 'manf_name', 'manf_ename',
        'brand_id', 'brand_name', 'brand_ename', 'manf_brand_id', 'manf_brand_name',
        'manf_brand_ename', 'manf_brand_py_first_letter', 'model_id', 'model_name',
        'model_ename', 'model_py_first_letter', 'sub_model_id', 'sub_model_name',
        'sub_model_ename', 'version_name', 'version_ename', 'version_full_name',
        'version_full_ename', 'launch_date', 'halt_product_date', 'halt_sale_date',
        'production_and_marketing', 'model_year', 'segment_id', 'segment_name',
        'segment_ename', 'parent_segment_id', 'parent_segment_name', 'parent_segment_ename',
        'drive_type_name', 'drive_type_ename', 'engine_type_id', 'engine_type_name',
        'engine_type_ename', 'tran_type_id', 'tran_type_name', 'tran_type_ename',
        'fuel_type_id', 'fuel_type_name', 'fuel_type_ename', 'body_type_id',
        'body_type_name', 'body_type_ename', 'changes_id', 'changes_name',
        'changes_ename', 'brand_nati_id', 'brand_nati_name', 'brand_nati_ename',
        'manf_prop_id', 'manf_prop_name', 'manf_prop_ename', 'manf_brand_prop_id',
        'manf_brand_prop_name', 'manf_brand_prop_ename', 'vehicle_type_id',
        'vehicle_type_name', 'vehicle_type_ename', 'vehicle_func_id', 'vehicle_func_name',
        'vehicle_func_ename', 'version_trim_name', 'brand_orig_id', 'brand_orig_name',
        'brand_orig_ename', 'last_version_id', 'brand_prop_id', 'brand_prop_name',
        'on_sale_flag', 'on_product_flag', 'market_prop_id'
    ]
    placeholders = ', '.join(['%s'] * len(columns))
    columns_str = ', '.join(columns)
    return f"INSERT INTO vehicle_versions ({columns_str}) VALUES ({placeholders})"

def read_excel_data(file_path: str) -> pd.DataFrame:
    """读取Excel文件数据"""
    try:
        # 读取Excel文件，并指定数据类型
        df = pd.read_excel(file_path)

        # 转换数据类型
        int_columns = [
            'version_id', 'manf_id', 'brand_id', 'manf_brand_id', 'model_id',
            'sub_model_id', 'launch_date', 'halt_sale_date', 'production_and_marketing',
            'engine_type_id', 'tran_type_id', 'fuel_type_id', 'body_type_id',
            'changes_id', 'brand_nati_id', 'manf_prop_id', 'manf_brand_prop_id',
            'vehicle_type_id', 'vehicle_func_id', 'brand_orig_id', 'last_version_id',
            'brand_prop_id', 'market_prop_id'
        ]

        # 转换整数类型字段
        for col in int_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0).astype(int)

        # 将NaN值替换为None
        df = df.replace({np.nan: None})

        print(f"Successfully read {len(df)} rows from Excel")
        return df
    except Exception as e:
        print(f"Failed to read Excel file: {e}")
        sys.exit(1)

def insert_data(connection: mysql.connector.connection.MySQLConnection,
                df: pd.DataFrame,
                batch_size: int = 100) -> None:
    """将数据插入MySQL数据库"""
    cursor = connection.cursor()
    insert_query = prepare_insert_query()

    try:
        total_rows = 0
        for i in range(0, len(df), batch_size):
            batch = df.iloc[i:i + batch_size]

            # 将DataFrame转换为列表时确保数据类型正确
            values = []
            for _, row in batch.iterrows():
                # 转换row为list，确保None值被正确处理
                row_values = []
                for val in row:
                    if pd.isna(val):
                        row_values.append(None)
                    else:
                        row_values.append(val)
                values.append(tuple(row_values))

            cursor.executemany(insert_query, values)
            connection.commit()
            total_rows += len(values)
            print(f"Inserted rows {i} to {min(i + batch_size, len(df))} (Total: {total_rows})")

    except mysql.connector.Error as err:
        print(f"Failed to insert data: {err}")
        print(f"Last failing batch started at row: {i}")
        connection.rollback()
    finally:
        cursor.close()

def main():
    # Excel文件路径
    excel_file = r'C:\Users\<USER>\Desktop\型号清单-06174W.xlsx'

    # 连接数据库
    connection = connect_to_mysql()

    try:
        # 读取Excel数据
        df = read_excel_data(excel_file)

        # 插入数据到MySQL
        insert_data(connection, df)

        print("Data import completed successfully!")

    except Exception as e:
        print(f"An error occurred: {e}")
    finally:
        connection.close()

if __name__ == '__main__':
    main()
