import pandas as pd

# 读取Excel文件
file_path = r'D:\06-AI 测试文件\多维表字段映射关系.xlsx'
df = pd.read_excel(file_path, engine='openpyxl')

# 处理数据的函数
def process_row(row):
    """将单行数据转换为表头:值的拼接格式"""
    return '\n'.join([f"{col}:{val}" for col, val in zip(df.columns, row)])

# 应用处理函数
df['合并单元格'] = df.apply(process_row, axis=1)

# 保存结果到新文件
output_path = r'C:\Users\<USER>\Desktop\处理结果.xlsx'
df.to_excel(output_path, index=False, engine='openpyxl')

print(f"处理完成！结果已保存至：{output_path}")