import pandas as pd

# 读取Excel文件
file_path = r'C:\Users\<USER>\Desktop\政策表格数据-秋怡\202412_Model_Bo2.xlsx'
df = pd.read_excel(file_path)

# 初始化变量
previous_model = None
result_lines = []
buffer = []

# 遍历DataFrame行
for index, row in df.iterrows():
    current_model = row['车型']

    if previous_model is None or previous_model == current_model:
        # 如果是相同车型，则只存储需要拼接的部分
        buffer.append(str(row['指标']))
    else:
        # 处理上一个车型的数据
        if previous_model is not None:
            result_line = ';'.join(
                [str(df.loc[index - 1, col]) for col in ['厂商品牌', '车型', 'MSRP范围', '年月']]) + ';' + ';'.join(
                buffer)
            result_lines.append(result_line)
            result_lines.append('****')  # 添加分隔符

        # 清空buffer并开始处理新的车型
        buffer = [str(row['指标'])]

    previous_model = current_model

# 处理最后一个车型的数据
if previous_model is not None:
    result_line = ';'.join(
        [str(df.loc[df.index[-1], col]) for col in ['厂商品牌', '车型', 'MSRP范围', '年月']]) + ';' + ';'.join(buffer)
    result_lines.append(result_line)
    result_lines.append('****')  # 最后一个车型处理完后也添加分隔符

# 将结果写入新的txt文件
output_file_path = r'C:\Users\<USER>\Desktop\policy_data_processed.txt'
with open(output_file_path, 'w', encoding='utf-8') as f:
    for line in result_lines:
        f.write(line + '\n')

print(f"处理完成，结果已保存至 {output_file_path}")