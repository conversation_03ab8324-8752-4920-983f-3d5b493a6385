import pandas as pd

def process_excel_to_txt(excel_file_path, txt_file_path):
    # 读取Excel文件
    df = pd.read_excel(excel_file_path)

    # 准备一个列表来存储处理后的数据
    processed_data = []

    # 遍历DataFrame的每一行
    for index, row in df.iterrows():
        for col in df.columns:
            # 将标题和值用冒号隔开，并添加到列表中
            processed_data.append(f"{col}: {row[col]}")
        # 每处理完一行数据后添加两个换行符
        processed_data.append("****")
        # processed_data.append("\n")

    # 将处理后的数据写入TXT文件
    with open(txt_file_path, 'w', encoding='utf-8') as file:
        file.write("\n".join(processed_data))


if __name__ == "__main__":
    # 替换为你的Excel文件路径和要输出的TXT文件路径
    excel_file_path = r"C:\Users\<USER>\Desktop\政策文件-提取内容框架-0506.xlsx"
    txt_file_path = r"C:\Users\<USER>\Desktop\政策文件-提取内容框架-0506.txt"

    # 调用函数处理数据并写入TXT文件
    process_excel_to_txt(excel_file_path, txt_file_path)
    print("处理完成！")