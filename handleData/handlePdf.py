import fitz  # PyMuPDF
import pdfplumber
from paddleocr import PaddleOCR


def parse_pdf(pdf_path):
    doc = fitz.open(pdf_path)
    ocr = PaddleOCR(use_angle_cls=True, lang="ch")

    contents = []
    for page_num in range(len(doc)):
        # 提取文本块
        with pdfplumber.open(pdf_path) as pdf:
            page = pdf.pages[page_num]
            text = page.extract_text(layout=True)  # 保留布局信息

        # 清洗噪音（正则表达式示例）
        text = re.sub(r'第\s*[零一二三四五六七八九十]+\s*页', '', text)  # 去页眉
        text = re.sub(r'\n{3,}', '\n\n', text)  # 合并多余空行

        # 提取表格
        tables = page.find_tables()
        table_data = [{'html': table.extract(), 'bbox': table.bbox} for table in tables]

        # 提取图片并OCR
        img_list = doc[page_num].get_images()
        img_texts = []
        for img in img_list:
            pix = fitz.Pixmap(doc, img[0])
            img_path = f"page{page_num}_img{img[0]}.png"
            pix.save(img_path)
            result = ocr.ocr(img_path)
            img_texts.append(" ".join([line[1][0] for line in result]))

        contents.append({
            "page": page_num,
            "text": text,
            "tables": table_data,
            "images": img_texts
        })
    return contents