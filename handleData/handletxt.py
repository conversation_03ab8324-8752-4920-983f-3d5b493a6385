import pandas as pd

# 读取Excel数据
df = pd.read_excel(r'C:\Users\<USER>\Desktop\政策指标字段映射关系.xlsx', sheet_name='型号完整')


# 合并两列数据
# 假设列名为'col1'和'col2'
# merged_data = df['sub_model_id'].astype(str) + ':' + df['sub_model_name'].astype(str) + '\n'  # 强制类型转换避免非字符串错误‌:ml-citation{ref="1,5" data="citationList"}

# 拼接两列数据（假设列名为'col1'和'col2'）
df['combined'] = ('型号ID：' + df['型号ID'].astype(str) + '\n' +
                  '型号名称：'+ df['型号名称'].astype(str) + '\n' +
                  '车型ID：'+ df['车型ID'].astype(str) + '\n' +
                  '车型：'+ df['车型'].astype(str) + '\n' +
                  '品牌ID：'+ df['品牌ID'].astype(str) + '\n' +
                  '品牌：'+ df['品牌'].astype(str) + '\n' +
                  '厂商品牌ID：'+ df['厂商品牌ID'].astype(str) + '\n' +
                  '厂商品牌：'+ df['厂商品牌'].astype(str) + '\n' +
                  '系别ID：'+ df['系别ID'].astype(str) + '\n' +
                  '系别：'+ df['系别'].astype(str) + '\n'
                  )
combined_list = df['combined'].tolist()  # 转换为列表‌:ml-citation{ref="3" data="citationList"}

chunk_size = 1
result = []

# 分块处理
for i in range(0, len(combined_list), chunk_size):
    # result.append('车型ID：车型名称')
    chunk = combined_list[i:i+chunk_size]
    result.extend(chunk)
    result.append('\n')  # 每50行添加一个换行符‌:ml-citation{ref="4" data="citationList"}


# merged_data = df.apply(lambda row: f"{row[col1]}:{row[col2]}", axis=1)
# result = '\n'.join(merged_data.tolist())

# 输出结果
with open('output_version_all_单带标题.txt', 'w', encoding='utf-8') as f:
    f.write('\n'.join(result))  # 每个元素单独换行，且每50行后额外换行‌:ml-citation{ref="4,7" data="citationList"}

