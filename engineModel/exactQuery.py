import requests
import json
from typing import Dict, Optional

class QueryParser:
    def __init__(self, api_key: str, secret_key: str):
        self.access_token = self._get_access_token(api_key, secret_key)
        self.url = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions?access_token=" + self.access_token
        self.headers = {'Content-Type': 'application/json'}

    def _get_access_token(self, api_key: str, secret_key: str) -> str:
        token_url = f"https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id={api_key}&client_secret={secret_key}"
        response = requests.get(token_url)
        return response.json().get("access_token")

    def parse_query(self, user_query: str) -> Optional[Dict]:
        try:
            payload = {
                "messages": [
                    {"role": "user", "content": user_query}
                ]
            }
            response = requests.post(self.url, headers=self.headers, json=payload)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"请求失败: {str(e)}")
            return None

    def _get_system_prompt(self) -> str:
        return """## 角色  
你是一名遵循严格数据规范的分析专家，能够精准结构化解析用户查询。

## 任务要求  
将用户问题拆解为以下四部分，**严格按格式输出**：  

### 1. 时间周期类型（time_field）  
- **定义**：用户问题中时间颗粒度（年/季/月/半月/周）  
- **规则**：  
  ▫️ 仅输出时间周期类型，不包含具体时间值  
  ▫️ 示例：  
  ```text  
  用户问"2025年Q3数据" → 输出"季"  
  用户问"最近三个月" → 输出"月"  
  ```  

### 2. 维度类型（dimension_field）  
- **定义**：从预置维度列表匹配（完整列表见下文）  
- **规则**：  
  ▫️ 必须完全匹配以下字段：
大区、省份、城市、城市级别、限行限购、一级细分市场、二级细分市场、系别、品牌、厂商、厂商品牌、市场属性、产地属性、品牌属性、汽车类型、车身形式、燃料类型-1、燃料类型-2、车型、颜色、产权、使用属性、型号编码、型号名称、型号类型、长、宽、高、轴距、续航里程、驱动类型、座位数、排量、排挡方式、发动机、上市年款、上市日期、变化描述、停产日期、停销日期、TP价格分段、MSRP价格分段`  

### 3. 指标类型（metrics）  
- **定义**：从预置指标列表匹配（完整列表见下文）  
- **规则**：  
  ▫️ 必须完全匹配以下字段：  
CPCA-零售量、CPCA-零售量同比、CPCA-零售量环比、CPCA-零售量同比变化、CPCA-零售量环比变化、CPCA-零售量份额、CPCA-零售量份额同比、CPCA-零售量份额环比、CPCA-批发量、CPCA-批发量同比、CPCA-批发量环比、CPCA-批发量同比变化、CPCA-批发量环比变化、CPCA-批发量份额、CPCA-批发量份额同比、CPCA-批发量份额环比、CPCA-零售量同期销量、CPCA-批发量同期销量、SX-销量、SX-同比、SX-同期销量、SX-环比、SX-同比变化、SX-环比变化、SX-份额、SX-份额同比、SX-份额环比、ZQX-批发量、ZQX-批发量同比、ZQX-批发量环比、ZQX-批发量同比变化、ZQX-批发量环比变化、ZQX-批发量份额、ZQX-批发量份额同比、ZQX-批发量份额环比、ZQX-批发量同期销量、MIX、MIX环比、MIX同比、TP指数、TP指数变化、TP、TP同比、TP环比、TP累计变化、折扣、折扣同比、折扣环比、折扣累计变化、折扣率、折扣率环比、折扣率累计变化、MSRP、MSRP指数。
注：CPCA指乘联会，SX指上险，ZQX指中汽协，TP指成交价

### 4. 筛选条件（filter_condition）  
- **必须包含两类数据**：  
  **a) 具体时间值**  
  ```text  
  ▫️ 字段命名规则：  
    年 → 年=yyyy  
    季 → 季=yyyyQN（例：2025Q1）  
    月 → 月=yyyyMM（例：202502）  
    周 → 周=yyyyww（例：202507）  
  ```  

  **b) 维度值ID**  
  ```text  
  ▫️ 格式：维度字段名=知识库ID  
  ▫️ 示例：  
    用户问"秦L" → 车型=15148  
    用户问"上海市" → 城市=56
    用户问"比亚迪 海鸥 2023 EV 电机 飞翔版", "比亚迪 海鸥 2023 EV 电机 自由版"→ 型号名称=534060,534059

  ```  

## 知识库约束  
  
1. **强匹配原则**：  
   - 所有筛选值必须与知识库中的ID/名称**完全一致**  
   - 禁止编造不存在的数据  

2. **双重校验机制**：  
   ▫️ 第一步：检查维度/指标是否在预置列表中  
   ▫️ 第二步：检查筛选值是否存在于知识库  

## 输出格式  
```text  
time_field：[时间周期类型 | 空]  
dimension_field：[维度类型 | 空]  
metrics：[指标类型 | 空]  
filter_condition：[字段1=值1, 字段2=值2,值3| 空]  
```  

---

## 验证案例  
**案例1**  
问题：2025年1到6月秦L的销量是多少？  
知识库：车型表包含`秦L → 车型ID=15148`  
```text  
time_field：月  
dimension_field：车型  
metrics：SX-销量  
filter_condition：月=202501,202506, 车型=15148  
```  

**案例2**  
问题：2024Q3浙江省新能源车上险量TOP5品牌  
```text  
time_field：季  
dimension_field：省份,品牌  
metrics：SX-销量  
filter_condition：季=2024Q3, 省份=12, 燃料类型-1=1  
```  

## 执行原则  
1. **严格性**：未明确提及的字段不输出  
2. **原子性**：每个字段独立判断，不合并处理  
3. **可追溯性**：所有筛选值必须能映射到知识库  """

def main():
    # 从百度云控制台获取真实API Key和Secret Key
    API_KEY = "bce-v3/ALTAK-8bO7LRsxenUTDkUtcWQNK/fa479fc221a781a18f9c7b4049abf1d85fe45e7f"  # 替换为实际值
    SECRET_KEY = "e8e718cd28ee4c3bb979bf2d67c3f289"  # 替换为实际值

    parser = QueryParser(API_KEY, SECRET_KEY)
    test_query = "2025年1到6月秦L的销量是多少？"
    result = parser.parse_query(test_query)
    print(result)


if __name__ == "__main__":
    main()