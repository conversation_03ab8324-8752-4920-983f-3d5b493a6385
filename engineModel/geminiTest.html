<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>汽车销售数据分析报告</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        /* Reset and Base Styles */
        *, *::before, *::after {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #E9ECEF; /* Slightly off-white background for better contrast with cards */
            color: #4A5568; /* Default text color */
            line-height: 1.6;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .main-title {
            font-family: 'SF Pro Display', sans-serif;
            font-size: 28px;
            color: #1A365D;
            text-align: center;
            margin-bottom: 24px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3A86FF;
        }

        /* Grid System */
        .grid-container {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            gap: 16px; /* Module间距 */
        }

        /* Card Module Styling */
        .card {
            background-color: #F8F9FA; /* 辅色 */
            border-radius: 8px; /* 模块圆角半径 */
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            transition: box-shadow 0.3s ease-in-out, transform 0.3s ease-in-out;
            display: flex;
            flex-direction: column;
        }

        .card:hover {
            transform: translateY(-2px); /* 悬浮效果提升 */
            box-shadow: 0 4px 12px rgba(0,0,0,0.1); /* 悬浮效果阴影 */
        }

        .card-header {
            background-color: #3A86FF; /* 主色 */
            color: #FFFFFF;
            font-family: 'SF Pro Display', sans-serif;
            font-size: 18px;
            font-weight: 600;
            padding: 0 20px;
            height: 60px;
            line-height: 60px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            border-bottom: 1px solid #EDF2F7; /* 分割线 (for header bottom) */
        }

        .card-body {
            padding: 20px;
            flex-grow: 1; /* Content area takes remaining space */
            display: flex;
            flex-direction: column;
        }

        .card-body .echarts-chart {
            width: 100%;
            min-height: 350px; /* Minimum height for charts */
            flex-grow: 1;
        }

        .card-footer {
            padding: 0 20px;
            height: 40px;
            line-height: 40px;
            font-size: 12px;
            color: #718096;
            border-top: 1px solid #EDF2F7; /* 分割线 */
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            text-align: right;
        }

        /* Summary Metric Cards Styling */
        .summary-metrics-container {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-bottom: 20px;
        }

        .summary-metric-card {
            background-color: #FFFFFF;
            border: 1px solid #EDF2F7;
            border-radius: 6px;
            padding: 12px 16px;
            flex-grow: 1;
            min-width: 180px; /* Minimum width for readability */
            box-shadow: 0 1px 2px rgba(0,0,0,0.03);
        }

        .summary-metric-card .metric-title {
            font-family: 'SF Pro Display', sans-serif;
            font-size: 14px;
            color: #2D3748;
            margin-bottom: 4px;
            font-weight: 500;
        }

        .summary-metric-card .metric-value {
            font-family: 'Roboto Mono', monospace;
            font-size: 16px;
            color: #1A365D;
            font-weight: bold;
        }
         .summary-metric-card .metric-value.highlight {
            color: #FF5A5F; /* 强调色 */
         }

        .summary-metric-card .metric-trend {
            font-size: 12px;
            color: #4A5568;
        }
        .summary-metric-card .metric-trend.positive { color: #38A169; }
        .summary-metric-card .metric-trend.negative { color: #E53E3E; }


        /* Responsive Grid Columns */
        .col-12 { grid-column: span 12; }
        .col-6 { grid-column: span 12; } /* Default to full width on small screens */

        @media (min-width: 768px) { /* Medium screens and up */
            .col-md-6 { grid-column: span 6; }
        }

        /* ECharts specific styles can be managed via JS options */
        /* But if some global defaults are needed, add here */
    </style>
</head>
<body>
    <div class="container">
        <h1 class="main-title">汽车销售数据分析报告</h1>

        <div class="grid-container">
            <!-- Card 1: Quarterly Sales -->
            <div class="card col-md-6">
                <div class="card-header">2024年Q4 季度销量对比</div>
                <div class="card-body">
                    <div class="summary-metrics-container">
                        <div class="summary-metric-card">
                            <div class="metric-title">理想L6 销量表现</div>
                            <div class="metric-value">显著领先</div>
                            <div class="metric-trend">在山东和广东均远超蔚来ET7</div>
                        </div>
                        <div class="summary-metric-card">
                            <div class="metric-title">蔚来ET7 销量表现</div>
                            <div class="metric-value">相对较低</div>
                            <div class="metric-trend">广东省销量高于山东省</div>
                        </div>
                         <div class="summary-metric-card">
                            <div class="metric-title">区域销量对比</div>
                            <div class="metric-value">广东省更高</div>
                            <div class="metric-trend">两品牌总销量均高于山东省</div>
                        </div>
                        <div class="summary-metric-card">
                            <div class="metric-title">数据稳定性</div>
                            <div class="metric-value">销量差异大</div>
                            <div class="metric-trend">品牌及区域间销量不均衡</div>
                        </div>
                    </div>
                    <div id="quarterlySalesChart" class="echarts-chart"></div>
                </div>
                <div class="card-footer">数据来源：季度销售报表</div>
            </div>

            <!-- Card 2: Monthly Sales Trend -->
            <div class="card col-md-6">
                <div class="card-header">月度销量趋势 (广东省佛山市)</div>
                <div class="card-body">
                    <div class="summary-metrics-container">
                        <div class="summary-metric-card">
                            <div class="metric-title">理想L6 增长趋势</div>
                            <div class="metric-value highlight">快速增长</div>
                            <div class="metric-trend positive">从4月起销量持续攀升</div>
                        </div>
                        <div class="summary-metric-card">
                            <div class="metric-title">蔚来ET7 增长趋势</div>
                            <div class="metric-value">平稳微增</div>
                            <div class="metric-trend">5月、6月略有提升，基数低</div>
                        </div>
                        <div class="summary-metric-card">
                            <div class="metric-title">市场表现对比</div>
                            <div class="metric-value">理想L6强劲</div>
                            <div class="metric-trend">蔚来ET7销量波动小但量级低</div>
                        </div>
                         <div class="summary-metric-card">
                            <div class="metric-title">数据稳定性</div>
                            <div class="metric-value">理想L6波动上升</div>
                            <div class="metric-trend">蔚来ET7相对稳定</div>
                        </div>
                    </div>
                    <div id="monthlySalesChart" class="echarts-chart"></div>
                </div>
                <div class="card-footer">数据来源：月度销售报表</div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // ECharts common text style
            const textStyle = {
                fontFamily: 'SF Pro Text',
                color: '#4A5568' // 正文色
            };
            const axisLabelTextStyle = {
                fontFamily: 'SF Pro Text',
                color: '#4A5568', // 正文色
                fontSize: 12
            };
            const axisLineStyle = {
                lineStyle: {
                    color: '#A0AEC0' // A bit darker for axis lines
                }
            };
            const splitLineStyle = {
                lineStyle: {
                    color: '#EDF2F7', // 分割线
                    type: 'solid'
                }
            };

            // --- Quarterly Sales Data ---
            const quarterlyRawData = [
                {季: '2024Q4', 省份: '山东省', 品牌: '蔚来', 车型: '蔚来ET7', 'SX-销量': 32, TP: 427295},
                {季: '2024Q4', 省份: '山东省', 品牌: '理想', 车型: '理想L6', 'SX-销量': 4368, TP: 254588},
                {季: '2024Q4', 省份: '广东省', 品牌: '蔚来', 车型: '蔚来ET7', 'SX-销量': 245, TP: 427295},
                {季: '2024Q4', 省份: '广东省', 品牌: '理想', 车型: '理想L6', 'SX-销量': 11683, TP: 254588}
            ];

            const qProvinces = [...new Set(quarterlyRawData.map(item => item.省份))];
            const qModels = [...new Set(quarterlyRawData.map(item => item.车型))];

            const qSeries = qModels.map(model => {
                return {
                    name: model,
                    type: 'bar',
                    barWidth: 24, // 柱体宽度24px
                    barGap: '20%', // 控制同类别中不同系列柱子之间的距离，例如蔚来和理想之间的距离
                                   // '间距4px'比较难直接通过百分比精确实现，20%是一个视觉上较好的间距
                    data: qProvinces.map(province => {
                        const item = quarterlyRawData.find(d => d.省份 === province && d.车型 === model);
                        return item ? item['SX-销量'] : 0;
                    }),
                    itemStyle: {
                        borderRadius: [4, 4, 0, 0] // Slight rounding on top of bars
                    }
                };
            });

            // --- Initialize Quarterly Sales Chart ---
            const quarterlySalesChartDom = document.getElementById('quarterlySalesChart');
            if (quarterlySalesChartDom) {
                const quarterlySalesChart = echarts.init(quarterlySalesChartDom);
                const quarterlyOption = {
                    color: ['#3A86FF', '#FF5A5F'], // 主色, 强调色 for series
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: { type: 'shadow' },
                        textStyle: textStyle
                    },
                    legend: {
                        data: qModels,
                        top: '5%',
                        textStyle: textStyle
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data: qProvinces,
                            axisTick: { alignWithLabel: true },
                            axisLabel: axisLabelTextStyle,
                            axisLine: axisLineStyle,
                            splitLine: { show: false } // Typically no split line for category axis
                        }
                    ],
                    yAxis: [
                        {
                            type: 'value',
                            name: '销量 (台)',
                            nameTextStyle: {...textStyle, fontSize: 14, color: '#2D3748'},
                            axisLabel: {...axisLabelTextStyle, fontFamily: 'Roboto Mono'},
                            axisLine: axisLineStyle,
                            splitLine: splitLineStyle // Y-axis split line
                        }
                    ],
                    series: qSeries
                };
                quarterlySalesChart.setOption(quarterlyOption);
                window.addEventListener('resize', () => quarterlySalesChart.resize());
            }


            // --- Monthly Sales Data ---
            const monthlyRawData = [
                {月: '2024/03', 省份: '广东省', 品牌: '蔚来', 城市: '佛山市', 车型: '蔚来ET7', 'SX-销量': 4, TP: 410542},
                {月: '2024/04', 省份: '广东省', 品牌: '蔚来', 城市: '佛山市', 车型: '蔚来ET7', 'SX-销量': 4, TP: 423157},
                {月: '2024/04', 省份: '广东省', 品牌: '理想', 城市: '佛山市', 车型: '理想L6', 'SX-销量': 36, TP: 256618},
                {月: '2024/05', 省份: '广东省', 品牌: '蔚来', 城市: '佛山市', 车型: '蔚来ET7', 'SX-销量': 18, TP: 424461},
                {月: '2024/05', 省份: '广东省', 品牌: '理想', 城市: '佛山市', 车型: '理想L6', 'SX-销量': 232, TP: 257189},
                {月: '2024/06', 省份: '广东省', 品牌: '蔚来', 城市: '佛山市', 车型: '蔚来ET7', 'SX-销量': 20, TP: 417080},
                {月: '2024/06', 省份: '广东省', 品牌: '理想', 城市: '佛山市', 车型: '理想L6', 'SX-销量': 362, TP: 256433}
            ];

            // Ensure months are sorted correctly
            const mMonths = [...new Set(monthlyRawData.map(item => item.月))].sort((a, b) => {
                const [yearA, monthA] = a.split('/');
                const [yearB, monthB] = b.split('/');
                return new Date(yearA, monthA - 1) - new Date(yearB, monthB - 1);
            });
            const mModels = [...new Set(monthlyRawData.map(item => item.车型))];

            const mSeries = mModels.map(model => {
                return {
                    name: model,
                    type: 'line',
                    smooth: true,
                    lineStyle: { width: 2 }, // stroke-width 2px
                    symbolSize: 8, // Make points slightly larger
                    data: mMonths.map(month => {
                        const item = monthlyRawData.find(d => d.月 === month && d.车型 === model);
                        return item ? item['SX-销量'] : null; // Use null for missing data points
                    })
                };
            });

            // --- Initialize Monthly Sales Chart ---
            const monthlySalesChartDom = document.getElementById('monthlySalesChart');
            if (monthlySalesChartDom) {
                const monthlySalesChart = echarts.init(monthlySalesChartDom);
                const monthlyOption = {
                    color: ['#3A86FF', '#FF5A5F'], // 主色, 强调色 for series
                    tooltip: {
                        trigger: 'axis',
                        textStyle: textStyle
                    },
                    legend: {
                        data: mModels,
                        top: '5%',
                        textStyle: textStyle
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false, // For line charts, often looks better
                        data: mMonths,
                        axisLabel: axisLabelTextStyle,
                        axisLine: axisLineStyle,
                        splitLine: { show: false }
                    },
                    yAxis: {
                        type: 'value',
                        name: '销量 (台)',
                        nameTextStyle: {...textStyle, fontSize: 14, color: '#2D3748'},
                        axisLabel: {...axisLabelTextStyle, fontFamily: 'Roboto Mono'},
                        axisLine: axisLineStyle,
                        splitLine: splitLineStyle // Y-axis split line
                    },
                    series: mSeries
                };
                monthlySalesChart.setOption(monthlyOption);
                window.addEventListener('resize', () => monthlySalesChart.resize());
            }
        });
    </script>
</body>
</html>