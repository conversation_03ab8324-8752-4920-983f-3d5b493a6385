import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.preprocessing import RobustScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import matplotlib.pyplot as plt
from datetime import datetime

# 设置 matplotlib 中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置 pandas 选项以避免弃用警告
pd.set_option('future.no_silent_downcasting', True)

def load_test_data(file_path):
    """加载并预处理测试数据"""
    # 读取数据
    df = pd.read_excel(file_path)

    # 删除无用特征
    columns_to_drop = ['序号', '排序ID', '配置大类', '配置中类', '父配置ID',
                      '配置项简称', '配置项统计外显名称']
    df = df.drop(columns=columns_to_drop, errors='ignore')

    # 定义需要处理的列类型
    numeric_columns = ['重要度', 'CPV', '装车量', '装车量-2022', '装车量同比',
                      'PARAM1', 'PARAM2', '年份']
    percent_columns = ['装备率', '装备率-2022', '装车率', '装车率-2022',
                      '近三年装备率平均增速', '装备率同比', '近三年装车率平均增速', '装车率同比']
    categorical_columns = ['细分市场ID', '细分市场全称', '配置项ID', '配置项全称',
                         '配置项类型', 'VAL值类型', '需求属性', '形象感知', '生命周期']
    forecast_columns = ['装备率预测 - 2024', '装备率预测 - 2025', '装备率预测 - 2026']

    # 定义所有可能的����殊值
    invalid_values = [
        '1-IN类无参数', '无装备型号，系统未展示', '无入库数据',
        '2-T父配置', '非调研数据', '1-T配置'
    ]

    # 统一处理函数
    def process_column(series, is_percent=False):
        series = series.fillna('0' if is_percent else np.nan)
        series = series.replace(invalid_values, '0' if is_percent else np.nan)
        if series.dtype == 'object':
            series = series.replace({r'^\s*$': '0' if is_percent else np.nan}, regex=True)
            if is_percent:
                series = series.astype(str).str.rstrip('%').str.strip()

        series = pd.to_numeric(series, errors='coerce')
        if is_percent:
            series /= 100

        mean_val = series.mean()
        return series.fillna(mean_val if pd.notnull(mean_val) else 0)

    # 处理数值列和百分比列
    for col in numeric_columns + percent_columns:
        if col in df.columns:
            df[col] = process_column(df[col], is_percent=(col in percent_columns))

    # 处理预测列
    for col in forecast_columns:
        if col in df.columns:
            df[col] = process_column(df[col], is_percent=True)

    # 处理分类列
    for col in categorical_columns:
        if col in df.columns:
            df[col] = df[col].fillna('未知').astype(str)

    # 确保所有数值列都是浮点数类型
    numeric_cols = numeric_columns + percent_columns + forecast_columns
    for col in numeric_cols:
        if col in df.columns:
            df[col] = df[col].astype(float)

    print("数据预处理完成。")
    return df, categorical_columns

def prepare_test_features(df, label_encoders=None):
    """准备用于模型的特征"""
    from sklearn.preprocessing import RobustScaler, LabelEncoder

    # 扩展特征集以包含更多潜在的预测因子
    categorical_features = [
        '细分市场ID', '配置项ID', 'VAL值类型', '需求属性',
        '配置项类型', '生命周期', '年份'  # 新增特征
    ]
    numeric_features = [
        '重要度', '装车量', '装车率',
        '装备率', '近三年装备率平均增速', '装备率预测 - 2024'  # 新增特征
    ]

    # 确保目标变量存在
    if 'CPV' not in df.columns:
        raise ValueError("目标变量 'CPV' 不在数据中！")

    # 保留原始列用于结果输出
    result_columns = ['细分市场全称', '配置项全称', '配置项类型']
    df_result = df[result_columns].copy()

    # 筛选特征，并创建副本以避免SettingWithCopyWarning
    all_features = [f for f in categorical_features + numeric_features if f in df.columns]
    selected_features = all_features + ['CPV']
    df_model = df[selected_features].copy()

    # 对分类特征进行编码
    existing_categorical_features = [f for f in categorical_features if f in df_model.columns]
    if label_encoders is None:
        # 如果没有提供编码器，创建新的编码器
        label_encoders = {}
        for feature in existing_categorical_features:
            df_model[feature] = df_model[feature].fillna('未知')
            label_encoders[feature] = LabelEncoder()
            df_model[feature] = label_encoders[feature].fit_transform(df_model[feature].astype(str))
    else:
        # 使用提供的编码器进行转换
        for feature in existing_categorical_features:
            df_model[feature] = df_model[feature].fillna('未知')
            try:
                df_model[feature] = label_encoders[feature].transform(df_model[feature].astype(str))
            except ValueError as e:
                # 处理未知类别
                print(f"警告：特征 {feature} 中存在训练集中未见的类别，将替换为最频繁类别")
                most_frequent_category = label_encoders[feature].transform(['未知'])[0]
                df_model[feature] = df_model[feature].map(lambda x: x if x in label_encoders[feature].classes_ else '未知')
                df_model[feature] = label_encoders[feature].transform(df_model[feature])

    # 处理数值特征
    existing_numeric_features = [f for f in numeric_features if f in df_model.columns]
    for feature in existing_numeric_features:
        df_model[feature] = pd.to_numeric(df_model[feature], errors='coerce')
        df_model[feature] = df_model[feature].fillna(df_model[feature].mean())

    # 对数值特征进行缩放
    if existing_numeric_features:
        scaler = RobustScaler(quantile_range=(10, 90))
        df_model[existing_numeric_features] = scaler.fit_transform(df_model[existing_numeric_features])

    print("特征工程完成。")
    print("\n分类特征的唯一值数量:")
    for feature in existing_categorical_features:
        print(f"{feature}: {df_model[feature].nunique()} 个唯一值")

    # 保存结果列信息和特征列表
    df_model.attrs['result_columns'] = df_result
    df_model.attrs['categorical_features'] = existing_categorical_features
    df_model.attrs['numeric_features'] = existing_numeric_features
    df_model.attrs['label_encoders'] = label_encoders

    return df_model, existing_categorical_features

def evaluate_model(model, X_test, y_test, categorical_features):
    """评估模型性能并生成详细的评估报告"""
    # 获取分类特征的索引
    categorical_indices = [X_test.columns.get_loc(col) for col in categorical_features if col in X_test.columns]

    # 预测
    y_pred = model.predict(X_test)

    # 计算评估指标
    mae = mean_absolute_error(y_test, y_pred)
    mse = mean_squared_error(y_test, y_pred)
    rmse = np.sqrt(mse)
    r2 = r2_score(y_test, y_pred)
    mape = np.mean(np.abs((y_test - y_pred) / y_test)) * 100
    relative_errors = np.abs((y_test - y_pred) / y_test) * 100
    within_10_percent = np.mean(relative_errors <= 10) * 100
    within_20_percent = np.mean(relative_errors <= 20) * 100

    print("\n=== 详细模型评估报告 ===")
    print(f"平均绝对误差 (MAE): {mae:.2f}")
    print(f"均方误差 (MSE): {mse:.2f}")
    print(f"均方根误差 (RMSE): {rmse:.2f}")
    print(f"决定系数 (R²): {r2:.4f}")
    print(f"平均绝对百分比误差 (MAPE): {mape:.2f}%")
    print(f"预测在10%误差范围内的比例: {within_10_percent:.2f}%")
    print(f"预测在20%误差范围内的比例: {within_20_percent:.2f}%")

    # 创建更详细的评估可视化
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    plt.figure(figsize=(20, 12))

    # 1. 预测值vs实际值分析
    plt.subplot(2, 3, 1)
    plt.scatter(y_test, y_pred, alpha=0.5)
    plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
    plt.xlabel('实际CPV值')
    plt.ylabel('预测CPV值')
    plt.title('预测值 vs 实际值对比')

    # 2. 误差分布直方图
    plt.subplot(2, 3, 2)
    plt.hist(y_test - y_pred, bins=50, density=True)
    plt.xlabel('预测误差')
    plt.ylabel('密度')
    plt.title('预测误差分布')

    # 3. 相对误差箱型图
    plt.subplot(2, 3, 3)
    plt.boxplot(relative_errors)
    plt.ylabel('相对误差 (%)')
    plt.title('相对误差分布箱型图')

    # 4. 残差图
    plt.subplot(2, 3, 4)
    plt.scatter(y_pred, y_test - y_pred, alpha=0.5)
    plt.axhline(y=0, color='r', linestyle='--')
    plt.xlabel('预测值')
    plt.ylabel('残差')
    plt.title('残差分布图')

    # 5. Q-Q图
    from scipy import stats
    plt.subplot(2, 3, 5)
    stats.probplot(y_test - y_pred, dist="norm", plot=plt)
    plt.title('残差Q-Q图')

    # 6. 相对误差密度图
    plt.subplot(2, 3, 6)
    plt.hist(relative_errors, bins=50, density=True)
    plt.xlabel('相对误差 (%)')
    plt.ylabel('密度')
    plt.title('相对误差分布')

    plt.tight_layout()
    plt.savefig(f'D:/PycahrmProjects/LangChain/trainModel/test_evaluation_{timestamp}.png')
    plt.close()

    return y_pred

def main():
    # 加载模型和测试数据
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    model = lgb.Booster(model_file='D:/PycahrmProjects/LangChain/trainModel/lgb_model_20250627_113554.txt')

    # 加载测试数据
    test_file_path = r'C:\Users\<USER>\Desktop\配置明细-测试集.xlsx'
    df_test, _ = load_test_data(test_file_path)

    # 准备测试特征
    df_model, categorical_features = prepare_test_features(df_test)

    # 准备测试特征和目标变量
    X_test = df_model.drop('CPV', axis=1)
    y_test = df_model['CPV']

    # 评估模型
    y_pred = evaluate_model(model, X_test, y_test, categorical_features)

    # 获取用于结果展示的列
    df_result = df_model.attrs['result_columns']

    # 创建预测结果DataFrame
    results_df = pd.DataFrame({
        '细分市场ID': df_model['细分市场ID'],
        '细分市场全称': df_result['细分市场全称'],
        '配置项ID': df_model['配置项ID'],
        '配置项全称': df_result['配置项全称'],
        '配置项类型': df_result['配置项类型'],
        'VAL值类型': df_model['VAL值类型'],
        '实际CPV值': y_test,
        '预测CPV值': y_pred,
        '绝对误差': abs(y_test - y_pred),
        '相对误差(%)': abs(y_test - y_pred) / y_test * 100,
        '误差方向': np.where(y_pred > y_test, '高估', '低估')
    })

    # 按绝对误差降序排序
    results_df = results_df.sort_values(by='绝对误差', ascending=False)

    # 分组统计分析
    print("\n=== 按细分市场的误差分析 ===")
    market_stats = results_df.groupby('细分市场全称').agg({
        '绝对误差': ['mean', 'std', 'count'],
        '相对误差(%)': 'mean',
        '误差方向': lambda x: (x == '高估').mean() * 100
    }).round(2)
    market_stats.columns = ['平均绝对误差', '误差标准差', '样本数量', '平均相对误差(%)', '高估比例(%)']
    market_stats = market_stats.sort_values('平均绝对误差', ascending=False)

    print("\n=== 按配置项类型的误差分析 ===")
    type_stats = results_df.groupby('配置项类型').agg({
        '绝对误差': ['mean', 'std', 'count'],
        '相对误差(%)': 'mean',
        '误差方向': lambda x: (x == '高估').mean() * 100
    }).round(2)
    type_stats.columns = ['平均绝对误差', '误差标准差', '样本数量', '平均相对误差(%)', '高估比例(%)']
    type_stats = type_stats.sort_values('平均绝对误差', ascending=False)

    # 保存分析结果
    with pd.ExcelWriter(f'D:/PycahrmProjects/LangChain/trainModel/lgb_prediction_analysis_{timestamp}.xlsx') as writer:
        results_df.to_excel(writer, sheet_name='预测结果', index=False)
        market_stats.to_excel(writer, sheet_name='细分市场分析')
        type_stats.to_excel(writer, sheet_name='配置项类型分析')

    print("\n=== 分析报告已生成 ===")
    print(f"详细结果已保存至: lgb_prediction_analysis_{timestamp}.xlsx")
    print("包含预测结果、细分市场分析和配置项类型分析")

if __name__ == "__main__":
    main()
