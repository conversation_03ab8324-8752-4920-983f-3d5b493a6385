import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import KFold
from sklearn.preprocessing import RobustScaler, LabelEncoder
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import matplotlib.pyplot as plt
from datetime import datetime

# 设置 matplotlib 中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用黑体显示中文
plt.rcParams['axes.unicode_minus'] = False     # 正常显示负号

# 设置 pandas 选项以避免弃用警告
pd.set_option('future.no_silent_downcasting', True)

# 1. 数据预处理
def load_and_preprocess_data(file_path):
    """加载并预处理Excel数据"""
    # 读取数据
    df = pd.read_excel(file_path)

    # 删除无用特征
    columns_to_drop = ['序号', '排序ID', '配置大类', '配置中类', '父配置ID',
                      '配置项简称', '配置项统计外显名称']
    df = df.drop(columns=columns_to_drop, errors='ignore')

    # 定义需要处理的列类型
    numeric_columns = ['重要度', 'CPV', '装车量', '装车量-2022', '装车量同比',
                      'PARAM1', 'PARAM2', '年份']
    percent_columns = ['装备率', '装备率-2022', '装车率', '装车率-2022',
                      '近三年装备率平均增速', '装备率同比', '近三年装车率平均增速', '装车率同比']
    categorical_columns = ['细分市场ID', '细分市场全称', '配置项ID', '配置项全称',
                         '配置项类型', 'VAL值类型', '需求属性', '形象感知', '生命周期']
    forecast_columns = ['装备率预测 - 2024', '装备率预测 - 2025', '装备率预测 - 2026']

    # 定义所有可能的特殊值
    invalid_values = [
        '1-IN类无参数', '无装备型号，系统未展示', '无入库数据',
        '2-T父配置', '非调研数据', '1-T配置'
    ]

    # 统一处理函数
    def process_column(series, is_percent=False):
        series = series.fillna('0' if is_percent else np.nan)
        series = series.replace(invalid_values, '0' if is_percent else np.nan)
        if series.dtype == 'object':
            series = series.replace({r'^\s*$': '0' if is_percent else np.nan}, regex=True)
            if is_percent:
                series = series.astype(str).str.rstrip('%').str.strip()
        
        series = pd.to_numeric(series, errors='coerce')
        if is_percent:
            series /= 100
        
        mean_val = series.mean()
        return series.fillna(mean_val if pd.notnull(mean_val) else 0)

    # 处理数值列和百分比列
    for col in numeric_columns + percent_columns:
        if col in df.columns:
            df[col] = process_column(df[col], is_percent=(col in percent_columns))

    # 处理预测列
    for col in forecast_columns:
        if col in df.columns:
            df[col] = process_column(df[col], is_percent=True)

    # 处理分类列
    for col in categorical_columns:
        if col in df.columns:
            df[col] = df[col].fillna('未知').astype(str)

    # 确保所有数值列都是浮点数类型
    numeric_cols = numeric_columns + percent_columns + forecast_columns
    for col in numeric_cols:
        if col in df.columns:
            df[col] = df[col].astype(float)

    print("数据预处理完成。")
    return df

# 2. 特征工程
def prepare_features(df):
    """准备用于模型的特征"""
    from sklearn.preprocessing import RobustScaler, LabelEncoder

    # 扩展特征集以包含更多潜在的预测因子
    categorical_features = [
        '细分市场ID', '配置项ID', 'VAL值类型', '需求属性',
        '配置项类型', '生命周期', '年份'  # 新增特征
    ]
    numeric_features = [
        '重要度', '装车量', '装车率',
        '装备率', '近三年装备率平均增速', '装备率预测 - 2024' # 新增特征
    ]

    # 确保目标变量存在
    if 'CPV' not in df.columns:
        raise ValueError("目标变量 'CPV' 不在数据中！")

    # 筛选特征，并创建副本以避免SettingWithCopyWarning
    all_features = [f for f in categorical_features + numeric_features if f in df.columns]
    selected_features = all_features + ['CPV']
    df_processed = df[selected_features].copy()

    # 对分类特征进行编码
    label_encoders = {}
    existing_categorical_features = [f for f in categorical_features if f in df_processed.columns]

    for feature in existing_categorical_features:
        df_processed[feature] = df_processed[feature].fillna('未知')
        # 对分类特征进行标签编码
        label_encoders[feature] = LabelEncoder()
        df_processed[feature] = label_encoders[feature].fit_transform(df_processed[feature].astype(str))

    # 处理数值特征
    existing_numeric_features = [f for f in numeric_features if f in df_processed.columns]
    for feature in existing_numeric_features:
        df_processed[feature] = pd.to_numeric(df_processed[feature], errors='coerce')
        df_processed[feature] = df_processed[feature].fillna(df_processed[feature].mean())

    # 对数值特征进行缩放
    if existing_numeric_features:
        scaler = RobustScaler(quantile_range=(10, 90))
        df_processed[existing_numeric_features] = scaler.fit_transform(df_processed[existing_numeric_features])

    print("特征工程完成。")
    print("\n分类特征的唯一值数量:")
    for feature in existing_categorical_features:
        print(f"{feature}: {df_processed[feature].nunique()} 个唯一值")

    # 保存编码器和特征列表，用于后续预测
    df_processed.attrs['label_encoders'] = label_encoders
    df_processed.attrs['categorical_features'] = existing_categorical_features
    df_processed.attrs['numeric_features'] = existing_numeric_features

    return df_processed, existing_categorical_features

# 3. 交叉验证训练模型
def train_model_with_cv(df, categorical_features, n_splits=5):
    """使用K折交叉验证训练 LightGBM 模型，并返回最终模型"""
    X = df.drop('CPV', axis=1)
    y = df['CPV']
    
    # 为 LightGBM 准备分类特征索引
    categorical_indices = [X.columns.get_loc(col) for col in categorical_features if col in X.columns]

    kf = KFold(n_splits=n_splits, shuffle=True, random_state=42)
    oof_preds = np.zeros(len(df))
    feature_importances = pd.DataFrame(index=X.columns)
    best_models = []
    best_iterations = []

    # LightGBM 参数，设置更强的正则化以防止过拟合
    params = {
        'objective': 'mae',         # 使用 MAE 作为目标
        'metric': 'mae',
        'boosting_type': 'gbdt',
        'num_leaves': 25,           # 减少叶子节点数量（原为31��
        'max_depth': 4,             # 进一步限制树深度（原为5）
        'learning_rate': 0.005,     # 更小的学习率（原为0.01）
        'feature_fraction': 0.7,    # 减少特征采样比例（原为0.8）
        'bagging_fraction': 0.7,    # 减少数据采样比例（原为0.8）
        'bagging_freq': 2,          # 更频繁的采样（原为5）
        'min_data_in_leaf': 30,     # 增加叶子节点最小样本数（原为20）
        'min_sum_hessian_in_leaf': 1e-2,  # 增加正则化强度
        'lambda_l1': 0.2,           # 增加L1正则化（原为0.1）
        'lambda_l2': 0.2,           # 增加L2���则化（原为0.1）
        'min_gain_to_split': 0.1,   # 添加分裂增益阈值
        'max_bin': 200,             # 减少特征分箱数量
        'random_state': 42,
        'n_jobs': -1,
        'verbose': -1,
        'first_metric_only': True   # 只用第一个指标来进行早停
    }

    print("\n开始 K 折交叉验证训练...")
    for fold, (train_idx, val_idx) in enumerate(kf.split(X, y)):
        print(f"\n--- 第 {fold+1}/{n_splits} 折 ---")
        X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
        y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
        
        # 创建 LightGBM 数据集
        train_data = lgb.Dataset(X_train, y_train, categorical_feature=categorical_indices)
        val_data = lgb.Dataset(X_val, y_val, categorical_feature=categorical_indices)

        # 训练模型
        model = lgb.train(
            params,
            train_data,
            num_boost_round=5000,   # 增加最大迭代次数
            valid_sets=[train_data, val_data],
            callbacks=[
                lgb.early_stopping(stopping_rounds=300, verbose=True),  # 增加早停轮数
                lgb.log_evaluation(period=200)
            ]
        )
        
        # 保存每折的最佳模型和迭代次数
        best_models.append(model)
        best_iterations.append(model.best_iteration)

        # 验证集预测
        val_preds = model.predict(X_val)
        oof_preds[val_idx] = val_preds

        # 记录特征重要性
        fold_importance = pd.Series(model.feature_importance('gain'), index=X.columns)  # 使用gain而不是split
        feature_importances[f'fold_{fold+1}'] = fold_importance

        # 打印当前折的评估结果
        val_mae = mean_absolute_error(y_val, val_preds)
        val_mse = mean_squared_error(y_val, val_preds)
        val_r2 = r2_score(y_val, val_preds)
        print(f"第 {fold+1} 折评估结果:")
        print(f"MAE: {val_mae:.4f}")
        print(f"RMSE: {np.sqrt(val_mse):.4f}")
        print(f"R2: {val_r2:.4f}")

    # 计算并打印整体交叉验证结果
    oof_mae = mean_absolute_error(y, oof_preds)
    oof_mse = mean_squared_error(y, oof_preds)
    oof_r2 = r2_score(y, oof_preds)

    print("\n=== 交叉验证总体结果 ===")
    print(f"MAE: {oof_mae:.4f}")
    print(f"RMSE: {np.sqrt(oof_mse):.4f}")
    print(f"R2: {oof_r2:.4f}")

    # 可视化并保存平均特征重要性
    feature_importances['mean'] = feature_importances.mean(axis=1)
    feature_importances = feature_importances.sort_values('mean', ascending=False)
    
    plt.figure(figsize=(12, 8))
    plt.barh(feature_importances.index, feature_importances['mean'])
    plt.xlabel('平均特征重要性')
    plt.ylabel('特征')
    plt.title('LightGBM - 交叉验证后的平均特征重要性')
    plt.gca().invert_yaxis()
    plt.tight_layout()

    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    plt.savefig(f'D:/PycahrmProjects/LangChain/trainModel/feature_importance_{timestamp}.png')
    plt.close()

    print("\n最重要的20个特征:")
    print(feature_importances.head(20))

    # 在全量数据上训练最终模型
    print("\n正在��量数据上训练最终模型...")
    final_train_data = lgb.Dataset(X, y, categorical_feature=categorical_indices)

    final_model = lgb.train(
        params,
        final_train_data,
        num_boost_round=model.best_iteration,  # 使用交叉验证得到的最佳迭代次数
        valid_sets=[final_train_data],
        callbacks=[lgb.log_evaluation(period=200)]
    )

    # 保存模型
    model_path = f'D:/PycahrmProjects/LangChain/trainModel/lgb_model_{timestamp}.txt'
    final_model.save_model(model_path)
    print(f"最终模型已保存至: {model_path}")

    # 保存特征重要性分析
    importance_df = pd.DataFrame({
        '特征': feature_importances.index,
        '重要性得分': feature_importances['mean']
    }).sort_values('重要性得分', ascending=False)

    importance_path = f'D:/PycahrmProjects/LangChain/trainModel/feature_importance_{timestamp}.csv'
    importance_df.to_csv(importance_path, index=False, encoding='utf-8-sig')
    print(f"特征重要性分析已保存至: {importance_path}")

    return final_model, oof_mae


def main():
    # 注意：请确保文件路径正确
    file_path = r'C:\\Users\\<USER>\\Desktop\\配置明细-训练集.xlsx'
    
    # 1. 加载和预处理数据
    df_raw = load_and_preprocess_data(file_path)
    
    # 2. 准备特征
    df_featured, categorical_cols = prepare_features(df_raw)
    
    # 3. 使用交叉验证训练模型
    train_model_with_cv(df_featured, categorical_cols, n_splits=5)
    
    print("\n模型训练流程完成！")

if __name__ == "__main__":
    main()