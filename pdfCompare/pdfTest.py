import os
import httpx
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MDComparator:
    def __init__(self, md_path1, md_path2):
        self.API_ENDPOINT = "https://yunwu.ai/v1/chat/completions"
        self.API_KEY = "sk-Mjv9S6FE5oIC3k5F9fxXfXHXWOeaaQcH9qx14IRRGeGEFzzr"
        self.md_path1 = md_path1
        self.md_path2 = md_path2

    def _build_payload(self, md_content1, md_content2):
        """构建API请求负载"""
        # compare_prompt = """
        # 请详细分析这两份Markdown文件的内容，找出它们相同的部分。请按以下格式输出：
        # 1. 总体相似度评估
        # 2. 具体相同内容列表，涉及的章节位置和页码
        # 3. 重要相同点总结
        # 请尽可能详细地列出相同的内容。
        # """
        compare_prompt = """
        我会提供两份项目招标文件给你，目的是找出两份Markdown文件之间的相似内容，避免被审核方发现两份文件过度相似，有陪标的风险。请帮我针对性的找出两份文件相似的文字段落/引用图片/排版格式，并告诉我怎么修改成让这两份文件看起来不相似,并用Markdown格式输出结果。
        
        """


        return {
            "model": "gemini-2.5-pro-preview-05-06",
            "messages": [
                {
                    "role": "system",
                    "content": "你是一个专业的文档比对分析专家"
                },
                {
                    "role": "user",
                    "content": f"{compare_prompt}\n\n文件1内容：\n{md_content1}\n\n文件2内容：\n{md_content2}"
                }
            ],
            "settings": {
                "output_format": "text",
                "max_tokens": 1000000
            }
        }

    def compare_mds(self):
        """比对两个Markdown文件"""
        try:
            # 读取Markdown文件
            with open(self.md_path1, 'r', encoding='utf-8') as f1, \
                 open(self.md_path2, 'r', encoding='utf-8') as f2:
                md_content1 = f1.read()
                md_content2 = f2.read()

            # 发送API请求
            with httpx.Client(timeout=120) as client:
                response = client.post(
                    self.API_ENDPOINT,
                    headers={
                        "Authorization": f"Bearer {self.API_KEY}",
                        "Content-Type": "application/json"
                    },
                    json=self._build_payload(md_content1, md_content2)
                )
                response.raise_for_status()
                result = response.json()

            # 保存比对结果
            output_dir = os.path.dirname(os.path.abspath(__file__))
            output_path = os.path.join(output_dir, "comparison_result2.txt")

            with open(output_path, "w", encoding="utf-8") as f:
                f.write(result['choices'][0]['message']['content'])

            logger.info(f"比对结果已保存至: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"Markdown比对过程中发生错误: {str(e)}")
            raise

def main():
    # Markdown文件路径
    md_path1 = r"C:\Users\<USER>\Desktop\项目方案比对文件\A项目方案书.md"
    md_path2 = r"C:\Users\<USER>\Desktop\项目方案比对文件\B项目方案书.md"

    try:
        comparator = MDComparator(md_path1, md_path2)
        result_path = comparator.compare_mds()
        print(f"比对完成！结果已保存到：{result_path}")
    except Exception as e:
        print(f"程序执行出错: {str(e)}")

if __name__ == "__main__":
    main()