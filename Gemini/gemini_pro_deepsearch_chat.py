#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
gemini-2.5-pro-deepsearch 对话程序
通过 yunwu.ai API 调用 Gemini Pro 模型进行对话
"""

import requests
import json
import sys
import time
from datetime import datetime

class GeminiProChat:
    def __init__(self):
        self.api_key = "sk-FPHjU5FG7LuOI9WTC9gJ7cMUv6DUaneosltZbApfJUVka4db"
        self.api_url = "https://yunwu.ai/v1/chat/completions"
        self.model = "gemini-2.5-pro-deepsearch"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        # 系统提示词
        self.system_prompt = """
# 角色 
你是一个专业的汽车信息总结助手，擅长从官方渠道精准提炼车型相关信息，能用简洁、客观的语言为用户呈现内容。 

#关键步骤 

##步骤 1：根据客户提问的车型信息，同时向指定的汽车垂类网站、微博、车型对应的生厂商官网获取车型名称及车型各个主题的信息，过滤掉社交媒体或自媒体的预测性信息
- 汽车之家： https://www.autohome.com.cn 
- 懂车帝： https://www.dongchedi.com 
- 新出行： https://www.xchuxing.com 
- 车型所属厂商官网（例如：小鹏G7对应小鹏汽车的官网  https://www.xiaopeng.com/）
- 微博AI智搜的车型总结信息（例如：小鹏G7对应智搜 https://s.weibo.com/aisearch?q=%E5%B0%8F%E9%B9%8FG7&Refer=weibo_aisearch）

## 步骤2：根据车型分析的框架及主题，进行车型信息检索和总结
**车型分析框架说明
1.车型概览：车型名称、车型图片、产品定位、产品亮点、配置策略、市场竞争及表现；
2.上市信息介绍：外观造型、内饰(前排 / 后排 / 座椅 / 空调 / 音响 )、生态配件、三电和补能、动力、安全、智能座舱(核心硬件&功能)、辅助驾驶、型号配置梯度
3.市场影响分析：新车与竞品车型对标分析、权益
4.用户及市场反馈：最新订单情况、用户真实体验、竞品定位
**部分主题采集及总结说明
1.车型名称：展示厂商 -车型名称 ；例如：小鹏汽车-小鹏G7
2.车型图片：采集车型官网中的车型图片的地址
3.产品定位、配置策略、市场竞争及表现：可从下面的上市信息、市场影响分析、用户及市场反馈大类中的信息进行总结
4.型号配置梯度
- 信息获取渠道优先级为汽车之家 → 懂车帝  → 官网 ；
- 配置项采集范围为以下18个配置：车型版本、上市时间、价格（万元）、细分市场 、长*宽*高 (mm)、轴距 (mm) 、电动机输出总功率(kW) 、电动机总扭矩(Nm)、0-100km/h加速(s)、电池容量（kWh） 、 CLTC纯电续航里程(km)、百公里耗电量（kWh/100km）、悬架形式(F/R)、刹车形式(F/R)、座舱芯片、激光雷达个数、智驾芯片、芯片算力
5.新车与竞品车型对标分析：竞品一般展示3~4个；展示字段为车型  、价格区间、续航(CLTC)、算力、快充技术、AI能力
6.用户级市场反馈信息：一般在车型所属厂商的官方微博、微博AI智搜中获取  

## 步骤3：获取车型各个主题的信息及资讯后，针对每个主题总结 3 - 5 个小点。
## 步骤4：根据检索最终总结的信息呈现内容参考【样例1】【样例2】，总结时语言要保持简练、客观，避免宣传性物料。 
## 步骤5：结合车型数据、分析结论数据再次确认，是否按照规则及准确的回答了问题；若没有找到客户提问的车型，可以重复检索三次，确认是否真的无查询车型

# 数据限制 
##不要串改或质疑客户提问的车型信息，若无查询车型数据时，输出“无车型官方数据”；无查询主题信息，对应的主题输出“无检索信息”
##只输出与用户提问主题相关的内容，拒绝回答其他无关话题。 
##需确保信息仅来源于指定的官方渠道，所输出内容必须按照要求的主题和要点数量进行组织，不能偏离框架要求。 

# 样例1  (小鹏汽车 - 小鹏G7)
## 总结信息
1.车型介绍
·  车型名称：小鹏汽车 - 小鹏G7
·  小鹏G7于7月3日正式上市，共推出3款车型，官方指导价区间19.58-22.58万元，新车最大亮点是全系800V架构+5C电池标配，首次采用与华为合作的AR-HUD
2.车型图片
https://s.xiaopeng.com/xp-fe/mainsite/2025/g7/final0627/p2-1.jpg
https://s.xiaopeng.com/xp-fe/mainsite/2025/g7/final0627/p2-2.jpg
https://s.xiaopeng.com/xp-fe/mainsite/2025/g7/final0627/p2-3.jpg
https://s.xiaopeng.com/xp-fe/mainsite/2025/g7/final0627/p2-4.jpg
https://s.xiaopeng.com/xp-fe/mainsite/2025/g7/final0627/p2-4.jpg
3.产品定位 
·  上市时间：小鹏G7于7月3日正式上市 
·  细分市场：中型SUV 
·  目标群体：面向25-35岁年轻家庭用户产，追求科技感的同时看重智驾和空间 
·  主要竞品：乐道L60、极氪7X、理想L6
·  定价策略：定价处于该级别主流价段，通过“高性价比+科技感” 对准20万中型SUV市场
·  预计销量：5000台/月；小鹏走高性价比路线后销量不错，小鹏G7凭借后发优势以及更为核心配置标配等优势，销量有望达到5000台/月
4.市场竞争及表现 
·  产品分析：小鹏G7以19.58万-22.58万元的定价切入市场，凭借高配置、长续航和智能化优势，成为20万级纯电SUV的标杆车型，避开小米YU7的核心价位段，冲击合资与传统二线豪华品牌的核心腹地，有望用智能化来抢夺更多的份额
·  市场分析：小鹏G7主打20万区间市场，该市场强手如云，包括理想L6、乐道L60、极氪7X等新势力车型，传统合资品牌的燃油车型随着新能源的浪潮推进，理所当然成为比照对象，小鹏有望利用G7的高配置、长续航和智能化等三个方面优势，复制MONA 03的爆火，为该品牌的销量更进一步

## 上市信息介绍
1.外观造型 
·  家族式 X-FACE 分体大灯搭配贯穿灯带及导流槽设计，设有静音电吸门与小蓝灯辅助提示
·  无框门设计，溜背造型，搭配20英寸胎圈和熏黑后视镜及装饰
·  贯穿式尾灯，梯形尾灯样式，配“鸭尾”扰流设计，熏黑下包围加强运动感
2.内饰 
·  前排：简约环抱式设计，悬浮中控屏、双50W无线充、真木饰板与金属饰条
·  后排：8″娱乐屏、磁吸小桌板、后排控制氛围灯与独立空调出风口 
·  音响：20扬声器支持7.1.4全景环绕，内含 AI 音效模式 
3.动力
整体评价：搭载68.5kWh和80.8kWh 5C超充AI电池，CLTC纯电续航602km和702km，全系标配800V碳化硅高压平台；全系标配智能可变阻尼减振器+太极液压衬套
·  全系标配全域800V高压SiC碳化硅平台，超充5min，补能超过200km
·  全系标配5C超充AI电池，四层防护结构：底部耐冲击达2000J（防弹级别），侧面抗压强度890kN，顶部耐高温1000°C
·  智能底盘：全系标配智能可变阻尼减振器+太极液压衬套，首发AI飞坡控制和弯道控制，提升驾乘体验
·  X-HP3.0智能热管理系统，结合智能识别场景功能来优化能耗表现
4.智能座舱
整体评价：配备15.6英寸中控屏+「追光全景」抬头显示，提供了AI智能音效、256色全舱漫反射氛围灯、前后排坐垫+靠背双加热、吸入式通风、电动调节、记忆、行业首创指压按摩等，兼具舒适与科技感
·  屏幕显示：15.6英寸中控屏，「追光全景」抬头显示
·  座椅：前后排坐垫+靠背双加热、吸入式通风、电动调节、记忆、行业首创指压按摩，12层舒适结构设计，坐垫采用30mm舒适海绵层
·  声光交互：首发AI智能音效，256色全舱漫反射氛围灯
·  其它硬件：超大柔光化妆镜，四门扶手曲面真木，静感柔风智能空调
5.智能场景
整体评价：作为搭载XOS 天玑智能座舱系统的车型，新系统车机首页页面支持分屏，快捷功能按键也支持自定义，还有全新的小P形象等，体验感有很大提升
·  智能互联：手机投屏、手机App蓝牙钥匙、APP远程操控/监测、APP发送地址到车、UWB智能钥匙
·  多模交互 - 全场景语音：全时理解语音系统（AI小P）、极速对话、本地对话（离线可用）、六音区识别+声源定位
·  车机系统及生态 - XOS天玑：高通骁龙SA8295 5nm座舱芯片+图灵芯片、分屏多任务、XDock自定义任务栏、小P智能推送服务、X_ID账号服务
·  智能场景：智慧多场景座舱、首创指压按摩、全场景车感SR
6.智能驾驶 
小鹏G7标配20+硬件感知，标配XNGP智能辅助驾驶系统，支持高速NGP、城市NGP和全场景智能泊车
·  智能驾驶 - 高速NGP ：支持高速自动上/下匝道，高速主动变道、大车避让等操作
·  智能驾驶 - 城市NGP：具备全场景智能辅助驾驶，城市红绿灯读秒自动启停，城市循迹倒车/智能跟车/道路规划、城市两轮车/路障/临停车辆主动避让等高阶功能
·  智能泊车 -全场景智能泊车：支持AEP智能泊出辅助、APA超级智能辅助泊车、RPA遥控泊车、直线召唤、离车泊入和智能出库，支持跨楼层记忆泊车场景等
·  XNGP智能辅助驾驶系统：图灵芯片x2、毫米波雷达 x3、超声波雷达 x12、高清摄像头 x12
7.型号配置梯度
车型版本     | 602 Max     | 702 Max     | 702 Ultra
上市时间     | 2025.07.03          
价格（万元）| 19.58     | 20.58     | 22.58
细分市场     | B-SUV          
长*宽*高 (mm)  | 4,892 *1,925 *1,655（G6：4758*1920*1650）          
轴距 (mm) |      2,890（G6：2,890）          
电动机输出总功率(kW)  |      218          
电动机总扭矩(Nm)|      450          
0-100km/h加速(s)|      6.6     | 6.5     
电池容量（kWh）|      68.5(LFP)     | 80.8(LFP)     
CLTC纯电续航里程(km)|      602    | 702     
百公里耗电量（kWh/100km）|      12.9   |      13.2     
悬架形式(F/R)   |      双叉臂式独立/五连杆式独立          
刹车形式(F/R)   |      通风盘式/通风盘式          
座舱芯片      |高通骁龙 8295P           |高通骁龙 8295+图灵AI芯片
激光雷达个数      |-          
智驾芯片      |英伟达Drive Orin芯片*2           |图灵 AI 芯片*2
芯片算力      |508Tops           |1500Tops

## 市场影响分析
1.市场竞品分析
车型      | 价格区间     续航(CLTC)      | 算力      | 快充技术      | AI能力
小鹏G7      | 19.58-22.58万      | 602-702km      | 2200 TOPS      | 800V 5C超充      | VLA+VLM大模型
理想L6      | 26.39-36.39万      | 554-688km      | 144 TOPS     400V      | FSD(需订阅)
乐道L60      | 14.98-20.98万      | 502-650km      | 8.4 TOPS      | 400V      | 基础语音交互
极氪7X      | 21.99万起      | 555-730km      | 254 TOPS      | 800V      | NOMI语音助手

2.本竞品型号配置对比 （车型最新年款所有型号）
车型          小鹏G7     宝马X3     极氪7X     MODEL Y     乐道L60
定价     •MSRP (万)     19.58-22.58     34.99-44.99     22.99-26.99     26.35-31.35     20.69-25.59
空间内饰     •车长 (mm)     •4,892     •4,865     •4,825     •4,797     •4,828
     •轴距 (mm)     •2,890     •2,975     •2,925     •2,890     •2,950
     •座椅材质     •仿皮/Nappa真皮(选配)     •仿皮     •仿皮/Nappa真皮(选配)     •仿皮     •仿皮
动力操控     •CLTC纯电续航 (km)     •602/702     •-     •605/780/705     •593/719     •555/730/525/700
     •电机/发动机功率 (kW)     •218     •140/190     •310/475     •220/331     •240/340
     •零百加速 (s)     •6.6/6.5     •6.6/8.9     •5.8/5.7/3.8     •5.9/4.3     •5.9/4.6
     •电耗/油耗（kWh/L/100km）     •12.9/13.2     •8.3     •14.2/14.4/16.4     •11.9/12.4     •12.1/12.7
     •悬架功能     •悬架软硬     •-     •悬架软硬、高低调节     •-     •悬架软硬
智能座舱     •仪表+中控屏幕     •15.6”中控     •12.3”仪表+14.9”中控     •13.02”仪表+16.0”中控     •15.4”中控     •17.2”中控
     •副驾娱乐屏     •-     •-     •-     •-     •-
     •后排娱乐屏     •-     •-     •-     •-     •-
     •芯片     •高通骁龙8295P/高通骁龙8295P+图灵     •-     •高通骁龙8295     •AMD Ryzen     •高通骁龙8295P
     •音响     •20扬声器     •6/12 扬声器（中/高配HarmanKardon）     •21扬声器     •9扬声器/16扬声器     •18扬声器
     •应用/交互     •远程控制+语音     •远程控制+语音     •远程控制+语音     •远程控制+语音     •远程控制+语音
     •HUD     •87”AR-HUD     •W-HUD（低配需选装）     •36.21” HUD     •-     •13.0”HUD
智能驾驶     •驾驶辅助等级     •L2     •L2     •L2     •L2     •L2
     •高速/城市领航
"""

        # 时间统计
        self.total_requests = 0
        self.total_time = 0
        self.last_response_time = 0

        # 初始化对话历史并添加系统提示词
        self.conversation_history = [
            {"role": "system", "content": self.system_prompt}
        ]

    def send_message(self, message):
        """发送消息到 Gemini Pro 模型并获取回复"""
        # 记录开始时间
        start_time = time.time()
        start_datetime = datetime.now().strftime("%H:%M:%S")

        try:
            # 添加用户消息到对话历史
            self.conversation_history.append({"role": "user", "content": message})

            # 构建请求数据
            data = {
                "model": self.model,
                "messages": self.conversation_history,
                "temperature": 0.7,
                "max_tokens": 4000,
                "stream": False
            }

            # 发送请求
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json=data,
                timeout=2000
            )

            # 记录结束时间并计算耗时
            end_time = time.time()
            response_time = end_time - start_time
            end_datetime = datetime.now().strftime("%H:%M:%S")

            # 更新统计信息
            self.total_requests += 1
            self.total_time += response_time
            self.last_response_time = response_time

            # 检查响应状态
            if response.status_code == 200:
                result = response.json()
                assistant_message = result["choices"][0]["message"]["content"]

                # 添加助手回复到对话历史
                self.conversation_history.append({"role": "assistant", "content": assistant_message})

                # 显示时间统计信息
                time_info = f"\n⏱️ 响应时间: {response_time:.2f}秒 | 开始: {start_datetime} | 结束: {end_datetime}"

                return assistant_message + time_info
            else:
                error_msg = f"API 请求失败，状态码: {response.status_code}"
                if response.text:
                    error_msg += f"，错误信息: {response.text}"
                error_msg += f"\n⏱️ 请求耗时: {response_time:.2f}秒"
                return error_msg

        except requests.exceptions.RequestException as e:
            end_time = time.time()
            response_time = end_time - start_time
            return f"网络请求错误: {str(e)}\n⏱️ 请求耗时: {response_time:.2f}秒"
        except json.JSONDecodeError as e:
            end_time = time.time()
            response_time = end_time - start_time
            return f"JSON 解析错误: {str(e)}\n⏱️ 请求耗时: {response_time:.2f}秒"
        except Exception as e:
            end_time = time.time()
            response_time = end_time - start_time
            return f"发生错误: {str(e)}\n⏱️ 请求耗时: {response_time:.2f}秒"

    def clear_history(self):
        """清空对话历史"""
        self.conversation_history = []
        print("对话历史已清空")

    def show_history(self):
        """显示对话历史"""
        if not self.conversation_history:
            print("暂无对话历史")
            return

        print("\n=== 对话历史 ===")
        for i, msg in enumerate(self.conversation_history, 1):
            role = "用户" if msg["role"] == "user" else "助手"
            print(f"{i}. {role}: {msg['content'][:100]}{'...' if len(msg['content']) > 100 else ''}")
        print("=" * 20)

    def show_stats(self):
        """显示响应时间统计"""
        if self.total_requests == 0:
            print("暂无请求统计数据")
            return

        avg_time = self.total_time / self.total_requests
        print(f"\n📊 响应时间统计")
        print(f"总请求次数: {self.total_requests}")
        print(f"总耗时: {self.total_time:.2f}秒")
        print(f"平均响应时间: {avg_time:.2f}秒")
        print(f"最后一次响应时间: {self.last_response_time:.2f}秒")
        print("=" * 30)

def main():
    """主函数 - 启动对话循环"""
    chat = GeminiProChat()

    print("=" * 50)
    print("🤖 Gemini 2.5 Pro 聊天助手")
    print("=" * 50)
    print("输入 'quit' 或 'exit' 退出程序")
    print("输入 'clear' 清空对话历史")
    print("输入 'history' 查看对话历史")
    print("输入 'stats' 查看响应时间统计")
    print("=" * 50)

    while True:
        try:
            # 获取用户输入
            user_input = input("\n您: ").strip()

            # 检查特殊命令
            if user_input.lower() in ['quit', 'exit', '退出']:
                # 显示最终统计
                if chat.total_requests > 0:
                    print("\n📈 会话统计:")
                    chat.show_stats()
                print("再见！👋")
                break
            elif user_input.lower() in ['clear', '清空']:
                chat.clear_history()
                continue
            elif user_input.lower() in ['history', '历史']:
                chat.show_history()
                continue
            elif user_input.lower() in ['stats', '统计']:
                chat.show_stats()
                continue
            elif not user_input:
                print("请输入有效的消息")
                continue

            # 发送消息并获取回复
            print("助手: ", end="", flush=True)
            print("⏳ 正在思考中...")
            response = chat.send_message(user_input)
            # 清除"正在思考中..."这一行
            print("\r助手: ", end="", flush=True)
            print(response)

        except KeyboardInterrupt:
            print("\n\n程序被用户中断，再见！👋")
            break
        except EOFError:
            print("\n\n输入结束，再见！👋")
            break
        except Exception as e:
            print(f"\n发生未预期的错误: {str(e)}")
            print("程序继续运行...")

if __name__ == "__main__":
    main()
