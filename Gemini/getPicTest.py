#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新出行文章正文图片提取器
只提取文章正文区域的图片，过滤掉页面其他部分的图片
"""

import requests
from bs4 import BeautifulSoup
import os
from urllib.parse import urljoin
import time

def main():
    url = "https://www.xchuxing.com/article/144526"
    
    print("🚀 新出行文章正文图片提取器")
    print("=" * 50)
    print(f"📄 目标URL: {url}")
    
    try:
        # 获取网页内容
        print("\n🌐 正在获取网页内容...")
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        print(f"✅ 网页获取成功 (状态码: {response.status_code})")
        
        # 解析HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 寻找文章正文容器
        print("\n🔍 寻找文章正文区域...")
        
        # 可能的正文容器选择器
        selectors = [
            '.article-content',
            '.detail-content',
            '.post-content',
            '.content-body',
            '.main-content',
            'article',
            '.rich-content',
            '[class*="content"]'
        ]
        
        content_container = None
        max_text_length = 0
        
        # 找到文本最多的容器作为正文区域
        for selector in selectors:
            try:
                elements = soup.select(selector)
                for element in elements:
                    # 排除导航、侧边栏等
                    class_attr = ' '.join(element.get('class', []))
                    if any(keyword in class_attr.lower() for keyword in ['nav', 'sidebar', 'header', 'footer']):
                        continue
                    
                    text_length = len(element.get_text().strip())
                    if text_length > max_text_length and text_length > 300:  # 至少300字符
                        max_text_length = text_length
                        content_container = element
                        print(f"✅ 找到正文容器: {selector} (文本长度: {text_length})")
            except:
                continue
        
        if not content_container:
            print("⚠️  未找到明确的正文区域，将使用整个页面")
            content_container = soup
        
        # 在正文区域查找图片
        print(f"\n🖼️  在正文区域查找图片...")
        img_tags = content_container.find_all('img')
        print(f"找到 {len(img_tags)} 个图片标签")
        
        if not img_tags:
            print("❌ 正文区域没有找到图片")
            return
        
        # 过滤正文图片
        print("\n🔍 过滤正文图片...")
        valid_images = []
        
        # 需要跳过的关键词
        skip_keywords = [
            'logo', 'icon', 'avatar', 'head', 'nav', 'menu', 'sidebar',
            'ad', 'banner', 'widget', 'button', 'arrow', 'loading',
            'placeholder', 'default', 'thumb'
        ]
        
        for i, img in enumerate(img_tags):
            # 获取图片源
            src = img.get('src') or img.get('data-src') or img.get('data-original')
            if not src:
                print(f"⏭️  跳过第{i+1}个图片: 无src属性")
                continue
            
            # 跳过base64图片
            if src.startswith('data:'):
                print(f"⏭️  跳过第{i+1}个图片: base64图片")
                continue
            
            # 转换为绝对URL
            full_url = urljoin(url, src)
            
            # 检查URL是否包含跳过的关键词
            should_skip = False
            url_lower = full_url.lower()
            for keyword in skip_keywords:
                if keyword in url_lower:
                    print(f"⏭️  跳过第{i+1}个图片: 包含关键词'{keyword}'")
                    should_skip = True
                    break
            
            if should_skip:
                continue
            
            # 检查alt属性
            alt = img.get('alt', '').lower()
            if any(keyword in alt for keyword in skip_keywords):
                print(f"⏭️  跳过第{i+1}个图片: alt属性包含跳过关键词")
                continue
            
            # 跳过某些文件类型
            if any(full_url.lower().endswith(ext) for ext in ['.svg', '.ico']):
                print(f"⏭️  跳过第{i+1}个图片: 不支持的文件类型")
                continue
            
            # 通过所有过滤条件
            valid_images.append(full_url)
            print(f"✅ 保留第{len(valid_images)}个图片: {os.path.basename(full_url)}")
        
        print(f"\n📊 过滤结果: 保留 {len(valid_images)}/{len(img_tags)} 张图片")
        
        if not valid_images:
            print("❌ 过滤后没有符合条件的图片")
            return
        
        # 下载图片
        save_dir = "article_images"
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
            print(f"\n📁 创建目录: {save_dir}")
        
        print(f"\n⬇️  开始下载 {len(valid_images)} 张图片...")
        print("-" * 30)
        
        success_count = 0
        
        for i, img_url in enumerate(valid_images, 1):
            try:
                print(f"下载第 {i}/{len(valid_images)} 张图片...")
                
                # 下载图片
                img_response = requests.get(img_url, headers=headers, timeout=30)
                img_response.raise_for_status()
                
                # 确定文件扩展名
                content_type = img_response.headers.get('content-type', '').lower()
                if 'jpeg' in content_type or 'jpg' in content_type:
                    ext = '.jpg'
                elif 'png' in content_type:
                    ext = '.png'
                elif 'webp' in content_type:
                    ext = '.webp'
                else:
                    ext = '.jpg'  # 默认
                
                # 生成文件名
                filename = f"article_img_{i:03d}{ext}"
                filepath = os.path.join(save_dir, filename)
                
                # 保存图片
                with open(filepath, 'wb') as f:
                    f.write(img_response.content)
                
                file_size = len(img_response.content)
                print(f"  ✅ 保存成功: {filename} ({file_size:,} bytes)")
                success_count += 1
                
                # 延时避免请求过快
                time.sleep(1)
                
            except Exception as e:
                print(f"  ❌ 下载失败: {e}")
        
        print("\n" + "=" * 50) 
        print("🎉 提取完成!")
        print(f"📊 成功下载: {success_count}/{len(valid_images)} 张图片")
        print(f"📁 保存位置: {os.path.abspath(save_dir)}")
        
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
