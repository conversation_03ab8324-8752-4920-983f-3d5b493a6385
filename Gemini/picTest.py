import requests
import json
import base64
import os

# 读取图片文件
image_path = r"D:\06-AI 测试文件\gemini-2.5pro测试\检查报告单.png"

# 检查文件是否存在
if not os.path.exists(image_path):
    print(f"错误：找不到图片文件 {image_path}")
    exit()

try:
    # 读取并编码图片
    with open(image_path, "rb") as image_file:
        encoded_string = base64.b64encode(image_file.read()).decode('utf-8')

    url = "https://yunwu.ai/v1/chat/completions"

    payload = {
        "model": "gemini-2.5-flash-preview-04-17",
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{encoded_string}"
                        }
                    },
                    {
                        "type": "text",
                        "text": "提取图片上的内容，保持原有样式，并用Html格式输出"
                    }
                ]
            }
        ]
    }

    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer sk-Mjv9S6FE5oIC3k5F9fxXfXHXWOeaaQcH9qx14IRRGeGEFzzr'
    }

    response = requests.post(url, headers=headers, json=payload)

    # 检查响应状态码
    response.raise_for_status()

    # 解析并打印结果
    result = response.json()
    if 'choices' in result and len(result['choices']) > 0:
        content = result['choices'][0]['message']['content']
        print(content)
    else:
        print("未能获取有效的响应内容")
        print("API响应:", result)

except FileNotFoundError:
    print(f"错误：无法打开文件 {image_path}")
except requests.exceptions.RequestException as e:
    print(f"API请求错误：{str(e)}")
    if hasattr(e.response, 'text'):
        print("错误详情:", e.response.text)
except json.JSONDecodeError:
    print("错误：无法解析API响应")
except Exception as e:
    print(f"发生未知错误：{str(e)}")
