<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Busy City - Three.js</title>
    <style>
        body { margin: 0; overflow: hidden; background-color: #000; }
        canvas { display: block; }
        #info {
            position: absolute;
            top: 10px;
            width: 100%;
            text-align: center;
            color: white;
            font-family: Arial, sans-serif;
            font-size: 16px;
            z-index: 100;
        }
    </style>
</head>
<body>
    <div id="info">Busy City Simulation<br>Use mouse to orbit, scroll to zoom, right-click to pan.</div>
    <script type="importmap">
        {
            "imports": {
                "three": "https://unpkg.com/three@0.160.0/build/three.module.js",
                "three/addons/": "https://unpkg.com/three@0.160.0/examples/jsm/"
            }
        }
    </script>
        <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';

        let scene, camera, renderer, controls, clock;
        let sunLight, ambientLight, hemisphereLight;
        let ground;
        const buildings = [];
        const cars = [];
        const pedestrians = [];
        const streetLights = [];
        let trafficLightState = 0; // 0: Red, 1: Yellow, 2: Green for one direction
        let trafficLightTimer = 0;
        const trafficLightCycle = { red: 8, yellow: 2, green: 8 }; // seconds

        const buildingTypes = {
            SKYSCRAPER: 'skyscraper',
            APARTMENT: 'apartment',
            SHOP: 'shop'
        };

        function init() {
            // Scene
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x87CEEB); // Start with a sky blue
            scene.fog = new THREE.Fog(0x87CEEB, 200, 700);

            // Clock
            clock = new THREE.Clock();

            // Camera
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(50, 60, 100);
            camera.lookAt(0, 0, 0);

            // Renderer
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap; // Softer shadows
            document.body.appendChild(renderer.domElement);

            // Controls
            controls = new OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;
            controls.screenSpacePanning = false;
            controls.minDistance = 20;
            controls.maxDistance = 400;
            controls.maxPolarAngle = Math.PI / 2.1; // Don't go below ground

            // Lighting
            ambientLight = new THREE.AmbientLight(0xffffff, 0.2); // Soft white light
            scene.add(ambientLight);

            hemisphereLight = new THREE.HemisphereLight(0x87CEEB, 0x080820, 0.6); // Sky, ground, intensity
            scene.add(hemisphereLight);

            sunLight = new THREE.DirectionalLight(0xffffff, 1.5); // Simulates sunlight
            sunLight.position.set(-100, 150, 100);
            sunLight.castShadow = true;
            sunLight.shadow.mapSize.width = 2048;
            sunLight.shadow.mapSize.height = 2048;
            sunLight.shadow.camera.near = 0.5;
            sunLight.shadow.camera.far = 500;
            sunLight.shadow.camera.left = -200;
            sunLight.shadow.camera.right = 200;
            sunLight.shadow.camera.top = 200;
            sunLight.shadow.camera.bottom = -200;
            scene.add(sunLight);
            scene.add(sunLight.target); // Target will be updated for day/night

            // Ground Plane
            const groundGeometry = new THREE.PlaneGeometry(500, 500);
            const groundMaterial = new THREE.MeshStandardMaterial({ color: 0x555555, side: THREE.DoubleSide });
            ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.receiveShadow = true;
            scene.add(ground);

            createCityLayout();
            createVehicles();
            createPedestrians();

            window.addEventListener('resize', onWindowResize, false);
            animate();
        }

        function createBuilding(width, height, depth, x, z, type = buildingTypes.APARTMENT) {
            const buildingGroup = new THREE.Group();
            buildingGroup.position.set(x, height / 2, z);

            let baseColor;
            switch(type) {
                case buildingTypes.SKYSCRAPER: baseColor = new THREE.Color(0x606070); break;
                case buildingTypes.APARTMENT: baseColor = new THREE.Color(0x908070); break;
                case buildingTypes.SHOP: baseColor = new THREE.Color(0xB0A090); break;
                default: baseColor = new THREE.Color(0x888888);
            }

            const buildingGeometry = new THREE.BoxGeometry(width, height, depth);
            const buildingMaterial = new THREE.MeshStandardMaterial({
                color: baseColor,
                roughness: 0.8,
                metalness: 0.2
            });
            const buildingMesh = new THREE.Mesh(buildingGeometry, buildingMaterial);
            buildingMesh.castShadow = true;
            buildingMesh.receiveShadow = true;
            buildingGroup.add(buildingMesh);

            // Store Bounding Box for collision
            buildingGroup.userData.bbox = new THREE.Box3().setFromObject(buildingMesh);
            // Make sure bbox is in world coordinates if buildingGroup itself is transformed significantly beyond position
            // For this setup, position is the main transform, so this should be okay.
            // If group had rotation/scale, we'd need: buildingGroup.updateMatrixWorld(true);
            // buildingGroup.userData.bbox.applyMatrix4(buildingGroup.matrixWorld);

            const windowMaterial = new THREE.MeshStandardMaterial({ color: 0x003366, emissive: 0x001122, emissiveIntensity: 0 });
            const windowDepth = 0.1;
            const numFloors = Math.max(1, Math.floor(height / 4));
            const windowsPerFloor = Math.max(1, Math.floor(Math.max(width, depth) / 4)); // Use larger dimension for window count

            if (type !== buildingTypes.SHOP || height > 6) {
                 for (let f = 0; f < numFloors; f++) {
                    // Front/Back (along Z if wider, along X if deeper)
                    for (let w = 0; w < windowsPerFloor; w++) {
                         if (Math.random() > 0.15) {
                            const windowWidth = 1.5;
                            const windowHeight = 2;
                            const windowGeometry = new THREE.BoxGeometry(windowWidth, windowHeight, windowDepth);
                            const windowMesh = new THREE.Mesh(windowGeometry, windowMaterial.clone());
                            const yPos = -height / 2 + 2.5 + f * 4;

                            if (width >= depth) { // Wider building, windows on Z-facing sides
                                windowMesh.position.set(
                                    -width / 2 + 2 + w * (width - 4) / Math.max(1, windowsPerFloor - 1),
                                    yPos,
                                    depth / 2 + windowDepth / 2 + 0.01
                                );
                                buildingGroup.add(windowMesh);
                                const windowMeshBack = windowMesh.clone();
                                windowMeshBack.position.z = -depth / 2 - windowDepth / 2 - 0.01;
                                buildingGroup.add(windowMeshBack);
                            } else { // Deeper building, windows on X-facing sides
                                windowMesh.position.set(
                                    width / 2 + windowDepth / 2 + 0.01,
                                    yPos,
                                    -depth / 2 + 2 + w * (depth - 4) / Math.max(1, windowsPerFloor - 1)
                                );
                                windowMesh.rotation.y = Math.PI / 2;
                                buildingGroup.add(windowMesh);
                                const windowMeshSide = windowMesh.clone();
                                windowMeshSide.position.x = -width / 2 - windowDepth / 2 - 0.01;
                                buildingGroup.add(windowMeshSide);
                            }
                        }
                    }
                }
            }

            if (Math.random() > 0.5 && type !== buildingTypes.SHOP) {
                const roofDetailHeight = Math.random() * 2 + 1;
                const roofDetailGeo = new THREE.BoxGeometry(width * 0.8, roofDetailHeight, depth * 0.8);
                const roofDetailMat = new THREE.MeshStandardMaterial({ color: baseColor.clone().multiplyScalar(0.8) });
                const roofDetailMesh = new THREE.Mesh(roofDetailGeo, roofDetailMat);
                roofDetailMesh.position.y = height / 2 + roofDetailHeight / 2 -0.1;
                roofDetailMesh.castShadow = true;
                buildingGroup.add(roofDetailMesh);
            }

            scene.add(buildingGroup);
            buildings.push(buildingGroup); // Store the group
            return buildingGroup;
        }

        function createRoad(width, length, x, z, rotationY = 0) {
            const roadGeometry = new THREE.PlaneGeometry(width, length);
            const roadMaterial = new THREE.MeshStandardMaterial({ color: 0x333333 });
            const roadMesh = new THREE.Mesh(roadGeometry, roadMaterial);
            roadMesh.rotation.x = -Math.PI / 2;
            // roadMesh.rotation.z = rotationY; // Incorrect for plane geometry if we mean world Y rotation
            roadMesh.position.set(x, 0.05, z);
            if (rotationY !== 0) roadMesh.geometry.rotateY(rotationY); // Rotate geometry if it's for N-S road
            roadMesh.receiveShadow = true;
            scene.add(roadMesh);

            const lineMaterial = new THREE.MeshBasicMaterial({ color: 0xffffff });
            const numDashes = Math.floor(length / 8);
            for (let i = 0; i < numDashes; i++) {
                if (i % 2 === 0) {
                    const dashGeom = new THREE.PlaneGeometry(0.3, 4);
                    const dashMesh = new THREE.Mesh(dashGeom, lineMaterial);
                    dashMesh.rotation.x = -Math.PI / 2;

                    if (rotationY === 0) { // Road along X (East-West)
                        dashMesh.position.set(x - length / 2 + 2 + i * 8, 0.06, z); // Start from one end
                    } else { // Road along Z (North-South)
                        dashMesh.geometry.rotateY(Math.PI/2); // Rotate dash geometry
                        dashMesh.position.set(x, 0.06, z - length / 2 + 2 + i * 8);
                    }
                    scene.add(dashMesh);
                }
            }
            return roadMesh;
        }

        function createCrosswalk(roadWidth, crosswalkLength, roadCenterX, roadCenterZ, onXAxisRoad = true) {
            const stripeWidth = 1; // Width of each white stripe
            const stripeLengthForCrosswalk = roadWidth * 0.8; // How long each stripe is (across the road)
            const numStripes = Math.floor(crosswalkLength / (stripeWidth * 2.5)); // Number of stripes along the crosswalk length
            const stripeMaterial = new THREE.MeshBasicMaterial({ color: 0xffffff });

            for (let i = 0; i < numStripes; i++) {
                // Stripe geometry depends on which way the *crosswalk itself* runs
                const stripeGeom = new THREE.PlaneGeometry(
                    onXAxisRoad ? stripeWidth : stripeLengthForCrosswalk, // If road is X-axis, crosswalk stripes are thin along X
                    onXAxisRoad ? stripeLengthForCrosswalk : stripeWidth   // and long along Z
                );
                const stripe = new THREE.Mesh(stripeGeom, stripeMaterial);
                stripe.rotation.x = -Math.PI / 2;
                stripe.position.y = 0.07;

                if (onXAxisRoad) { // Crosswalk is perpendicular to an E/W road (so crosswalk runs N/S)
                    // roadCenterX is the X position of the crosswalk, roadCenterZ is the Z center of the E/W road
                    stripe.position.x = roadCenterX;
                    stripe.position.z = roadCenterZ - crosswalkLength / 2 + (i + 0.5) * (stripeWidth * 2.5);
                } else { // Crosswalk is perpendicular to an N/S road (so crosswalk runs E/W)
                    // roadCenterX is the X center of the N/S road, roadCenterZ is the Z position of the crosswalk
                    stripe.position.x = roadCenterX - crosswalkLength / 2 + (i + 0.5) * (stripeWidth * 2.5);
                    stripe.position.z = roadCenterZ;
                }
                scene.add(stripe);
            }
        }

        function createTrafficLightPole(x, y, z, facingPositiveAxis = 'z') { // axis can be 'x' or 'z'
            const poleHeight = 8;
            const poleGeo = new THREE.CylinderGeometry(0.2, 0.2, poleHeight);
            const poleMat = new THREE.MeshStandardMaterial({color: 0x505050});
            const pole = new THREE.Mesh(poleGeo, poleMat);
            pole.position.set(x, y + poleHeight/2, z);
            pole.castShadow = true;
            scene.add(pole);

            const armLength = 3;
            const armGeo = new THREE.CylinderGeometry(0.15,0.15, armLength);
            const arm = new THREE.Mesh(armGeo, poleMat);
            arm.position.set(x, y + poleHeight - 0.5, z); // Base position

            const lightBoxGeo = new THREE.BoxGeometry(0.6, 1.8, 0.6);
            const lightBoxMat = new THREE.MeshStandardMaterial({color: 0x303030});
            const lightBox = new THREE.Mesh(lightBoxGeo, lightBoxMat);
            lightBox.position.set(x, y + poleHeight - 0.5, z); // Base position

            if (facingPositiveAxis === 'z') { // Facing +Z (for cars moving North)
                arm.position.x += armLength/2;
                arm.rotation.z = Math.PI/2;
                lightBox.position.x += armLength;
            } else if (facingPositiveAxis === '-z') { // Facing -Z (for cars moving South)
                arm.position.x -= armLength/2;
                arm.rotation.z = -Math.PI/2;
                lightBox.position.x -= armLength;
                lightBox.rotation.y = Math.PI;
            } else if (facingPositiveAxis === 'x') { // Facing +X (for cars moving East)
                arm.position.z -= armLength/2; // Arm extends towards -Z
                arm.rotation.x = Math.PI/2;
                lightBox.position.z -= armLength;
                lightBox.rotation.y = -Math.PI/2;
            } else if (facingPositiveAxis === '-x') { // Facing -X (for cars moving West)
                arm.position.z += armLength/2; // Arm extends towards +Z
                arm.rotation.x = -Math.PI/2;
                lightBox.position.z += armLength;
                lightBox.rotation.y = Math.PI/2;
            }

            scene.add(arm);
            scene.add(lightBox);

            const lightRadius = 0.2;
            const lightGeo = new THREE.SphereGeometry(lightRadius, 16, 8);

            const redLightMat = new THREE.MeshStandardMaterial({color: 0xff0000, emissive: 0x880000, emissiveIntensity: 0});
            const redLight = new THREE.Mesh(lightGeo, redLightMat);
            redLight.position.set(0, 0.6, 0.31);
            lightBox.add(redLight);

            const yellowLightMat = new THREE.MeshStandardMaterial({color: 0xffff00, emissive: 0x888800, emissiveIntensity: 0});
            const yellowLight = new THREE.Mesh(lightGeo, yellowLightMat);
            yellowLight.position.set(0, 0, 0.31);
            lightBox.add(yellowLight);

            const greenLightMat = new THREE.MeshStandardMaterial({color: 0x00ff00, emissive: 0x008800, emissiveIntensity: 0});
            const greenLight = new THREE.Mesh(lightGeo, greenLightMat);
            greenLight.position.set(0, -0.6, 0.31);
            lightBox.add(greenLight);

            return {box: lightBox, red: redLight, yellow: yellowLight, green: greenLight, facing: facingPositiveAxis};
        }

        let trafficLightGroups = { 'ns': [], 'ew': [] }; // Separate groups for N/S and E/W lights


        function createStreetLamp(x, y, z) {
            const group = new THREE.Group();
            const poleHeight = 7;
            const poleGeo = new THREE.CylinderGeometry(0.15, 0.2, poleHeight, 8);
            const poleMat = new THREE.MeshStandardMaterial({ color: 0x666666 });
            const pole = new THREE.Mesh(poleGeo, poleMat);
            pole.castShadow = true;
            group.add(pole);

            const armGeo = new THREE.BoxGeometry(0.2, 0.2, 1.5);
            const arm = new THREE.Mesh(armGeo, poleMat);
            arm.position.set(0, poleHeight / 2 - 0.3, 0.65);
            arm.castShadow = true;
            group.add(arm);

            const lampHeadGeo = new THREE.CylinderGeometry(0.5, 0.3, 0.6, 8);
            const lampHeadMat = new THREE.MeshStandardMaterial({ color: 0x444444 });
            const lampHead = new THREE.Mesh(lampHeadGeo, lampHeadMat);
            lampHead.position.set(0, poleHeight / 2 - 0.3, 1.3);
            lampHead.castShadow = true;
            group.add(lampHead);

            const lightBulb = new THREE.PointLight(0xffffee, 0, 20, 1.5);
            lightBulb.position.set(0, -0.3, 0);
            lampHead.add(lightBulb);

            group.position.set(x, y + poleHeight / 2, z);
            scene.add(group);
            streetLights.push({mesh: group, light: lightBulb});
        }

        function createTree(x,y,z) {
            const group = new THREE.Group();
            const trunkHeight = Math.random() * 2 + 3;
            const trunkRadius = Math.random() * 0.2 + 0.2;
            const trunkGeo = new THREE.CylinderGeometry(trunkRadius * 0.7, trunkRadius, trunkHeight, 8);
            const trunkMat = new THREE.MeshStandardMaterial({color: 0x8B4513});
            const trunk = new THREE.Mesh(trunkGeo, trunkMat);
            trunk.castShadow = true;
            group.add(trunk);

            const leavesHeight = Math.random() * 3 + 3;
            const leavesRadius = Math.random() * 1 + 1.5;
            const leavesGeo = new THREE.IcosahedronGeometry(leavesRadius, 0);
            const leavesMat = new THREE.MeshStandardMaterial({color: 0x228B22});
            const leaves = new THREE.Mesh(leavesGeo, leavesMat);
            leaves.position.y = trunkHeight/2 + leavesHeight * 0.4;
            leaves.castShadow = true;
            group.add(leaves);

            group.position.set(x,y + trunkHeight/2, z);
            scene.add(group);
        }

        function createBench(x,y,z, rotationY = 0) {
            const group = new THREE.Group();
            const seatHeight = 0.5;
            const seatWidth = 2;
            const seatDepth = 0.4;
            const legHeight = 0.4;
            const legSize = 0.1;

            const seatMat = new THREE.MeshStandardMaterial({color: 0xD2B48C});
            const legMat = new THREE.MeshStandardMaterial({color: 0x505050});

            const seatGeo = new THREE.BoxGeometry(seatWidth, seatHeight * 0.2, seatDepth);
            const seat = new THREE.Mesh(seatGeo, seatMat);
            seat.position.y = legHeight + (seatHeight * 0.2)/2;
            seat.castShadow = true;
            group.add(seat);

            const backGeo = new THREE.BoxGeometry(seatWidth, seatHeight * 0.8, seatDepth * 0.2);
            const back = new THREE.Mesh(backGeo, seatMat);
            back.position.y = legHeight + seatHeight * 0.2 + (seatHeight * 0.8)/2 * 0.8;
            back.position.z = -seatDepth/2 + (seatDepth*0.2)/2;
            back.rotation.x = Math.PI / 12;
            back.castShadow = true;
            group.add(back);

            const legGeo = new THREE.BoxGeometry(legSize, legHeight, legSize);
            const leg1 = new THREE.Mesh(legGeo, legMat);
            leg1.position.set(-seatWidth/2 + legSize, legHeight/2, seatDepth/2 - legSize);
            leg1.castShadow = true;
            group.add(leg1);
            const leg2 = leg1.clone();
            leg2.position.x = seatWidth/2 - legSize;
            group.add(leg2);
            const leg3 = leg1.clone();
            leg3.position.z = -seatDepth/2 + legSize;
            group.add(leg3);
            const leg4 = leg2.clone();
            leg4.position.z = -seatDepth/2 + legSize;
            group.add(leg4);

            group.position.set(x,y,z);
            group.rotation.y = rotationY;
            scene.add(group);
        }


        function createCityLayout() {
            const mainRoadWidth = 12;
            const secondaryRoadWidth = 10;
            // Roads
            createRoad(mainRoadWidth, 200, 0, 0, Math.PI / 2); // Main North-South
            createRoad(mainRoadWidth, 200, 0, 0, 0);          // Main East-West
            createRoad(secondaryRoadWidth, 200, 60, 0, Math.PI / 2);
            createRoad(secondaryRoadWidth, 200, 0, 60, 0);
            createRoad(secondaryRoadWidth, 200, -60, 0, Math.PI / 2);
            createRoad(secondaryRoadWidth, 200, 0, -60, 0);

            // Traffic Lights at main intersection (0,0)
            // N/S lights
            trafficLightGroups.ns.push(createTrafficLightPole(8, 0, 8, '-z')); // For S-bound traffic approaching from N
            trafficLightGroups.ns.push(createTrafficLightPole(-8, 0, -8, 'z'));// For N-bound traffic approaching from S
            // E/W lights
            trafficLightGroups.ew.push(createTrafficLightPole(-8, 0, 8, 'x')); // For E-bound traffic approaching from W
            trafficLightGroups.ew.push(createTrafficLightPole(8, 0, -8, '-x'));// For W-bound traffic approaching from E

            // Crosswalks at (0,0) intersection
            const crosswalkLen = 10; // Length of the crosswalk area
            createCrosswalk(mainRoadWidth, crosswalkLen, 0, mainRoadWidth/2 + crosswalkLen/2, false); // N of intersection (on N/S road)
            createCrosswalk(mainRoadWidth, crosswalkLen, 0, -(mainRoadWidth/2 + crosswalkLen/2), false); // S of intersection
            createCrosswalk(mainRoadWidth, crosswalkLen, mainRoadWidth/2 + crosswalkLen/2, 0, true);   // E of intersection (on E/W road)
            createCrosswalk(mainRoadWidth, crosswalkLen, -(mainRoadWidth/2 + crosswalkLen/2), 0, true);  // W of intersection

            const blockSize = 55;
            const buildingPadding = 8;

            for (let i = -2; i <= 2; i++) {
                for (let j = -2; j <= 2; j++) {
                    if (i === 0 && j === 0) continue;

                    const centerX = i * blockSize;
                    const centerZ = j * blockSize;

                    const roadTouchX = (Math.abs(i) === 1 && j===0) ? mainRoadWidth : secondaryRoadWidth;
                    const roadTouchZ = (Math.abs(j) === 1 && i===0) ? mainRoadWidth : secondaryRoadWidth;


                    const worldX = centerX + Math.sign(centerX) * ( (i===0?0:roadTouchZ/2) + buildingPadding + Math.random()*5);
                    const worldZ = centerZ + Math.sign(centerZ) * ( (j===0?0:roadTouchX/2) + buildingPadding + Math.random()*5);

                    let type = buildingTypes.APARTMENT;
                    let bWidth = Math.random() * 15 + 10;
                    let bHeight = Math.random() * 30 + 15;
                    let bDepth = Math.random() * 15 + 10;

                    if (Math.abs(i) <= 1 && Math.abs(j) <= 1 && Math.random() > 0.3) {
                        type = buildingTypes.SKYSCRAPER;
                        bHeight = Math.random() * 60 + 40;
                        bWidth = Math.random() * 10 + 15;
                        bDepth = Math.random() * 10 + 15;
                    } else if (Math.random() > 0.6) {
                        type = buildingTypes.SHOP;
                        bHeight = Math.random() * 5 + 5;
                         bWidth = Math.random() * 10 + 8;
                        bDepth = Math.random() * 8 + 6;
                        if (Math.random() > 0.5) {
                             createStreetLamp(worldX, 0, worldZ + Math.sign(worldZ)*(bDepth/2 + 3));
                        } else {
                             createStreetLamp(worldX + Math.sign(worldX)*(bWidth/2 + 3), 0, worldZ);
                        }
                    }
                    createBuilding(bWidth, bHeight, bDepth, worldX, worldZ, type);

                    if (Math.random() < 0.4 && type !== buildingTypes.SHOP) {
                         createTree(worldX + (Math.random()-0.5)*bWidth * 1.2, 0, worldZ + (Math.random()-0.5)*bDepth * 1.2);
                    }
                    if (Math.random() < 0.2) {
                        const benchRot = Math.random() * Math.PI;
                        createBench(worldX + Math.sign(worldX)*(bWidth/2 + 2 + Math.random()*2), 0, worldZ + (Math.random()-0.5)*bDepth, benchRot);
                    }
                }
            }
            for (let k = -80; k <= 80; k += 40) {
                if (k === 0 && Math.abs(k) < mainRoadWidth) continue;
                createStreetLamp(mainRoadWidth/2 + 3, 0, k);
                createStreetLamp(-(mainRoadWidth/2 + 3), 0, k);
                createStreetLamp(k, 0, mainRoadWidth/2 + 3);
                createStreetLamp(k, 0, -(mainRoadWidth/2 + 3));
            }
        }

        function createCar() {
            const carColors = [0xff0000, 0x0000ff, 0x00ff00, 0xffff00, 0xffa500, 0x808080, 0xffffff, 0x000000];
            const carGroup = new THREE.Group();

            const bodyWidth = 2, bodyHeight = 1.2, bodyLength = 4; // Length is along local Z
            const bodyGeometry = new THREE.BoxGeometry(bodyWidth, bodyHeight, bodyLength);
            const bodyMaterial = new THREE.MeshStandardMaterial({
                color: carColors[Math.floor(Math.random() * carColors.length)],
                roughness: 0.3,
                metalness: 0.5
            });
            const bodyMesh = new THREE.Mesh(bodyGeometry, bodyMaterial);
            bodyMesh.castShadow = true;
            carGroup.add(bodyMesh);

            const cabinHeight = 0.8, cabinOffsetZ = -0.3;
            const cabinGeometry = new THREE.BoxGeometry(bodyWidth * 0.85, cabinHeight, bodyLength * 0.6);
            const cabinMaterial = new THREE.MeshStandardMaterial({ color: 0x336699, transparent: true, opacity: 0.5});
            const cabinMesh = new THREE.Mesh(cabinGeometry, cabinMaterial);
            cabinMesh.position.set(0, bodyHeight/2 + cabinHeight/2 -0.2, cabinOffsetZ);
            carGroup.add(cabinMesh);

            const wheelRadius = 0.4, wheelWidth = 0.2;
            const wheelGeometry = new THREE.CylinderGeometry(wheelRadius, wheelRadius, wheelWidth, 16);
            const wheelMaterial = new THREE.MeshStandardMaterial({ color: 0x111111 });

            // Wheels positions relative to car body (bodyLength is along Z, bodyWidth along X)
            const wheelPositions = [
                {x: bodyWidth/2 - wheelRadius*0.7, y: -bodyHeight/2 + wheelRadius, z: bodyLength/2 - wheelRadius*1.5}, // Front right
                {x: -bodyWidth/2 + wheelRadius*0.7, y: -bodyHeight/2 + wheelRadius, z: bodyLength/2 - wheelRadius*1.5},// Front left
                {x: bodyWidth/2 - wheelRadius*0.7, y: -bodyHeight/2 + wheelRadius, z: -bodyLength/2 + wheelRadius*1.5},// Rear right
                {x: -bodyWidth/2 + wheelRadius*0.7, y: -bodyHeight/2 + wheelRadius, z: -bodyLength/2 + wheelRadius*1.5}// Rear left
            ];

            wheelPositions.forEach(pos => {
                const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
                // Wheels are cylinders, default orientation is upright along Y.
                // Rotate them to lie flat and point along X for car.
                wheel.rotation.z = Math.PI / 2;
                wheel.position.set(pos.x, pos.y, pos.z);
                wheel.castShadow = true;
                carGroup.add(wheel);
            });

            carGroup.userData.width = bodyWidth;
            carGroup.userData.length = bodyLength;
            carGroup.position.y = wheelRadius; // Ground car by wheel radius

            const headlightGeo = new THREE.SphereGeometry(0.15, 8, 8);
            const headlightMat = new THREE.MeshStandardMaterial({color:0xffffff, emissive: 0xeeeeaa, emissiveIntensity: 0});
            const hl1 = new THREE.Mesh(headlightGeo, headlightMat);
            hl1.position.set(bodyWidth/2 * 0.7, 0, bodyLength/2 + 0.05); // Front of car body
            bodyMesh.add(hl1);
            const hl2 = hl1.clone();
            hl2.position.x *= -1;
            bodyMesh.add(hl2);
            carGroup.headlights = [hl1, hl2];

            return carGroup;
        }

        function createVehicles() {
            const numCars = 30;
            const mainRoadLaneOffset = 3; // For main roads (width 12)
            const secondaryRoadLaneOffset = 2.5; // For secondary roads (width 10)

            for (let i = 0; i < numCars; i++) {
                const car = createCar();
                const speed = Math.random() * 15 + 15;
                let direction = Math.random() < 0.5 ? 1 : -1; // 1 for positive axis travel, -1 for negative
                let axis, laneOffsetVal, startPos, rotationY;

                const roadChoice = Math.random();
                if (roadChoice < 0.25) { // Main E/W road (Z=0)
                    axis = 'x'; laneOffsetVal = direction > 0 ? -mainRoadLaneOffset : mainRoadLaneOffset; // Drive on right
                    startPos = new THREE.Vector3(direction * -150, car.position.y, laneOffsetVal);
                    rotationY = direction > 0 ? 0 : Math.PI; // Face +X or -X
                } else if (roadChoice < 0.5) { // Main N/S road (X=0)
                    axis = 'z'; laneOffsetVal = direction > 0 ? mainRoadLaneOffset : -mainRoadLaneOffset; // Drive on right (+X is right for +Z travel)
                    startPos = new THREE.Vector3(laneOffsetVal, car.position.y, direction * -150);
                    rotationY = direction > 0 ? -Math.PI / 2 : Math.PI / 2; // Face +Z or -Z
                } else if (roadChoice < 0.75) { // Secondary E/W road (Z= +/-60)
                    axis = 'x'; laneOffsetVal = direction > 0 ? -secondaryRoadLaneOffset : secondaryRoadLaneOffset;
                    const roadZ = Math.random() < 0.5 ? 60 : -60;
                    startPos = new THREE.Vector3(direction * -120, car.position.y, roadZ + laneOffsetVal);
                    rotationY = direction > 0 ? 0 : Math.PI;
                } else { // Secondary N/S road (X= +/-60)
                    axis = 'z'; laneOffsetVal = direction > 0 ? secondaryRoadLaneOffset : -secondaryRoadLaneOffset;
                    const roadX = Math.random() < 0.5 ? 60 : -60;
                    startPos = new THREE.Vector3(roadX + laneOffsetVal, car.position.y, direction * -120);
                    rotationY = direction > 0 ? -Math.PI / 2 : Math.PI / 2;
                }

                car.position.copy(startPos);
                car.rotation.y = rotationY;

                cars.push({ mesh: car, speed: speed, direction: direction, axis: axis, initialLaneOffset: laneOffsetVal, isStopped: false });
                scene.add(car);
            }
        }

        function createPerson() {
            const personGroup = new THREE.Group();
            const bodyHeight = 1.8;
            const headRadius = 0.25;

            const bodyGeo = new THREE.CapsuleGeometry(headRadius * 0.8, bodyHeight - headRadius*2 , 4, 8);
            const bodyMat = new THREE.MeshStandardMaterial({color: new THREE.Color(Math.random(), Math.random(), Math.random())});
            const body = new THREE.Mesh(bodyGeo, bodyMat);
            body.castShadow = true;
            personGroup.add(body);

            const headGeo = new THREE.SphereGeometry(headRadius, 16,8);
            const headMat = new THREE.MeshStandardMaterial({color: 0xFFDBAC});
            const head = new THREE.Mesh(headGeo, headMat);
            head.position.y = bodyHeight/2 - headRadius*0.1;
            head.castShadow = true;
            personGroup.add(head);

            personGroup.position.y = bodyHeight/2;
            return personGroup;
        }

        function createPedestrians() {
            const numPedestrians = 40;
            const mainSidewalkOffset = 7.5;
            const secSidewalkOffset = 6; // Road width 10/2 + 1 for sidewalk
            for (let i = 0; i < numPedestrians; i++) {
                const pedestrian = createPerson();
                let speed = Math.random() * 2 + 1;
                let direction = Math.random() < 0.5 ? 1 : -1;
                let axis, pathOffset, pathZ, pathX, rotationY;

                const choice = Math.random();
                if (choice < 0.25) {
                    axis = 'x';
                    pathZ = (Math.random() < 0.5 ? 1 : -1) * mainSidewalkOffset;
                    pedestrian.position.set(direction * -100, pedestrian.position.y, pathZ);
                    rotationY = direction > 0 ? 0 : Math.PI;
                } else if (choice < 0.5) {
                    axis = 'z';
                    pathX = (Math.random() < 0.5 ? 1 : -1) * mainSidewalkOffset;
                    pedestrian.position.set(pathX, pedestrian.position.y, direction * -100);
                    rotationY = direction > 0 ? -Math.PI / 2 : Math.PI / 2;
                } else if (choice < 0.75) {
                    axis = 'x';
                    const roadZBase = Math.random() < 0.5 ? 60 : -60;
                    pathZ = roadZBase + (Math.random() < 0.5 ? 1 : -1) * secSidewalkOffset;
                    pedestrian.position.set(direction * -80, pedestrian.position.y, pathZ);
                    rotationY = direction > 0 ? 0 : Math.PI;
                }
                else { // Crossing street
                    axis = (Math.random() < 0.5) ? 'x' : 'z';
                    if (axis === 'x') {
                        pedestrian.position.set(direction * -10, pedestrian.position.y, (Math.random()-0.5)*8);
                        rotationY = direction > 0 ? 0 : Math.PI;
                    } else {
                        pedestrian.position.set((Math.random()-0.5)*8, pedestrian.position.y, direction * -10);
                        rotationY = direction > 0 ? -Math.PI / 2 : Math.PI / 2;
                    }
                    speed *= 0.8;
                }
                pedestrian.rotation.y = rotationY;
                pedestrians.push({ mesh: pedestrian, speed: speed, direction: direction, axis: axis });
                scene.add(pedestrian);
            }
        }

        let currentGreenDirection = 'ns'; // 'ns' or 'ew'
        function updateTrafficLights(dt) {
            trafficLightTimer += dt;
            let activeIntensity = 2;
            let inactiveIntensity = 0.1;

            // Reset all lights
            [...trafficLightGroups.ns, ...trafficLightGroups.ew].forEach(tl => {
                tl.red.material.emissiveIntensity = inactiveIntensity;
                tl.yellow.material.emissiveIntensity = inactiveIntensity;
                tl.green.material.emissiveIntensity = inactiveIntensity;
            });

            let lightsToControl = (currentGreenDirection === 'ns') ? trafficLightGroups.ns : trafficLightGroups.ew;
            let otherLights = (currentGreenDirection === 'ns') ? trafficLightGroups.ew : trafficLightGroups.ns;

            // Other direction is always red when primary is not red
            otherLights.forEach(tl => tl.red.material.emissiveIntensity = activeIntensity);


            if (trafficLightState === 0) { // Current direction is RED
                lightsToControl.forEach(tl => tl.red.material.emissiveIntensity = activeIntensity);
                // The *other* direction should now be green or preparing to be red
                // For simplicity, when one is red, the other just finished its green/yellow cycle.
                // This state means currentGreenDirection is RED, so other direction just turned red.
                // So otherLights are also RED.
                if (trafficLightTimer > trafficLightCycle.red / 2) { // Shorter red for one direction if other is also red
                    trafficLightState = 2; // Switch to Green for currentGreenDirection
                    trafficLightTimer = 0;
                }
            } else if (trafficLightState === 2) { // Current direction is GREEN
                lightsToControl.forEach(tl => tl.green.material.emissiveIntensity = activeIntensity);
                if (trafficLightTimer > trafficLightCycle.green) {
                    trafficLightState = 1; // Switch to Yellow
                    trafficLightTimer = 0;
                }
            } else if (trafficLightState === 1) { // Current direction is YELLOW
                lightsToControl.forEach(tl => tl.yellow.material.emissiveIntensity = activeIntensity);
                if (trafficLightTimer > trafficLightCycle.yellow) {
                    trafficLightState = 0; // Switch to Red
                    trafficLightTimer = 0;
                    // Switch the primary green direction
                    currentGreenDirection = (currentGreenDirection === 'ns') ? 'ew' : 'ns';
                }
            }
        }


        function updateVehicles(dt) {
            const carHalfLength = 2; // Approx from car's local Z origin to front
            const carHalfWidth = 1;  // Approx from car's local X origin to side

            cars.forEach(carObj => {
                const car = carObj.mesh;
                let currentSpeed = carObj.speed;
                carObj.isStopped = false;

                // --- Traffic Light Logic ---
                const distToIntersectionCenter = carObj.axis === 'x' ? Math.abs(car.position.x) : Math.abs(car.position.z);
                const stoppingZoneStart = 25; // Start checking light
                const stoppingZoneEnd = 5;   // Must stop before this if light is red/yellow

                if (distToIntersectionCenter < stoppingZoneStart && distToIntersectionCenter > stoppingZoneEnd) {
                    let carIsOnNS = carObj.axis === 'z';
                    let carIsOnEW = carObj.axis === 'x';
                    let approachingFromPositive = (carIsOnNS && car.position.z > 0 && carObj.direction < 0) || // Approaching intersection from +Z, going -Z
                                                 (carIsOnEW && car.position.x > 0 && carObj.direction < 0);   // Approaching intersection from +X, going -X
                    let approachingFromNegative = (carIsOnNS && car.position.z < 0 && carObj.direction > 0) || // Approaching intersection from -Z, going +Z
                                                 (carIsOnEW && car.position.x < 0 && carObj.direction > 0);   // Approaching intersection from -X, going +X

                    if (approachingFromPositive || approachingFromNegative) {
                        if (carIsOnNS) { // Car on N/S road
                            if (currentGreenDirection === 'ew' || (currentGreenDirection === 'ns' && trafficLightState === 1)) { // E/W has green, or N/S has yellow
                                currentSpeed = 0; carObj.isStopped = true;
                            }
                        } else { // Car on E/W road
                             if (currentGreenDirection === 'ns' || (currentGreenDirection === 'ew' && trafficLightState === 1)) { // N/S has green, or E/W has yellow
                                currentSpeed = 0; carObj.isStopped = true;
                            }
                        }
                    }
                }

                // --- Basic Forward Collision Detection with Buildings ---
                if (!carObj.isStopped) { // Only check collision if not stopped by light
                    const forwardOffset = carHalfLength + currentSpeed * dt * 0.5 + 0.5; // Probe slightly ahead
                    const probePointWorld = new THREE.Vector3(0, 0, forwardOffset); // Local Z is forward
                    probePointWorld.applyMatrix4(car.matrixWorld); // Transform to world space

                    // Optional: Add side probes for wider collision check
                    const probeLeftWorld = new THREE.Vector3(-carHalfWidth * 0.8, 0, forwardOffset);
                    probeLeftWorld.applyMatrix4(car.matrixWorld);
                    const probeRightWorld = new THREE.Vector3(carHalfWidth * 0.8, 0, forwardOffset);
                    probeRightWorld.applyMatrix4(car.matrixWorld);

                    const probes = [probePointWorld, probeLeftWorld, probeRightWorld];
                    let collisionDetected = false;

                    for (const building of buildings) {
                        // Update building bbox if it could move (not in this sim, but good practice)
                        // building.userData.bbox.setFromObject(building.children[0]); // Assuming first child is main mesh
                        // building.userData.bbox.applyMatrix4(building.matrixWorld);

                        for (const probe of probes) {
                            if (building.userData.bbox.containsPoint(probe)) {
                                collisionDetected = true;
                                break;
                            }
                        }
                        if (collisionDetected) break;
                    }

                    if (collisionDetected) {
                        currentSpeed = 0;
                        carObj.isStopped = true; // Treat as stopped by obstacle
                    }
                }


                // --- Movement ---
                if (!carObj.isStopped) {
                    const moveDistance = currentSpeed * dt * carObj.direction;
                    if (carObj.axis === 'x') {
                        car.position.x += moveDistance;
                        // Simpler boundary wrapping:
                        if (car.position.x > 160 && carObj.direction > 0) car.position.x = -160;
                        if (car.position.x < -160 && carObj.direction < 0) car.position.x = 160;
                    } else { // axis === 'z'
                        car.position.z += moveDistance;
                        if (car.position.z > 160 && carObj.direction > 0) car.position.z = -160;
                        if (car.position.z < -160 && carObj.direction < 0) car.position.z = 160;
                    }
                }

                car.children.forEach(child => {
                    if (child.geometry.type === 'CylinderGeometry') {
                        child.rotation.x += currentSpeed * dt * carObj.direction * 0.15; // Simplified wheel rotation
                    }
                });
            });
        }

        function updatePedestrians(dt) {
            pedestrians.forEach(pObj => {
                const ped = pObj.mesh;
                const moveDistance = pObj.speed * dt * pObj.direction;
                if (pObj.axis === 'x') {
                    ped.position.x += moveDistance;
                    if (ped.position.x > 110 && pObj.direction > 0) ped.position.x = -110;
                    if (ped.position.x < -110 && pObj.direction < 0) ped.position.x = 110;
                } else { // axis === 'z'
                    ped.position.z += moveDistance;
                     if (ped.position.z > 110 && pObj.direction > 0) ped.position.z = -110;
                    if (ped.position.z < -110 && pObj.direction < 0) ped.position.z = 110;
                }
            });
        }

        function updateDayNightCycle(time) {
            const cycleDuration = 120;
            const normalizedTime = (time % cycleDuration) / cycleDuration;

            const sunAngle = normalizedTime * Math.PI * 2 - Math.PI / 2;
            sunLight.position.set(Math.cos(sunAngle) * 200, Math.sin(sunAngle) * 150, Math.cos(sunAngle * 0.5) * 100 + 50); // Added some Z variation
            sunLight.target.position.set(0,0,0);

            const dayIntensity = 1.5;
            const nightIntensitySun = 0.05;
            const nightIntensityAmbient = 0.1;
            const nightIntensityHemi = 0.1;

            const daySky = new THREE.Color(0x87CEEB);
            const nightSky = new THREE.Color(0x030815);
            const duskSky = new THREE.Color(0xFF8C00);

            const dayFog = new THREE.Color(0x87CEEB);
            const nightFog = new THREE.Color(0x030815);

            let sunIntensityFactor = Math.max(0, Math.sin(sunAngle + Math.PI/2));

            sunLight.intensity = THREE.MathUtils.lerp(nightIntensitySun, dayIntensity, sunIntensityFactor);
            ambientLight.intensity = THREE.MathUtils.lerp(nightIntensityAmbient, 0.3, sunIntensityFactor);
            hemisphereLight.intensity = THREE.MathUtils.lerp(nightIntensityHemi, 0.8, sunIntensityFactor);

            let currentSkyColor = new THREE.Color();
            let currentFogColor = new THREE.Color();

            if (sunIntensityFactor > 0.1) {
                const transition = THREE.MathUtils.smoothstep(sunIntensityFactor, 0.1, 0.4);
                currentSkyColor.lerpColors(duskSky, daySky, transition);
                currentFogColor.lerpColors(duskSky, dayFog, transition); // Fog can use same dusk color
                 hemisphereLight.groundColor.setHex(0x404020);
            } else {
                currentSkyColor.copy(nightSky);
                currentFogColor.copy(nightFog);
                hemisphereLight.groundColor.setHex(0x080820);
            }

            scene.background.copy(currentSkyColor);
            scene.fog.color.copy(currentFogColor);
            hemisphereLight.color.copy(currentSkyColor);

            const isNight = sunIntensityFactor < 0.15;
            streetLights.forEach(sl => {
                sl.light.intensity = isNight ? 1.5 : 0;
            });
            cars.forEach(carObj => {
                carObj.mesh.headlights.forEach(hl => hl.material.emissiveIntensity = isNight ? 2 : 0);
            });
            buildings.forEach(building => {
                building.children.forEach(child => {
                    if (child.geometry.type === "BoxGeometry" && child.material.emissive) {
                        if(child.parent === building) { // Only top-level windows for now
                             child.material.emissiveIntensity = isNight && Math.random() > 0.3 ? (Math.random()*0.5+0.3) : 0;
                        }
                    }
                });
            });
        }


        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }

        function animate() {
            requestAnimationFrame(animate);
            const delta = clock.getDelta();
            const elapsedTime = clock.getElapsedTime();

            controls.update();

            updateTrafficLights(delta);
            updateVehicles(delta); // Pass delta
            updatePedestrians(delta);
            updateDayNightCycle(elapsedTime);

            renderer.render(scene, camera);
        }

        init();
    </script>
</body>
</html>