<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>太阳系模拟动画</title>
    <style>
        body {
            margin: 0;
            background-color: #000000;
            color: #ffffff;
            font-family: Arial, sans-serif;
            overflow: hidden; /* 防止拖动时出现滚动条 */
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            position: relative; /* 为星星背景定位 */
        }

        #universe {
            width: 100%;
            height: 100%;
            position: relative;
            cursor: grab; /* 拖动提示 */
        }

        #solar-system-container {
            position: absolute;
            /* 中心点将通过JS设置，并由拖动和缩放更新 */
            transform-origin: center center; /* 确保缩放以中心为基准 */
        }

        #sun {
            width: 80px;
            height: 80px;
            background-color: #FFD700; /* 金色 */
            border-radius: 50%;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%); /* 确保太阳在容器中心 */
            box-shadow: 0 0 30px 15px #FF8C00, 0 0 50px 25px #FFA500; /* 辉光效果 */
            animation: pulse 2s infinite alternate ease-in-out;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 35px 18px #FF8C00, 0 0 55px 28px #FFA500;
                transform: translate(-50%, -50%) scale(1);
            }
            100% {
                box-shadow: 0 0 45px 22px #FF8C00, 0 0 65px 32px #FFA500;
                transform: translate(-50%, -50%) scale(1.05);
            }
        }

        .celestial-body {
            position: absolute;
            border-radius: 50%;
            transform: translate(-50%, -50%); /* 中心定位 */
            cursor: pointer;
        }

        .orbit-path {
            position: absolute;
            border: 1px dashed rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            pointer-events: none; /* 确保轨道线不会干扰点击行星 */
            transform: translate(-50%, -50%); /* 中心定位 */
        }

        .asteroid {
            position: absolute;
            background-color: #A9A9A9; /* 暗灰色 */
            border-radius: 50%;
            opacity: 0.7;
        }

        #controls {
            position: fixed;
            top: 20px;
            left: 20px;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
        }

        #controls button, #controls label {
            margin: 5px;
            color: white;
        }
        #controls input[type="checkbox"]{
            vertical-align: middle;
        }

        #info-panel {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: rgba(0, 0, 0, 0.85);
            padding: 15px;
            border-radius: 8px;
            z-index: 1000;
            max-width: 250px;
            display: none; /* 初始隐藏 */
            border: 1px solid #555;
        }

        #info-panel h3 {
            margin-top: 0;
            color: #FFD700;
        }
        #info-panel p {
            font-size: 0.9em;
            line-height: 1.4;
        }
        #close-info-panel {
            background-color: #555;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            margin-top: 10px;
            float: right;
        }
        #close-info-panel:hover {
            background-color: #777;
        }

        .star {
            position: fixed; /* 使用 fixed 定位，星星不随拖动和缩放移动 */
            background-color: white;
            border-radius: 50%;
            pointer-events: none;
        }

    </style>
</head>
<body>

    <div id="universe">
        <div id="solar-system-container">
            <div id="sun"></div>
            </div>
    </div>

    <div id="controls">
        <button id="zoom-in">+</button>
        <button id="zoom-out">-</button>
        <label>
            <input type="checkbox" id="toggle-orbits" checked> 显示轨道
        </label>
    </div>

    <div id="info-panel">
        <h3 id="info-title"></h3>
        <p id="info-details"></p>
        <button id="close-info-panel">关闭</button>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const solarSystemContainer = document.getElementById('solar-system-container');
            const sunElement = document.getElementById('sun');
            const universe = document.getElementById('universe');

            // UI Controls
            const zoomInButton = document.getElementById('zoom-in');
            const zoomOutButton = document.getElementById('zoom-out');
            const toggleOrbitsCheckbox = document.getElementById('toggle-orbits');
            const infoPanel = document.getElementById('info-panel');
            const infoTitle = document.getElementById('info-title');
            const infoDetails = document.getElementById('info-details');
            const closeInfoPanelButton = document.getElementById('close-info-panel');

            // 缩放和拖动状态变量
            let scale = 0.7; // 初始缩放比例
            let offsetX = universe.clientWidth / 2; // 初始太阳系中心 X
            let offsetY = universe.clientHeight / 2; // 初始太阳系中心 Y
            let isDragging = false;
            let lastMouseX, lastMouseY;

            // 时间和动画速度控制
            let time = 0;
            const baseSpeedMultiplier = 0.0005; // 全局速度调整

            const planetData = [
                { name: "水星", id: "mercury", size: 6, color: "#B0AFA8", orbitRadius: 60, speedFactor: 4.15, info: "太阳系中最小且最靠近太阳的行星。", moons: [] },
                { name: "金星", id: "venus", size: 10, color: "#E6C3A5", orbitRadius: 90, speedFactor: 1.62, info: "以其厚厚的有毒大气层而闻名，是太阳系中最热的行星。", moons: [] },
                { name: "地球", id: "earth", size: 11, color: "#5B99E0", orbitRadius: 130, speedFactor: 1, info: "我们所知的唯一存在生命的行星。", moons: [
                    { name: "月球", id: "moon", size: 3, color: "#D3D3D3", orbitRadius: 18, speedFactor: 12 } // 月球相对于地球的速度
                ]},
                { name: "火星", id: "mars", size: 8, color: "#D87A58", orbitRadius: 180, speedFactor: 0.53, info: "被称为“红色星球”，科学家们对其过去或现在是否存在生命很感兴趣。", moons: [] }, // 火星有很小的卫星，这里为简化省略
                { name: "木星", id: "jupiter", size: 35, color: "#D8BBA1", orbitRadius: 300, speedFactor: 0.084, info: "太阳系中最大的行星，拥有强大的磁场和著名的大红斑。", moons: [
                    { name: "木卫一 (Io)", id: "io", size: 3, color: "#F0E68C", orbitRadius: 30, speedFactor: 7.8 },
                    { name: "木卫二 (Europa)", id: "europa", size: 2.8, color: "#B0C4DE", orbitRadius: 38, speedFactor: 5.5 },
                    // { name: "木卫三 (Ganymede)", id: "ganymede", size: 4, color: "#8B8880", orbitRadius: 48, speedFactor: 3.9 }, // 为简化显示，可选择性添加更多卫星
                    // { name: "木卫四 (Callisto)", id: "callisto", size: 3.8, color: "#708090", orbitRadius: 60, speedFactor: 2.9 }
                ]},
                { name: "土星", id: "saturn", size: 28, color: "#F0E6C1", orbitRadius: 420, speedFactor: 0.034, info: "以其壮观的行星环系统而闻名。", moons: [
                    { name: "土卫六 (Titan)", id: "titan", size: 4, color: "#DAA520", orbitRadius: 35, speedFactor: 6 }
                ], hasRing: true }, // 土星环
                { name: "天王星", id: "uranus", size: 18, color: "#ACE5EE", orbitRadius: 530, speedFactor: 0.012, info: "一颗冰巨星，自转轴几乎完全侧向。", moons: [] },
                { name: "海王星", id: "neptune", size: 17, color: "#5F81E3", orbitRadius: 620, speedFactor: 0.006, info: "最远的气态巨行星，以其深蓝色和强风而著称。", moons: [] }
            ];

            const asteroids = [];
            const numAsteroids = 150;
            const asteroidBeltMinRadius = (planetData[3].orbitRadius + planetData[4].orbitRadius) / 2.5; // 火星和木星轨道之间
            const asteroidBeltMaxRadius = (planetData[3].orbitRadius + planetData[4].orbitRadius) / 1.8;
            const asteroidBeltWidth = asteroidBeltMaxRadius - asteroidBeltMinRadius;


            function createCelestialBody(data, parentElement, isMoon = false, parentPlanetData = null) {
                const element = document.createElement('div');
                element.id = data.id;
                element.className = 'celestial-body';
                element.style.width = `${data.size}px`;
                element.style.height = `${data.size}px`;
                element.style.backgroundColor = data.color;
                element.dataset.info = data.info; // 存储信息供面板使用
                element.dataset.name = data.name;

                element.addEventListener('click', (e) => {
                    e.stopPropagation(); // 防止点击行星时触发宇宙的拖动
                    infoTitle.textContent = data.name;
                    infoDetails.textContent = data.info;
                    infoPanel.style.display = 'block';
                });
                parentElement.appendChild(element);
                data.element = element; // 存储DOM元素的引用

                if (!isMoon) { // 行星才有轨道线 (相对于太阳)
                    const orbit = document.createElement('div');
                    orbit.className = 'orbit-path';
                    orbit.style.width = `${data.orbitRadius * 2}px`;
                    orbit.style.height = `${data.orbitRadius * 2}px`;
                    // 轨道位于太阳中心
                    orbit.style.left = `calc(50% - ${data.orbitRadius}px)`;
                    orbit.style.top = `calc(50% - ${data.orbitRadius}px)`;
                    solarSystemContainer.insertBefore(orbit, sunElement.nextSibling); // 在太阳之后，行星之前插入
                    data.orbitElement = orbit;
                }

                if (data.hasRing && data.id === 'saturn') {
                    const ring = document.createElement('div');
                    ring.style.width = `${data.size * 2.2}px`;
                    ring.style.height = `${data.size * 2.2}px`;
                    ring.style.border = `${data.size * 0.2}px solid rgba(200, 180, 150, 0.5)`;
                    ring.style.borderRadius = '50%';
                    ring.style.position = 'absolute';
                    ring.style.boxSizing = 'border-box'; // 边框包含在宽高内
                    // 环的定位使其围绕行星中心
                    ring.style.left = `-${data.size * 0.6}px`; // (ringWidth - planetWidth)/2
                    ring.style.top = `-${data.size * 0.6}px`;
                    ring.style.pointerEvents = 'none'; // 环不响应点击
                    element.appendChild(ring); // 将环作为土星元素的子元素
                    data.ringElement = ring;
                }


                if (data.moons) {
                    data.moons.forEach(moonData => {
                        createCelestialBody(moonData, element, true, data); // 月球的父元素是行星
                    });
                }
            }

            function initSolarSystem() {
                // 创建背景星星
                for (let i = 0; i < 300; i++) {
                    const star = document.createElement('div');
                    star.className = 'star';
                    const size = Math.random() * 2 + 0.5;
                    star.style.width = `${size}px`;
                    star.style.height = `${size}px`;
                    star.style.left = `${Math.random() * 100}%`;
                    star.style.top = `${Math.random() * 100}%`;
                    star.style.opacity = Math.random() * 0.5 + 0.2;
                    universe.insertBefore(star, solarSystemContainer); // 星星在最底层
                }

                planetData.forEach(pData => createCelestialBody(pData, solarSystemContainer));

                // 创建小行星带
                for (let i = 0; i < numAsteroids; i++) {
                    const asteroid = document.createElement('div');
                    asteroid.className = 'asteroid';
                    const size = Math.random() * 2 + 1;
                    asteroid.style.width = `${size}px`;
                    asteroid.style.height = `${size}px`;
                    solarSystemContainer.appendChild(asteroid);

                    const angle = Math.random() * Math.PI * 2;
                    const radius = asteroidBeltMinRadius + Math.random() * asteroidBeltWidth;
                    const speedFactor = (Math.random() * 0.2 + 0.05) * (planetData[3].speedFactor + planetData[4].speedFactor) / 2; // 速度介于火星木星之间

                    asteroids.push({
                        element: asteroid,
                        angle: angle,
                        orbitRadius: radius,
                        speedFactor: speedFactor,
                        size: size
                    });
                }
                updateSolarSystemTransform(); // 应用初始变换
                animate();
            }

            function updateSolarSystemTransform() {
                solarSystemContainer.style.transform = `translate(${offsetX}px, ${offsetY}px) scale(${scale})`;
            }

            function animate() {
                time += 1; // 简单的时间递增

                // 更新行星位置
                planetData.forEach(pData => {
                    const angle = time * baseSpeedMultiplier * pData.speedFactor;
                    const x = Math.cos(angle) * pData.orbitRadius;
                    const y = Math.sin(angle) * pData.orbitRadius;
                    // 行星的位置是相对于太阳系容器的中心 (50%, 50%)
                    pData.element.style.left = `calc(50% + ${x}px)`;
                    pData.element.style.top = `calc(50% + ${y}px)`;

                    // 更新卫星位置 (相对于其行星)
                    if (pData.moons) {
                        pData.moons.forEach(moonData => {
                            const moonAngle = time * baseSpeedMultiplier * moonData.speedFactor * 5; // 卫星通常更快
                            const moonX = Math.cos(moonAngle) * moonData.orbitRadius;
                            const moonY = Math.sin(moonAngle) * moonData.orbitRadius;
                            // 卫星的位置是相对于其父行星的中心
                            // 父行星的 transform(-50%,-50%) 已经处理了中心对齐，所以这里直接设置 left/top
                            moonData.element.style.left = `${moonX}px`;
                            moonData.element.style.top = `${moonY}px`;
                        });
                    }
                });

                // 更新小行星位置
                asteroids.forEach(asteroid => {
                    const angle = asteroid.angle + time * baseSpeedMultiplier * asteroid.speedFactor;
                    const x = Math.cos(angle) * asteroid.orbitRadius;
                    const y = Math.sin(angle) * asteroid.orbitRadius;
                    asteroid.element.style.left = `calc(50% + ${x - asteroid.size/2}px)`; //相对于太阳系容器中心
                    asteroid.element.style.top = `calc(50% + ${y - asteroid.size/2}px)`;
                });


                requestAnimationFrame(animate);
            }

            // 事件监听器
            zoomInButton.addEventListener('click', () => {
                scale *= 1.2;
                updateSolarSystemTransform();
            });

            zoomOutButton.addEventListener('click', () => {
                scale /= 1.2;
                updateSolarSystemTransform();
            });

            toggleOrbitsCheckbox.addEventListener('change', () => {
                const displayValue = toggleOrbitsCheckbox.checked ? 'block' : 'none';
                document.querySelectorAll('.orbit-path').forEach(orbit => {
                    orbit.style.display = displayValue;
                });
            });

            closeInfoPanelButton.addEventListener('click', () => {
                infoPanel.style.display = 'none';
            });

            // 拖动逻辑
            universe.addEventListener('mousedown', (e) => {
                if (e.target === universe || e.target === solarSystemContainer || e.target.classList.contains('orbit-path') || e.target.id === 'sun') { // 允许在容器或太阳上开始拖动
                    isDragging = true;
                    universe.style.cursor = 'grabbing';
                    lastMouseX = e.clientX;
                    lastMouseY = e.clientY;
                    e.preventDefault(); // 防止文本选择等默认行为
                }
            });

            universe.addEventListener('mousemove', (e) => {
                if (isDragging) {
                    const dx = e.clientX - lastMouseX;
                    const dy = e.clientY - lastMouseY;
                    offsetX += dx;
                    offsetY += dy;
                    lastMouseX = e.clientX;
                    lastMouseY = e.clientY;
                    updateSolarSystemTransform();
                }
            });

            universe.addEventListener('mouseup', () => {
                if (isDragging) {
                    isDragging = false;
                    universe.style.cursor = 'grab';
                }
            });

            universe.addEventListener('mouseleave', () => { // 如果鼠标移出窗口则停止拖动
                if (isDragging) {
                    isDragging = false;
                    universe.style.cursor = 'grab';
                }
            });

            // 滚轮缩放
            universe.addEventListener('wheel', (e) => {
                e.preventDefault(); // 防止页面滚动
                const zoomIntensity = 0.1;
                const delta = e.deltaY > 0 ? -1 : 1; // 向上滚轮放大，向下缩小
                const oldScale = scale;

                scale += delta * zoomIntensity * scale; // 按比例缩放，体验更平滑
                scale = Math.max(0.1, Math.min(scale, 10)); // 限制缩放范围

                // 计算鼠标指针在 solarSystemContainer 内的相对位置
                // offsetX 和 offsetY 是 solarSystemContainer 的左上角在 universe 中的位置
                // e.clientX 和 e.clientY 是鼠标在视口中的位置
                const mouseX = e.clientX - offsetX;
                const mouseY = e.clientY - offsetY;

                // 更新 offsetX 和 offsetY，使得鼠标指针下的点在缩放后保持在原位
                offsetX -= mouseX * (scale / oldScale - 1);
                offsetY -= mouseY * (scale / oldScale - 1);

                updateSolarSystemTransform();
            });


            // 窗口大小调整时重新计算中心
            window.addEventListener('resize', () => {
                // 如果没有拖动过，保持在中心
                if (offsetX === universe.clientWidth / 2 && offsetY === universe.clientHeight / 2) {
                   offsetX = universe.clientWidth / 2;
                   offsetY = universe.clientHeight / 2;
                }
                updateSolarSystemTransform();
            });


            initSolarSystem();
        });
    </script>
</body>
</html>