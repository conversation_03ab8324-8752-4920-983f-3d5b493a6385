import pygame
import math
import numpy as np

# --- Pygame Setup ---
pygame.init()
WIDTH, HEIGHT = 800, 600
SCREEN = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption("Bouncing Ball in Rotating Dodecagon")
CLOCK = pygame.time.Clock()
FPS = 60
FONT = pygame.font.SysFont("Arial", 18)

# --- Colors ---
GREEN = (0, 200, 0)
DARK_GREEN = (0, 100, 0)
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
GRAY = (150, 150, 150)

# --- Physics Parameters ---
GRAVITY_ACCELERATION = np.array([0, 250.0])  # pixels/s^2
BALL_RADIUS = 10
BALL_MASS = 1.0
BALL_COLOR = GREEN
BALL_ELASTICITY = 0.7  # Coefficient of restitution (0 to 1)
# This friction_coeff determines how much the wall's tangential velocity influences the ball's
BALL_WALL_FRICTION_COEFF = 0.2  # 0: no tangential effect, 1: ball matches wall tang. speed quickly

DODECAGON_CENTER = np.array([WIDTH / 2, HEIGHT / 2 - 50])
DODECAGON_OUTER_RADIUS = 200
DODECAGON_SIDES = 12
DODECAGON_ROT_SPEED = math.radians(20)  # radians per second (positive for clockwise)
DODECAGON_COLOR = DARK_GREEN


# --- Helper Functions ---
def get_regular_polygon_vertices(center_x, center_y, radius, n_sides, angle_offset=0):
    vertices = []
    for i in range(n_sides):
        angle = angle_offset + (2 * math.pi * i) / n_sides
        x = center_x + radius * math.cos(angle)
        y = center_y + radius * math.sin(angle)
        vertices.append(np.array([x, y]))
    return vertices


# --- Classes ---
class Dodecagon:
    def __init__(self, center, outer_radius, sides, color, angular_velocity):
        self.center = np.array(center, dtype=float)
        self.outer_radius = outer_radius
        self.sides = sides
        self.color = color
        self.angle = 0.0  # Current rotation angle in radians
        self.angular_velocity = angular_velocity  # rad/s
        self.vertices = []
        self._update_vertices()

    def _update_vertices(self):
        self.vertices = get_regular_polygon_vertices(
            self.center[0], self.center[1],
            self.outer_radius, self.sides, self.angle
        )

    def rotate(self, dt):
        self.angle += self.angular_velocity * dt
        self._update_vertices()

    def draw(self, surface):
        if len(self.vertices) >= 2:
            pygame.draw.polygon(surface, self.color, [tuple(v) for v in self.vertices], 5)  # 5 is line thickness

    def get_edge_velocity_at_point(self, point_on_edge):
        """Calculates the linear velocity of a point on the edge due to rotation."""
        if self.angular_velocity == 0:
            return np.array([0.0, 0.0])

        # Vector from center of dodecagon to the point on edge
        r_vec = point_on_edge - self.center

        # Linear velocity v = omega x r (cross product)
        # In 2D, if omega is scalar (rotation around Z-axis) and r = (rx, ry):
        # v = (-omega * ry, omega * rx)
        # Note: Pygame's y-axis is inverted, but angular velocity sign convention handles this.
        # If angular_velocity is positive for clockwise:
        # A positive omega means clockwise. For r=(x,y), v = (omega*y, -omega*x) for clockwise
        # For CCW, v = (-omega*y, omega*x)
        # Our omega positive is clockwise, so:
        vx = self.angular_velocity * r_vec[1]
        vy = -self.angular_velocity * r_vec[0]
        return np.array([vx, vy])


class Ball:
    def __init__(self, x, y, radius, mass, color, elasticity, wall_friction_coeff):
        self.pos = np.array([float(x), float(y)])
        self.vel = np.array([20.0, -50.0])  # Initial velocity for some action
        self.acc = np.array([0.0, 0.0])
        self.radius = radius
        self.mass = mass
        self.color = color
        self.elasticity = elasticity
        self.wall_friction_coeff = wall_friction_coeff

    def apply_force(self, force):
        self.acc += force / self.mass

    def update(self, dt, dodecagon):
        # Apply gravity
        self.apply_force(GRAVITY_ACCELERATION * self.mass)  # F = m*a

        self.vel += self.acc * dt
        self.pos += self.vel * dt
        self.acc = np.array([0.0, 0.0])  # Reset acceleration

        # Collision with dodecagon walls
        self._handle_dodecagon_collision(dodecagon)

    def _handle_dodecagon_collision(self, dodecagon):
        num_vertices = len(dodecagon.vertices)
        for i in range(num_vertices):
            p1 = dodecagon.vertices[i]
            p2 = dodecagon.vertices[(i + 1) % num_vertices]  # Next vertex, wraps around

            # --- Collision Detection (Point-Segment Distance) ---
            line_vec = p2 - p1
            point_vec = self.pos - p1
            line_len_sq = np.dot(line_vec, line_vec)

            if line_len_sq == 0:  # Edge is a point, should not happen
                continue

            t = np.dot(point_vec, line_vec) / line_len_sq

            if t < 0.0:
                closest_point_on_edge = p1  # Closest to p1
            elif t > 1.0:
                closest_point_on_edge = p2  # Closest to p2
            else:
                closest_point_on_edge = p1 + t * line_vec  # Closest point is on the segment

            dist_vec_ball_to_edge_point = self.pos - closest_point_on_edge
            distance_ball_to_edge = np.linalg.norm(dist_vec_ball_to_edge_point)

            penetration = self.radius - distance_ball_to_edge

            if penetration > -0.1:  # Collision or very close (small tolerance)
                # --- Collision Resolution ---
                # 1. Normal vector (from edge point to ball center)
                if distance_ball_to_edge < 1e-6:  # Ball center is (almost) on edge/vertex
                    # If on vertex, normal is from vertex to ball. If on edge, it's perpendicular.
                    # A robust way for segment: normal is (line_vec.y, -line_vec.x) or (-line_vec.y, line_vec.x)
                    # pointing "out" of the polygon. For CCW vertices, (dy, -dx) points out.
                    # Our polygon vertices are CCW if rot_speed is negative, CW if positive.
                    # For simplicity, use dist_vec_ball_to_edge_point if it's valid
                    # otherwise, compute from edge vector
                    if np.linalg.norm(dist_vec_ball_to_edge_point) > 1e-6:
                        collision_normal = dist_vec_ball_to_edge_point / distance_ball_to_edge
                    else:  # Ball is exactly on the edge point, need to derive normal from edge
                        edge_normal_unnormalized = np.array([line_vec[1], -line_vec[0]])
                        # Ensure normal points towards the ball (which is "outside" the edge segment from ball's perspective)
                        # Or, ensure normal points "out" of the polygon.
                        # For a convex polygon with CCW vertices, (dy, -dx) points "out".
                        # If dodecagon vertices are CCW, this points out of polygon.
                        # The ball is inside, so we want the normal pointing towards the ball.
                        # Test: dot product of (ball_center - edge_midpoint) and edge_normal_unnormalized
                        # If positive, normal is good. If negative, flip.
                        # For dodecagon, vertices are generally CCW if angle increases.
                        # Here, we want normal from edge point TO ball center
                        # This can be tricky with vertex collisions.
                        # A simpler way: dist_vec is from closest_point_on_edge to ball_pos
                        # So, collision_normal is dist_vec normalized.
                        # If ball is AT closest_point_on_edge, this fails.
                        # Fallback for exact overlap:
                        # Normal from dodecagon center towards edge midpoint
                        mid_edge = (p1 + p2) / 2
                        temp_normal = mid_edge - dodecagon.center
                        collision_normal = temp_normal / (np.linalg.norm(temp_normal) + 1e-6)
                        # Then ensure it's pointing from edge towards ball.
                        # If dot((self.pos - mid_edge), collision_normal) < 0, flip it.
                        # This is becoming complex for exact vertex hits.
                        # The dist_vec_ball_to_edge_point / distance_ball_to_edge should generally work.

                else:
                    collision_normal = dist_vec_ball_to_edge_point / distance_ball_to_edge

                # Check if ball is outside the polygon by testing against normal
                # For a convex polygon, if ball is inside, (ball.pos - p1) dot edge_normal_out should be negative
                # edge_normal_out = np.array([line_vec[1], -line_vec[0]]) # Assuming CCW polygon
                # if np.dot(self.pos - p1, edge_normal_out) > 0: # Ball is outside this edge's half-plane
                #     continue # Not a collision with the *inside* of the polygon

                # 2. Resolve penetration (move ball out)
                # self.pos += collision_normal * penetration # This might be too aggressive
                # A slightly better way to avoid overcorrection if multiple edges are hit:
                # Accumulate corrections or handle one dominant collision.
                # For now, simple correction:
                self.pos = closest_point_on_edge + collision_normal * self.radius

                # 3. Calculate relative velocity
                velocity_of_edge_at_contact = dodecagon.get_edge_velocity_at_point(closest_point_on_edge)
                relative_velocity = self.vel - velocity_of_edge_at_contact

                velocity_along_normal = np.dot(relative_velocity, collision_normal)

                # 4. If moving towards the wall, apply impulse
                if velocity_along_normal < 0:
                    # --- Normal Impulse (Elasticity) ---
                    # j = -(1 + e) * m * v_normal_rel
                    # Here, mass of wall is infinite, so formula simplifies for ball
                    impulse_scalar_normal = -(1 + self.elasticity) * velocity_along_normal
                    # Apply impulse to ball's velocity (since mass is already considered if we use impulse_scalar / mass)
                    # Or, directly: change_in_vel_normal = -(1+e) * vel_along_normal
                    self.vel += impulse_scalar_normal * collision_normal

                    # --- Tangential Impulse (Friction/Drag from rotating wall) ---
                    # Recalculate relative velocity after normal impulse for friction calc
                    # Not strictly necessary if friction impulse is perpendicular to normal impulse
                    # current_relative_velocity_after_normal_bounce = self.vel - velocity_of_edge_at_contact

                    tangent_direction = np.array([-collision_normal[1], collision_normal[0]])
                    # Ensure tangent direction aligns with edge p1->p2 (or consistent direction)
                    if np.dot(tangent_direction, line_vec) < 0:
                        tangent_direction = -tangent_direction

                    relative_velocity_tangential_scalar = np.dot(relative_velocity, tangent_direction)

                    # We want to reduce the relative_velocity_tangential_scalar
                    # delta_v_tangential = -relative_velocity_tangential_scalar * friction_coeff
                    # This is a simplified model: change ball's tangential velocity by a fraction
                    # of the difference between its current tangential velocity and the edge's.

                    # Ball's tangential velocity component relative to the world
                    ball_vel_tangent_comp = np.dot(self.vel, tangent_direction) * tangent_direction
                    # Edge's tangential velocity component relative to the world
                    edge_vel_tangent_comp = np.dot(velocity_of_edge_at_contact, tangent_direction) * tangent_direction

                    # The change needed to make ball's tangential velocity closer to edge's
                    vel_diff_tangential = edge_vel_tangent_comp - ball_vel_tangent_comp

                    # Apply a fraction of this difference
                    self.vel += vel_diff_tangential * self.wall_friction_coeff

    def draw(self, surface):
        pygame.draw.circle(surface, self.color, (int(self.pos[0]), int(self.pos[1])), self.radius)
        # Draw velocity vector (for debugging)
        # end_vel_line = self.pos + self.vel * 0.1
        # pygame.draw.line(surface, WHITE, tuple(self.pos.astype(int)), tuple(end_vel_line.astype(int)), 1)


# --- Game Objects ---
dodecagon = Dodecagon(DODECAGON_CENTER, DODECAGON_OUTER_RADIUS, DODECAGON_SIDES, DODECAGON_COLOR, DODECAGON_ROT_SPEED)
ball = Ball(DODECAGON_CENTER[0], DODECAGON_CENTER[1] - DODECAGON_OUTER_RADIUS / 2,
            BALL_RADIUS, BALL_MASS, BALL_COLOR, BALL_ELASTICITY, BALL_WALL_FRICTION_COEFF)

# --- Main Loop ---
running = True
while running:
    dt = CLOCK.tick(FPS) / 1000.0  # Delta time in seconds (limit dt to avoid instability)
    dt = min(dt, 1 / 30)  # Max dt to 1/30th of a second

    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False
        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_SPACE:  # Reset ball to center
                ball.pos = np.array([DODECAGON_CENTER[0], DODECAGON_CENTER[1] - DODECAGON_OUTER_RADIUS / 2],
                                    dtype=float)
                ball.vel = np.array([20.0, -50.0], dtype=float)

    # --- Physics Update ---
    dodecagon.rotate(dt)
    ball.update(dt, dodecagon)

    # --- Drawing ---
    SCREEN.fill(BLACK)
    dodecagon.draw(SCREEN)
    ball.draw(SCREEN)

    # Display Info
    info_text_ball_vel = FONT.render(f"Ball Vel: ({ball.vel[0]:.1f}, {ball.vel[1]:.1f}) px/s", True, WHITE)
    SCREEN.blit(info_text_ball_vel, (10, 10))
    info_text_dt = FONT.render(f"dt: {dt * 1000:.2f} ms, FPS: {CLOCK.get_fps():.1f}", True, WHITE)
    SCREEN.blit(info_text_dt, (10, 30))
    info_text_rot = FONT.render(f"Poly Angle: {math.degrees(dodecagon.angle):.1f} deg", True, WHITE)
    SCREEN.blit(info_text_rot, (10, 50))

    pygame.display.flip()

pygame.quit()