import pygame
import math
import numpy as np
from collections import deque

# --- Pygame Setup ---
pygame.init()
WIDTH, HEIGHT = 800, 700
SCREEN = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption("Rotating Hexagons Physics")
CLOCK = pygame.time.Clock()
FPS = 60
FONT = pygame.font.SysFont("Arial", 18)

# --- Colors ---
BLUE = (0, 0, 255)
GREEN = (0, 255, 0)
RED = (255, 0, 0)
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
PURPLE = (128, 0, 128, 150)  # RGBA for overlap, A for alpha
LIGHT_GRAY = (200, 200, 200)
TRAIL_COLOR = (255, 100, 100)

# --- Physics Parameters ---
GRAVITY = np.array([0, 180.0])  # pixels/s^2
BALL_RADIUS = 8
BALL_MASS = 1.0
BALL_ELASTICITY = 0.75  # Coefficient of restitution
HEX_ROT_SPEED = math.radians(30)  # radians per second
HEX_SIZE = 150  # Outer radius of hexagon
FRICTION_COEFFICIENT = 0.3  # How much tangential velocity is transferred


# --- Helper Functions ---
def get_regular_polygon_vertices(center_x, center_y, radius, n_sides, angle_offset=0):
    """Calculates vertices of a regular polygon."""
    vertices = []
    for i in range(n_sides):
        angle = angle_offset + (2 * math.pi * i) / n_sides
        x = center_x + radius * math.cos(angle)
        y = center_y + radius * math.sin(angle)
        vertices.append(np.array([x, y]))
    return vertices


def is_inside(p, edge_start, edge_end):
    """Checks if point p is to the 'inside' of an edge (edge_start -> edge_end).
       Assumes counter-clockwise polygon vertices for 'inside' to be to the left."""
    return (edge_end[0] - edge_start[0]) * (p[1] - edge_start[1]) - \
        (edge_end[1] - edge_start[1]) * (p[0] - edge_start[0]) > 0  # For CCW, >0 is inside


def get_intersection_point(p1, p2, p3, p4):
    """Finds intersection of line segments p1p2 and p3p4."""
    # Using line-line intersection formula from Wikipedia
    den = (p1[0] - p2[0]) * (p3[1] - p4[1]) - (p1[1] - p2[1]) * (p3[0] - p4[0])
    if den == 0:
        return None  # Parallel or collinear
    t_num = (p1[0] - p3[0]) * (p3[1] - p4[1]) - (p1[1] - p3[1]) * (p3[0] - p4[0])
    u_num = -((p1[0] - p2[0]) * (p1[1] - p3[1]) - (p1[1] - p2[1]) * (p1[0] - p3[0]))
    t = t_num / den
    u = u_num / den
    if 0 <= t <= 1 and 0 <= u <= 1:  # Intersection lies on both segments
        return np.array([p1[0] + t * (p2[0] - p1[0]), p1[1] + t * (p2[1] - p1[1])])
    return None


def sutherland_hodgman_clip(subject_polygon, clip_polygon):
    """Clips a subject polygon against a convex clip polygon (Sutherland-Hodgman)."""
    output_list = list(subject_polygon)
    cp1 = clip_polygon[-1]  # Start with the last vertex of clip polygon

    for cp2 in clip_polygon:  # For each edge of the clip polygon (cp1, cp2)
        input_list = list(output_list)
        output_list.clear()
        if not input_list: break  # No subject polygon left

        s = input_list[-1]  # Start with the last vertex of current subject polygon

        for e in input_list:  # For each edge of the subject polygon (s, e)
            s_inside = is_inside(s, cp1, cp2)
            e_inside = is_inside(e, cp1, cp2)

            if s_inside and e_inside:  # Case 1: Both points inside
                output_list.append(e)
            elif s_inside and not e_inside:  # Case 2: S_start inside, S_end outside
                intersection = get_intersection_point(s, e, cp1, cp2)
                if intersection is not None:
                    output_list.append(intersection)
            elif not s_inside and e_inside:  # Case 3: S_start outside, S_end inside
                intersection = get_intersection_point(s, e, cp1, cp2)
                if intersection is not None:
                    output_list.append(intersection)
                output_list.append(e)
            # Case 4: Both points outside - do nothing
            s = e
        cp1 = cp2
    return output_list


# --- Classes ---
class Hexagon:
    def __init__(self, center_x, center_y, size, color, angular_velocity):
        self.center = np.array([float(center_x), float(center_y)])
        self.size = size  # Outer radius
        self.color = color
        self.angle = 0.0
        self.angular_velocity = angular_velocity  # rad/s
        self.vertices = []
        self.update_vertices()

    def update_vertices(self):
        self.vertices = get_regular_polygon_vertices(self.center[0], self.center[1], self.size, 6, self.angle)

    def rotate(self, dt):
        self.angle += self.angular_velocity * dt
        self.update_vertices()

    def draw(self, surface):
        if len(self.vertices) >= 2:
            pygame.draw.polygon(surface, self.color, [tuple(v) for v in self.vertices], 3)

    def get_edge_velocity_at_point(self, point_on_edge):
        """Calculates the linear velocity of a point on the edge of the rotating hexagon."""
        r_vec = point_on_edge - self.center
        # omega_vec = np.array([0, 0, self.angular_velocity]) # Assuming 2D rotation around Z
        # r_vec_3d = np.array([r_vec[0], r_vec[1], 0])
        # vel_3d = np.cross(omega_vec, r_vec_3d)
        # return np.array([vel_3d[0], vel_3d[1]])
        # Simplified for 2D: v = omega * r (perpendicular to r)
        radius = np.linalg.norm(r_vec)
        if radius == 0: return np.array([0.0, 0.0])

        # Velocity is omega * r, perpendicular to r_vec
        # If r_vec = (x,y), perpendicular is (-y,x) or (y,-x)
        # Direction depends on angular_velocity sign
        perp_vec = np.array([-r_vec[1], r_vec[0]])
        return perp_vec / radius * self.angular_velocity * radius  # Simplified: perp_vec * self.angular_velocity


class Ball:
    def __init__(self, x, y, radius, mass, color, elasticity):
        self.pos = np.array([float(x), float(y)])
        self.vel = np.array([0.0, 0.0])  # Initial velocity
        self.acc = np.array([0.0, 0.0])
        self.radius = radius
        self.mass = mass
        self.color = color
        self.elasticity = elasticity
        self.trail = deque(maxlen=30)  # Store last 30 positions for trail

    def apply_force(self, force):
        self.acc += force / self.mass

    def update(self, dt):
        self.vel += self.acc * dt
        self.pos += self.vel * dt
        self.acc = np.array([0.0, 0.0])  # Reset acceleration for next frame
        self.trail.append(tuple(self.pos.astype(int)))

    def draw(self, surface):
        # Draw trail
        for i, pos in enumerate(self.trail):
            alpha = int(255 * (i / len(self.trail)))
            if alpha > 0:
                trail_surf = pygame.Surface((self.radius * 2, self.radius * 2), pygame.SRCALPHA)
                current_trail_color = (*TRAIL_COLOR, alpha)
                pygame.draw.circle(trail_surf, current_trail_color, (self.radius, self.radius),
                                   self.radius * (i / len(self.trail)) * 0.8)
                surface.blit(trail_surf, (pos[0] - self.radius, pos[1] - self.radius))

        # Draw ball
        pygame.draw.circle(surface, self.color, (int(self.pos[0]), int(self.pos[1])), self.radius)

    def handle_collision_with_edge(self, edge_start, edge_end, edge_owner_hexagon):
        # Calculate closest point on line segment edge_start-edge_end to ball_pos
        line_vec = edge_end - edge_start
        point_vec = self.pos - edge_start
        line_len_sq = np.dot(line_vec, line_vec)

        if line_len_sq == 0:  # Edge is a point, should not happen for a polygon
            return

        t = np.dot(point_vec, line_vec) / line_len_sq

        if t < 0.0:
            closest_point = edge_start
        elif t > 1.0:
            closest_point = edge_end
        else:
            closest_point = edge_start + t * line_vec

        dist_vec = self.pos - closest_point
        distance = np.linalg.norm(dist_vec)
        penetration = self.radius - distance

        if penetration > -0.5:  # Collision or very close (add small tolerance)
            if distance == 0:  # Avoid division by zero if ball center is on edge
                # Try to find a normal; if t is 0 or 1, it's a vertex collision
                if t <= 0.001 or t >= 0.999:  # Vertex collision
                    normal = dist_vec / (np.linalg.norm(dist_vec) + 1e-6)  # Normal from vertex to ball
                else:  # Edge collision, normal is perpendicular to edge
                    normal = np.array([line_vec[1], -line_vec[0]])
                    normal = normal / (np.linalg.norm(normal) + 1e-6)
                    # Ensure normal points outwards from polygon (assuming CCW vertices for intersection)
                    # A simple check: if ball is "more inside" than edge center, flip normal
                    edge_center = (edge_start + edge_end) / 2
                    if np.dot(self.pos - edge_center, normal) > 0:  # Normal is already pointing towards ball
                        pass
                    else:  # Normal is pointing away from ball (polygon's internal normal)
                        normal = -normal  # We need normal pointing from edge to ball
            else:
                normal = dist_vec / distance

            # Resolve penetration
            self.pos += normal * penetration

            # Calculate relative velocity
            edge_velocity_at_contact = edge_owner_hexagon.get_edge_velocity_at_point(closest_point)

            v_rel = self.vel - edge_velocity_at_contact
            v_rel_normal_comp = np.dot(v_rel, normal)

            if v_rel_normal_comp < 0:  # Ball is moving towards the edge
                # --- Normal collision response (elastic) ---
                impulse_j = -(1 + self.elasticity) * self.mass * v_rel_normal_comp
                # For dynamic edge, effective mass of edge is ~infinity
                # So, impulse_j = -(1 + elasticity) * v_rel_normal_comp / (1/ball_mass)
                self.vel += (impulse_j / self.mass) * normal

                # --- Tangential friction / Angular momentum effect ---
                # After normal collision, calculate new relative tangential velocity
                # Tangent direction
                tangent_dir = np.array([-normal[1], normal[0]])
                if np.dot(tangent_dir, line_vec) < 0:  # Ensure tangent aligns with edge direction
                    tangent_dir = -tangent_dir

                v_ball_tangential_comp_vec = self.vel - np.dot(self.vel, normal) * normal
                v_edge_tangential_comp_vec = edge_velocity_at_contact - np.dot(edge_velocity_at_contact,
                                                                               normal) * normal

                # Relative tangential velocity vector
                v_rel_tangential_vec = v_ball_tangential_comp_vec - v_edge_tangential_comp_vec

                # Calculate friction impulse (simplified: try to match a fraction of edge tangential speed)
                # We want to reduce v_rel_tangential_vec
                # Impulse is in direction opposite to v_rel_tangential_vec if ball is faster,
                # or in same direction as edge tangential if ball is slower.

                # Simplified: transfer a portion of the edge's tangential velocity
                # This is a "sticky friction" or "rolling friction" type effect
                desired_vel_change_tangential = (
                                                            v_edge_tangential_comp_vec - v_ball_tangential_comp_vec) * FRICTION_COEFFICIENT
                self.vel += desired_vel_change_tangential


# --- Game Objects ---
hex_left_center_x = WIDTH / 2 - HEX_SIZE / 1.8
hex_right_center_x = WIDTH / 2 + HEX_SIZE / 1.8
hex_y = HEIGHT / 2.2

hexagon_left = Hexagon(hex_left_center_x, hex_y, HEX_SIZE, BLUE, -HEX_ROT_SPEED)  # Counter-clockwise
hexagon_right = Hexagon(hex_right_center_x, hex_y, HEX_SIZE, GREEN, HEX_ROT_SPEED)  # Clockwise

# Initial ball position (needs to be in overlap)
# Calculate initial overlap to place ball
temp_overlap = sutherland_hodgman_clip(hexagon_left.vertices, hexagon_right.vertices)
if temp_overlap:
    overlap_centroid = np.mean(np.array(temp_overlap), axis=0)
    ball_start_x, ball_start_y = overlap_centroid[0], overlap_centroid[1]
else:  # Fallback if initial overlap calculation fails (should not happen with these params)
    ball_start_x, ball_start_y = WIDTH / 2, HEIGHT / 2

ball = Ball(ball_start_x, ball_start_y, BALL_RADIUS, BALL_MASS, RED, BALL_ELASTICITY)

# --- Main Loop ---
running = True
while running:
    dt = CLOCK.tick(FPS) / 1000.0  # Delta time in seconds

    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False

    # --- Physics Update ---
    ball.apply_force(GRAVITY)  # Apply gravity every frame before velocity update

    hexagon_left.rotate(dt)
    hexagon_right.rotate(dt)

    # Calculate intersection of the two hexagons
    # The order matters for clipping if one is "subject" and other is "clipper"
    # To get the true intersection, clip H1 by H2, then clip H2 by H1, and take the smaller one,
    # or, more robustly, clip H1 by H2, then clip the result by H1 (or vice versa).
    # For two convex polygons, clipping one by the other is sufficient.
    intersection_vertices = sutherland_hodgman_clip(hexagon_left.vertices, hexagon_right.vertices)

    # If an intersection exists, perform collision detection with its edges
    if len(intersection_vertices) >= 3:
        num_intersect_edges = len(intersection_vertices)
        for i in range(num_intersect_edges):
            edge_start = intersection_vertices[i]
            edge_end = intersection_vertices[(i + 1) % num_intersect_edges]

            # Determine which original hexagon this edge belongs to for its angular velocity
            # This is tricky. A simpler heuristic: check which hexagon center is "behind" the edge.
            # Or, check if edge mid-point is very close to an edge of an original hexagon.
            # For now, let's average the effect or pick one based on proximity.
            # A more robust way: tag edges during clipping.
            # Simplified: check which hexagon's original edge is *collinear* and *overlapping* with this intersection edge.

            edge_owner = None
            # Check if edge is from hexagon_left
            for j in range(6):
                h1_v1 = hexagon_left.vertices[j]
                h1_v2 = hexagon_left.vertices[(j + 1) % 6]
                # Check for collinearity and overlap (simplified: check if midpoints are close and vectors aligned)
                if np.linalg.norm((edge_start + edge_end) / 2 - (h1_v1 + h1_v2) / 2) < 1.0:  # Midpoints close
                    vec_edge = edge_end - edge_start
                    vec_h1_edge = h1_v2 - h1_v1
                    if abs(np.dot(vec_edge / (np.linalg.norm(vec_edge) + 1e-6),
                                  vec_h1_edge / (np.linalg.norm(vec_h1_edge) + 1e-6))) > 0.99:
                        edge_owner = hexagon_left
                        break
            if not edge_owner:
                for j in range(6):
                    h2_v1 = hexagon_right.vertices[j]
                    h2_v2 = hexagon_right.vertices[(j + 1) % 6]
                    if np.linalg.norm((edge_start + edge_end) / 2 - (h2_v1 + h2_v2) / 2) < 1.0:
                        vec_edge = edge_end - edge_start
                        vec_h2_edge = h2_v2 - h2_v1
                        if abs(np.dot(vec_edge / (np.linalg.norm(vec_edge) + 1e-6),
                                      vec_h2_edge / (np.linalg.norm(vec_h2_edge) + 1e-6))) > 0.99:
                            edge_owner = hexagon_right
                            break

            # If owner cannot be determined (e.g. intersection points forming new edges),
            # we might need a more complex way or use an average angular velocity.
            # For now, if no clear owner, assume a static edge (or skip tangential effect).
            if edge_owner:
                ball.handle_collision_with_edge(edge_start, edge_end, edge_owner)
            else:  # If edge is newly formed by intersection points, treat as static for simplicity
                # Create a dummy static hexagon for edge velocity calculation
                dummy_static_hex = Hexagon(0, 0, 1, BLACK, 0)
                ball.handle_collision_with_edge(edge_start, edge_end, dummy_static_hex)

    ball.update(dt)

    # --- Drawing ---
    SCREEN.fill(BLACK)

    # Draw intersection area (filled)
    if len(intersection_vertices) >= 3:
        # Create a surface for the overlap with alpha
        overlap_surface = pygame.Surface((WIDTH, HEIGHT), pygame.SRCALPHA)
        pygame.draw.polygon(overlap_surface, PURPLE, [tuple(v) for v in intersection_vertices])
        SCREEN.blit(overlap_surface, (0, 0))

    hexagon_left.draw(SCREEN)
    hexagon_right.draw(SCREEN)
    ball.draw(SCREEN)

    # Display Info
    info_text_ball_vel = FONT.render(f"Ball Vel: ({ball.vel[0]:.1f}, {ball.vel[1]:.1f}) px/s", True, WHITE)
    info_text_ball_pos = FONT.render(f"Ball Pos: ({ball.pos[0]:.1f}, {ball.pos[1]:.1f})", True, WHITE)
    SCREEN.blit(info_text_ball_vel, (10, 10))
    SCREEN.blit(info_text_ball_pos, (10, 30))
    if intersection_vertices:
        info_text_overlap_v = FONT.render(f"Overlap Vertices: {len(intersection_vertices)}", True, WHITE)
        SCREEN.blit(info_text_overlap_v, (10, 50))

    pygame.display.flip()

pygame.quit()