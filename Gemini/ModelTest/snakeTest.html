<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>贪吃蛇</title>
    <style>
        body {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            background-color: #f0f0f0;
            font-family: Arial, sans-serif;
        }
        #game-container {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        #game-board {
            border: 2px solid #333;
            background-color: #fff;
            /* Dimensions will be set by JavaScript */
        }
        #score-board {
            margin-top: 20px;
            font-size: 24px;
            color: #333;
        }
        #start-button, #restart-button {
            margin-top: 15px;
            padding: 10px 20px;
            font-size: 18px;
            cursor: pointer;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
        }
        #restart-button {
            background-color: #f44336;
            display: none; /* Initially hidden */
        }
        .game-over-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 20px;
            border-radius: 10px;
            font-size: 28px;
            text-align: center;
            display: none; /* Initially hidden */
            z-index: 10;
        }
    </style>
</head>
<body>
    <div id="game-container">
        <canvas id="game-board"></canvas>
        <div id="score-board">分数: 0</div>
        <button id="start-button">开始游戏</button>
        <div id="game-over-message">
            游戏结束!<br>
            最终分数: <span id="final-score">0</span>
            <button id="restart-button">重新开始</button>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('game-board');
        const ctx = canvas.getContext('2d');
        const scoreBoard = document.getElementById('score-board');
        const startButton = document.getElementById('start-button');
        const restartButton = document.getElementById('restart-button');
        const gameOverMessage = document.getElementById('game-over-message');
        const finalScoreDisplay = document.getElementById('final-score');

        const gridSize = 20; // 每个格子的大小 (像素)
        const tileCountX = 25; // 画布横向格子数
        const tileCountY = 20; // 画布纵向格子数

        canvas.width = gridSize * tileCountX;
        canvas.height = gridSize * tileCountY;

        let snake = [];
        let food = []; // 食物数组，可以包含多种食物
        let dx = gridSize; // 水平速度
        let dy = 0;      // 垂直速度
        let score = 0;
        let gameLoopTimeout;
        let gameSpeed = 150; // 毫秒，越小越快
        let changingDirection = false; // 防止快速连续改变方向导致180度掉头
        let gameStarted = false;
        let gameOver = false;

        const FOOD_TYPES = {
            NORMAL: { color: 'lightgreen', points: 10, effect: 'grow' },
            DANGER: { color: 'red', points: 0, effect: 'die' },
            DOUBLE: { color: 'yellow', points: 20, effect: 'double' }
        };
        const MAX_FOOD_ITEMS = 5; // 屏幕上同时存在的食物最大数量

        // --- 游戏控制 ---
        startButton.addEventListener('click', startGame);
        restartButton.addEventListener('click', startGame);
        document.addEventListener('keydown', changeDirection);

        function startGame() {
            resetGame();
            gameStarted = true;
            gameOver = false;
            startButton.style.display = 'none';
            gameOverMessage.style.display = 'none';
            restartButton.style.display = 'none'; // 确保重新开始按钮也隐藏
            mainLoop();
        }

        function resetGame() {
            // 初始化蛇的位置和长度
            snake = [
                { x: gridSize * 7, y: gridSize * 10 },
                { x: gridSize * 6, y: gridSize * 10 },
                { x: gridSize * 5, y: gridSize * 10 }
            ];
            // 初始方向向右
            dx = gridSize;
            dy = 0;
            score = 0;
            updateScoreDisplay();
            food = []; // 清空食物
            generateMultipleFood(); // 生成初始食物
            if (gameLoopTimeout) clearTimeout(gameLoopTimeout);
        }

        function mainLoop() {
            if (gameOver) {
                showGameOver();
                return;
            }
            changingDirection = false;
            gameLoopTimeout = setTimeout(() => {
                clearCanvas();
                moveSnake();
                drawFood();
                drawSnake();
                checkCollision();
                mainLoop();
            }, gameSpeed);
        }

        // --- 绘图函数 ---
        function clearCanvas() {
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            ctx.strokeStyle = '#eee'; // 格子线颜色
            for (let x = 0; x < canvas.width; x += gridSize) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvas.height);
                ctx.stroke();
            }
            for (let y = 0; y < canvas.height; y += gridSize) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(canvas.width, y);
                ctx.stroke();
            }
        }

        function drawSnakePart(snakePart) {
            ctx.fillStyle = 'darkgreen';
            ctx.strokeStyle = 'black';
            ctx.fillRect(snakePart.x, snakePart.y, gridSize, gridSize);
            ctx.strokeRect(snakePart.x, snakePart.y, gridSize, gridSize);
        }

        function drawSnake() {
            snake.forEach(drawSnakePart);
            // 绘制蛇头（可选，使其更突出）
            if (snake.length > 0) {
                ctx.fillStyle = 'green'; // 蛇头颜色
                ctx.fillRect(snake[0].x, snake[0].y, gridSize, gridSize);
                ctx.strokeRect(snake[0].x, snake[0].y, gridSize, gridSize);
            }
        }

        function drawFood() {
            food.forEach(f => {
                ctx.fillStyle = f.type.color;
                ctx.strokeStyle = 'darkgrey';
                ctx.beginPath();
                ctx.arc(f.x + gridSize / 2, f.y + gridSize / 2, gridSize / 2.5, 0, 2 * Math.PI);
                ctx.fill();
                ctx.stroke();
            });
        }

        // --- 游戏逻辑 ---
        function moveSnake() {
            const head = { x: snake[0].x + dx, y: snake[0].y + dy };
            snake.unshift(head); // 将新头添加到蛇的开始

            let ateFoodIndex = -1;
            for (let i = 0; i < food.length; i++) {
                if (snake[0].x === food[i].x && snake[0].y === food[i].y) {
                    ateFoodIndex = i;
                    break;
                }
            }

            if (ateFoodIndex !== -1) {
                const eatenFood = food[ateFoodIndex];
                score += eatenFood.type.points;
                updateScoreDisplay();

                // 根据食物类型执行效果
                if (eatenFood.type.effect === 'die') {
                    gameOver = true;
                    return; // 游戏结束，不移除尾巴
                } else if (eatenFood.type.effect === 'double') {
                    const currentLength = snake.length -1; // 不包括刚吃下的头
                    for(let i=0; i < currentLength; i++){
                        // 添加到蛇尾，位置与当前蛇尾相同即可，下次移动时会自动调整
                        snake.push({ ...snake[snake.length - 1] });
                    }
                }
                // 'grow' 效果是默认的，因为我们没有 pop() 尾巴

                food.splice(ateFoodIndex, 1); // 移除吃掉的食物
                generateFood(); // 生成一个新的食物
            } else {
                snake.pop(); // 如果没吃到食物，移除蛇尾以保持长度不变
            }
        }

        function changeDirection(event) {
            if (changingDirection || !gameStarted || gameOver) return;
            changingDirection = true;

            const LEFT_KEY = 37;
            const RIGHT_KEY = 39;
            const UP_KEY = 38;
            const DOWN_KEY = 40;

            const keyPressed = event.keyCode;
            const goingUp = dy === -gridSize;
            const goingDown = dy === gridSize;
            const goingRight = dx === gridSize;
            const goingLeft = dx === -gridSize;

            if (keyPressed === LEFT_KEY && !goingRight) {
                dx = -gridSize;
                dy = 0;
            } else if (keyPressed === UP_KEY && !goingDown) {
                dx = 0;
                dy = -gridSize;
            } else if (keyPressed === RIGHT_KEY && !goingLeft) {
                dx = gridSize;
                dy = 0;
            } else if (keyPressed === DOWN_KEY && !goingUp) {
                dx = 0;
                dy = gridSize;
            } else {
                // 如果按了无效的方向键（比如试图掉头），则不改变方向
                changingDirection = false;
            }
        }

        function randomFoodPosition() {
            let newX, newY;
            while (true) {
                newX = Math.floor(Math.random() * tileCountX) * gridSize;
                newY = Math.floor(Math.random() * tileCountY) * gridSize;
                // 确保食物不生成在蛇身上
                let conflict = false;
                for (const part of snake) {
                    if (part.x === newX && part.y === newY) {
                        conflict = true;
                        break;
                    }
                }
                // 确保食物不生成在已有食物上
                for (const f of food) {
                    if (f.x === newX && f.y === newY) {
                        conflict = true;
                        break;
                    }
                }
                if (!conflict) break;
            }
            return { x: newX, y: newY };
        }

        function generateFood() {
            if (food.length >= MAX_FOOD_ITEMS) return;

            const position = randomFoodPosition();
            let foodType;
            const rand = Math.random();
            if (rand < 0.15) { // 15% 几率是危险食物
                foodType = FOOD_TYPES.DANGER;
            } else if (rand < 0.30) { // 15% 几率是加倍食物
                foodType = FOOD_TYPES.DOUBLE;
            } else { // 70% 几率是普通食物
                foodType = FOOD_TYPES.NORMAL;
            }
            food.push({ x: position.x, y: position.y, type: foodType });
        }

        function generateMultipleFood() {
            for (let i = 0; i < MAX_FOOD_ITEMS; i++) {
                generateFood();
            }
        }


        function checkCollision() {
            // 撞墙
            if (snake[0].x < 0 || snake[0].x >= canvas.width ||
                snake[0].y < 0 || snake[0].y >= canvas.height) {
                gameOver = true;
                return;
            }
            // 撞自己
            for (let i = 1; i < snake.length; i++) {
                if (snake[i].x === snake[0].x && snake[i].y === snake[0].y) {
                    gameOver = true;
                    return;
                }
            }
        }

        function updateScoreDisplay() {
            scoreBoard.textContent = '分数: ' + score;
        }

        function showGameOver() {
            clearTimeout(gameLoopTimeout); // 停止游戏循环
            gameStarted = false;
            finalScoreDisplay.textContent = score;
            gameOverMessage.style.display = 'block';
            restartButton.style.display = 'inline-block'; // 显示重新开始按钮
        }

    </script>
</body>
</html>