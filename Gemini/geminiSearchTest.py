import requests
import json

def call_gemini_model(message, api_key, api_url):
    """
    调用 gemini-2.5-flash-all 模型
    
    Args:
        message (str): 要发送给模型的消息
        api_key (str): API密钥
        api_url (str): API地址
    
    Returns:
        dict: 模型的响应结果
    """
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    data = {
        "model": "gemini-2.5-flash-all",
        "messages": [
            {
                "role": "user",
                "content": message
            }
        ],
        "temperature": 0.7,
        "max_tokens": 1000
    }
    
    try:
        response = requests.post(api_url, headers=headers, json=data)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"请求出错: {e}")
        return None

def main():
    # 配置信息
    api_key = "sk-vRFFqMG3ba5FpvxXJ689ZWUP78SKn5UYlsz8ifVF47wo86Nf"
    api_url = "https://yunwu.ai/v1/chat/completions"
    
    # 测试消息 总结这个网页的内容：https://36kr.com/p/3398065816816003·
    test_message = "你好，请介绍一下自己"  
    
    print("正在调用 gemini-2.5-flash-all 模型...")
    print(f"发送消息: {test_message}")
    print("-" * 50)
    
    # 调用模型
    result = call_gemini_model(test_message, api_key, api_url)
    
    if result:
        # 提取并显示回复
        if 'choices' in result and len(result['choices']) > 0:
            response_content = result['choices'][0]['message']['content']
            print("模型回复:")
            print(response_content)
            print("-" * 50)
            print("完整响应:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print("响应格式异常:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
    else:
        print("调用失败")

if __name__ == "__main__":
    main()
