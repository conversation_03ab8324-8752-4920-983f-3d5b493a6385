import os
import pathlib
import requests
import base64
from PyPDF2 import PdfReader

# 配置信息
API_ENDPOINT = "https://yunwu.ai/v1/chat/completions"
API_KEY = "sk-Mjv9S6FE5oIC3k5F9fxXfXHXWOeaaQcH9qx14IRRGeGEFzzr"
PDF_PATH = r"C:\Users\<USER>\Desktop\威尔森OTA监测报告-简版.pdf"
MODEL_NAME = "gemini-2.5-pro-exp-03-25"

def extract_text_from_pdf(pdf_path):
    """从PDF文件中提取文本内容"""
    text = ""
    try:
        with open(pdf_path, 'rb') as file:
            pdf = PdfReader(file)
            for page in pdf.pages:
                text += page.extract_text() + "\n"
    except Exception as e:
        print(f"Error extracting text from PDF: {e}")
        return None
    return text

def call_yunwu_api(prompt, text_content=None):
    """调用云雾AI的API"""
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }

    if text_content:
        messages = [{
            "role": "user",
            "content": f"{prompt}\n\nHere is the content to translate:\n\n{text_content}"
        }]
    else:
        messages = [{"role": "user", "content": prompt}]

    data = {
        "model": MODEL_NAME,
        "messages": messages
    }

    response = requests.post(API_ENDPOINT, headers=headers, json=data)
    if response.status_code == 200:
        return response.json()["choices"][0]["message"]["content"]
    else:
        raise Exception(f"API调用失败: {response.status_code} - {response.text}")

def translate_pdf_to_html(pdf_path, output_html_path):
    """将PDF内容翻译成英文并输出为HTML"""
    if not os.path.exists(pdf_path):
        print(f"Error: PDF file not found at {pdf_path}")
        return

    print(f"Processing PDF: {pdf_path}")

    try:
        print("Reading PDF file...")
        # 提取PDF文本内容
        text_content = extract_text_from_pdf(pdf_path)
        if not text_content:
            raise Exception("Failed to extract text from PDF")

        # 计算总字节大小并显示进度
        total_chars = len(text_content)
        print(f"Starting translation of {total_chars} characters...")

        # 提示模型将PDF转换为英文HTML
        prompt = """
        Please translate the following Chinese text into English and format it as HTML.
        Please follow these guidelines:
        1. Maintain the original formatting and structure
        2. Preserve all headings, paragraphs, and layout
        3. Translate all text content into fluent English
        4. Keep any tables, lists, or special formatting
        5. Output the result as valid HTML with appropriate tags
        """

        # 调用云雾AI API进行翻译
        print("Translating content...")
        translated_content = call_yunwu_api(prompt, text_content)

        # 将响应写入HTML文件
        with open(output_html_path, 'w', encoding='utf-8') as f:
            f.write('''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Translated PDF Content</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 40px; }
        h1, h2, h3 { color: #333; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; }
    </style>
</head>
<body>
''')
            f.write(translated_content)
            f.write('</body></html>')

        print(f"Translation completed. HTML file saved to: {output_html_path}")

    except Exception as e:
        print(f"Error during translation: {e}")

def main():
    # 设置输出文件路径
    output_html = os.path.splitext(PDF_PATH)[0] + "_translated.html"

    # 执行翻译
    translate_pdf_to_html(PDF_PATH, output_html)

if __name__ == "__main__":
    main()
