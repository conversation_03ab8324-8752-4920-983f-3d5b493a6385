import os
import requests
from pathlib import Path

def download_images(url_list, folder='downloaded_images', referer='https://www.autohome.com.cn'):
    """
    批量下载图片并本地命名保存
    :param url_list: 图片 URL 列表
    :param folder: 本地保存目录（自动创建）
    :param referer: Referer 头，用于绕过防盗链
    """
    Path(folder).mkdir(parents=True, exist_ok=True)

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) '
                      'AppleWebKit/537.36 (KHTML, like Gecko) '
                      'Chrome/125.0 Safari/537.36',
        'Referer': referer
    }

    for idx, url in enumerate(url_list, 1):
        try:
            resp = requests.get(url.strip(), headers=headers, timeout=10)
            resp.raise_for_status()

            # 生成文件名：image_0001.jpg
            ext = url.split('?')[0].split('.')[-1][:4] or 'jpg'
            filename = f'image_{idx:04d}.{ext}'
            save_path = Path(folder) / filename

            with open(save_path, 'wb') as f:
                f.write(resp.content)
            print(f'✅ 已保存：{save_path}')
        except Exception as e:
            print(f'❌ 下载失败：{url} -> {e}')

# ====== 使用示例 ======
if __name__ == '__main__':
    urls = [
        'https://club2.autoimg.cn/album/g25/M0A/A4/E5/userphotos/2025/07/21/21/820_ChxkqWh-PfWAWLs3ADpwgXJAUSs911.jpg',
        # … 继续添加更多 URL
    ]
    download_images(urls)