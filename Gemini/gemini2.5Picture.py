import requests
import base64
import os

# 配置信息
API_ENDPOINT = "https://yunwu.ai/v1/chat/completions"
API_KEY = "sk-vRFFqMG3ba5FpvxXJ689ZWUP78SKn5UYlsz8ifVF47wo86Nf"  # 替换为你的实际API密钥
# API_KEY = "sk-hjrQtiRW4Z0ULyLGfNFkYMXpn4DBnURNu3xcMT2TlIs0TvgB"  # 替换为你的实际API密钥
IMAGE_PATH = r"C:\Users\<USER>\Desktop\250729_i8参数配置中文(2)_9.png"  # 原始图片路径
PROMPT = "完整提取图片上的内容，图片原格式用HTML输出"  # 提示词内容


def process_image(image_path):
    """验证并编码图片文件"""
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"图片文件不存在：{image_path}")

    max_size = 10 * 1024 * 1024  # 10MB
    file_size = os.path.getsize(image_path)
    if file_size > max_size:
        raise ValueError(f"图片过大 ({file_size / 1024 / 1024:.1f}MB)，超过10MB限制")

    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')


def query_gemini():
    """执行多模态请求"""
    try:
        encoded_image = process_image(IMAGE_PATH)

        headers = {
            "Authorization": f"Bearer {API_KEY}",
            "Content-Type": "application/json"
        }

        payload = {
            "model": "gemini-2.5-pro",  #gemini-2.5-pro-preview-05-06
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": PROMPT},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{encoded_image}",
                                "detail": "high"
                            }
                        }
                    ]
                }
            ],
            "temperature": 0.3
        }

        response = requests.post(API_ENDPOINT, headers=headers, json=payload)
        response.raise_for_status()

        result = response.json()
        if 'choices' in result and len(result['choices']) > 0:
            return result['choices'][0]['message']['content']
        return "未收到有效响应"

    except Exception as e:
        if isinstance(e, requests.HTTPError):
            status_code = e.response.status_code
            if status_code == 401:
                return "错误：API密钥无效"
            elif status_code == 413:
                return "错误：图片文件过大"
            elif status_code == 415:
                return "错误：不支持的图片格式"
            else:
                return f"HTTP错误：{status_code}"
        elif isinstance(e, FileNotFoundError):
            return f"文件错误：{str(e)}"
        elif isinstance(e, ValueError):
            return f"参数错误：{str(e)}"
        else:
            return f"请求失败：{str(e)}"


if __name__ == "__main__":
    print("正在处理图片...")
    result = query_gemini()

    print("\n提取结果：")
    print(result)

    # ============ 新增保存功能 ============
    try:
        # 生成保存路径：与原图片同目录同文件名，后缀改为.md
        dir_path = os.path.dirname(IMAGE_PATH)
        file_name = os.path.splitext(os.path.basename(IMAGE_PATH))[0]
        save_path = os.path.join(dir_path, f"{file_name}.html")

        # 写入Markdown文件
        with open(save_path, "w", encoding="utf-8") as f:
            f.write(result)

        print(f"\n结果已保存至：{save_path}")
    except Exception as e:
        print(f"\n保存文件时出错：{str(e)}")
    # ============ 新增内容结束 ============