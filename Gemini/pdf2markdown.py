import google.generativeai as genai # 更新了导入方式
import os
import pathlib

# --- 配置 ---
# 强烈建议通过环境变量设置 API 密钥
# 例如: os.environ["GOOGLE_API_KEY"]
# 如果你必须硬编码（不推荐），请替换下面的 YOUR_API_KEY
API_KEY = "AIzaSyAtHtoUQLT1sgqmYZDK8KZp6pvQ12wLkMM" # 请替换为你的真实API密钥

PDF_FILE_PATH = r"D:\06-AI 测试文件\营销卖点文件\比亚迪-202503-B-关于第二代元PLUS智驾版上市营销指导的通知.pdf"
MODEL_NAME = "gemini-2.5-pro-exp-03-25"

# --- 主程序 ---
def configure_genai(api_key):
    """配置 Google Generative AI"""
    try:
        genai.configure(api_key=api_key)
        print("Google GenAI configured successfully.")
    except Exception as e:
        print(f"Error configuring GenAI: {e}")
        print("Please ensure your API key is correct and you have the necessary permissions.")
        return False
    return True

def pdf_to_markdown(pdf_path, output_md_path, model_name):
    """
    使用 Gemini API 将 PDF 内容提取并转换为 Markdown。
    """
    if not os.path.exists(pdf_path):
        print(f"Error: PDF file not found at {pdf_path}")
        return

    print(f"Processing PDF: {pdf_path}")
    print(f"Using model: {model_name}")

    try:
        print("Reading PDF file...")
        with open(pdf_path, "rb") as f:
            pdf_data = f.read()

        # 创建多模态内容部分
        pdf_part = {"mime_type": "application/pdf", "data": pdf_data}

        # 提示模型将整个PDF转换为Markdown
        # 这个提示非常关键，需要指导模型如何处理文本、图片和表格
        prompt = """
        请将这份PDF文档的全部内容转换为Markdown格式。
        请遵循以下指引：
        1.  文本内容：完整保留所有文本，并尽可能保持段落结构。
        2.  标题：识别文档中的标题层级，并使用Markdown的 #, ##, ### 等语法表示。
        3.  列表：将无序列表和有序列表转换为Markdown的列表格式。
        4.  图片：
            - 对于每张图片，请在Markdown中插入一个占位符，格式为：`![图片描述或上下文 - 页码X](image_placeholder_Y.png)`。
            - X 是图片所在的原始PDF页码（如果可以确定）。
            - Y 是图片的序号 (image_placeholder_1.png, image_placeholder_2.png, ...)。
            - 如果可能，请为“图片描述或上下文”提供一个简短的说明。
            - 注意：你无法直接提取图片文件，但这个占位符很重要。
        5.  表格：
            - 尽最大努力将所有表格转换为Markdown表格格式。
            - 对于非常复杂的表格，如果无法完美转换，请尝试以最可读的方式呈现其数据和结构。
        6.  代码块：如果PDF中包含代码片段，请使用Markdown的代码块语法。
        7.  链接：如果PDF中有超链接，请尝试转换为Markdown链接格式 `[链接文本](URL)`。
        8.  整体结构：尽量保持文档的逻辑流程。
        9.  输出：请只输出纯Markdown内容，不要包含任何额外的解释、引言或总结，除非是图片描述。
        10. 语言：输出的Markdown内容应使用与PDF文档主要语言相同的语言。

        如果PDF文件为空或无法处理，请输出：“错误：无法处理此PDF文件内容。”
        """

        print("Generating Markdown content from PDF...")
        model = genai.GenerativeModel(model_name)
        response = model.generate_content([pdf_part, prompt]) # 直接传递 parts 列表

        markdown_content = response.text

        print(f"Saving Markdown to: {output_md_path}")
        with open(output_md_path, "w", encoding="utf-8") as f:
            f.write(markdown_content)
        print("Markdown file saved successfully.")

        # (可选) 打印部分内容以供快速检查
        # print("\n--- Generated Markdown (first 500 chars) ---")
        # print(markdown_content[:500])
        # print("...\n---")

    except AttributeError as ae:
        if "'NoneType' object has no attribute 'parts'" in str(ae) and 'response' in locals() and response.prompt_feedback:
            print(f"Content generation failed. Prompt feedback: {response.prompt_feedback}")
            print("This often indicates the content was blocked due to safety settings or other reasons.")
        else:
            print(f"An AttributeError occurred: {ae}")
            if 'response' in locals():
                print(f"Response details: {response}")
    except Exception as e:
        print(f"An error occurred during PDF to Markdown conversion: {e}")
        if 'response' in locals() and hasattr(response, 'prompt_feedback'):
             print(f"Prompt Feedback: {response.prompt_feedback}")


if __name__ == "__main__":
    if not configure_genai(API_KEY):
        exit()

    # 自动生成 Markdown 文件名
    pdf_file = pathlib.Path(PDF_FILE_PATH)
    output_markdown_path = pdf_file.with_suffix(".md")

    pdf_to_markdown(PDF_FILE_PATH, str(output_markdown_path), MODEL_NAME)

    print("\n--- Reminder ---")
    print("1. Review the generated Markdown file for accuracy, especially for tables and image placeholders.")
    print("2. You will need to manually extract images from the PDF and update the 'image_placeholder_Y.png' links if you want them displayed.")
    print("3. For very complex PDFs, consider specialized PDF-to-Markdown tools or libraries (e.g., pymupdf, nougat-ocr) for potentially better structural conversion, then use an LLM for refinement if needed.")