import base64
import httpx
import json
import os
import pandas as pd
import re

def encode_image_to_base64(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

def extract_table_data(text_content):
    """
    从文本内容中提取表格数据并转换为可用于pandas的格式
    """
    # 按行分割文本
    lines = [line.strip() for line in text_content.strip().split('\n')]

    # 存储处理后的数据
    processed_data = []

    for line in lines:
        # 跳过空行
        if not line:
            continue

        # 如果行以'|'开头，这是表格行
        if line.startswith('|'):
            # 分割单元格，去除首尾的'|'并分割
            cells = line.strip('|').split('|')
            # 清理每个单元格的内容
            cells = [cell.strip() for cell in cells]
            processed_data.append(cells)

    # 如果有数据
    if processed_data:
        try:
            # 使用第一行作为列名
            headers = processed_data[0]
            # 创建DataFrame，使用剩余行作为数据
            df = pd.DataFrame(processed_data[1:], columns=headers)

            # 清理列名
            df.columns = df.columns.str.strip()

            # 返回处理后的DataFrame
            return df
        except Exception as e:
            print(f"创建DataFrame时出错: {str(e)}")
            return None
    return None

def process_image_with_gemini(image_path):
    # API配置
    api_key = "sk-vRFFqMG3ba5FpvxXJ689ZWUP78SKn5UYlsz8ifVF47wo86Nf"
    api_base_url = "https://yunwu.ai/v1/chat/completions"

    # 将图片转换为base64
    base64_image = encode_image_to_base64(image_path)

    # 构建请求payload
    payload = {
        "model": "gemini-2.5-flash",  # gemini-2.5-pro
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{base64_image}"
                        }
                    },
                    {
                        "type": "text",
                        "text": "请完整提取并分析这张图片中的所有的表格内容，只输出表格上的内容，其他不做输出"
                    }
                ]
            }
        ]
    }

    # 发送请求
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }

    try:
        with httpx.Client(timeout=60.0) as client:
            response = client.post(
                api_base_url,
                json=payload,
                headers=headers
            )
            response.raise_for_status()
            result = response.json()
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']
                print("\nAPI返回的原始内容：")
                print(content)
                print("\n正在处理表格数据...")
                table_data = extract_table_data(content)
                return content, table_data
            else:
                return f"API返回数据格式异常: {json.dumps(result, ensure_ascii=False)}", None
    except Exception as e:
        return f"处理图片时发生错误: {str(e)}", None

def main():
    # 指定图片路径
    image_path = r"C:\Users\<USER>\Desktop\250729_i8参数配置中文(2)_9.png"

    # 确保图片文件存在
    if not os.path.exists(image_path):
        print(f"错误：图片文件不存在: {image_path}")
        return

    # 处理图片并获取结果
    raw_result, table_data = process_image_with_gemini(image_path)

    print("\n原始识别结果：")
    print(raw_result)

    # 保存到Excel文件
    if table_data is not None:
        try:
            # 生成输出文件路径（与输入图片在同一目录）
            output_path = os.path.join(
                os.path.dirname(image_path),
                f"{os.path.splitext(os.path.basename(image_path))[0]}_extracted.xlsx"
            )

            # 清理数据：移除空列和只包含 '|' 或 '-' 的列
            table_data = table_data.loc[:, ~table_data.apply(lambda x: x.str.contains('^[|-]*$', na=True).all())]

            # 清理列名中的特殊字符
            table_data.columns = table_data.columns.str.replace('[|/-]', '', regex=True).str.strip()

            # 清理单元格数据
            table_data = table_data.map(lambda x: x.strip('|').strip() if isinstance(x, str) else x)
            table_data = table_data.replace('', pd.NA)  # 将空字符串转换为NA

            # 移除全为NA的行
            table_data = table_data.dropna(how='all')

            # 保存到Excel，不包含index
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                table_data.to_excel(writer, index=False, sheet_name='提取数据')

            print(f"\n表格数据已成功保存到: {output_path}")

            # 显示处理后的表格数据预览
            print("\n处理后的表格数据预览：")
            print(table_data.head())

        except Exception as e:
            print(f"\n保存Excel文件时出错: {str(e)}")
    else:
        print("\n未能从识别结果中提取到有效的表格数据")

if __name__ == "__main__":
    main()
