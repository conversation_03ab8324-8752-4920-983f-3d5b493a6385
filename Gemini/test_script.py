#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import sys
import time

class TestGeminiChat:
    def __init__(self):
        print("Test script initialized")
        self.conversation_history = []
        try:
            self.conversation_history = [
                {"role": "system", "content": "Test system prompt"}
            ]
            print("Initialization successful")
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    print("Starting test...")
    chat = TestGeminiChat()
    print("Test completed successfully") 