import os
import base64
import httpx
import time
from tenacity import (
    retry,
    stop_after_attempt,
    wait_random_exponential,
    retry_if_exception_type,
    before_sleep_log
)
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PDFProcessor:
    def __init__(self, pdf_path, chunk_size=1024 * 1024):
        self.API_ENDPOINT = "https://yunwu.ai/v1/chat/completions"
        self.API_KEY = "sk-2vU2NrEPnmYYJ7AIr5W9H990jDHU4y0bleN2JDYim7sQITgR"
        self.pdf_path = pdf_path
        self.chunk_size = chunk_size
        self.MAX_RETRIES = 5
        self.BASE_DELAY = 10  # 初始延迟10秒

    @retry(
        wait=wait_random_exponential(multiplier=1, min=self.BASE_DELAY, max=60),
        stop=stop_after_attempt(3),
        retry=retry_if_exception_type((httpx.HTTPStatusError, httpx.RequestError)),
        before_sleep=before_sleep_log(logger, logging.WARNING)
    )
    def process_pdf_chunk(self, chunk_data, chunk_number):
        prompt = """
        [保持原有完整的prompt内容不变]
        """

        try:
            # 智能负载构建
            payload = self._build_payload(prompt, chunk_data)

            # 自适应重试逻辑
            for attempt in range(self.MAX_RETRIES):
                try:
                    response = self._send_request(payload)
                    return response['choices'][0]['message']['content']

                except httpx.HTTPStatusError as e:
                    if e.response.status_code == 429:
                        retry_after = int(e.response.headers.get("Retry-After", 30))
                        logger.warning(f"速率限制触发，将在 {retry_after}秒后重试...")
                        time.sleep(retry_after)
                    elif 500 <= e.response.status_code < 600:
                        sleep_time = self.BASE_DELAY * (2 ** attempt)
                        logger.warning(f"服务器错误，第{attempt + 1}次重试，等待 {sleep_time}秒...")
                        time.sleep(sleep_time)
                    else:
                        raise

            raise Exception(f"经过 {self.MAX_RETRIES} 次重试仍失败")

        except Exception as e:
            logger.error(f"处理第 {chunk_number} 块时发生关键错误: {str(e)}")
            raise

    def _build_payload(self, prompt, chunk_data):
        """构建符合API规范的请求体"""
        return {
            "model": "yunwu-ai-model-v1",  # 根据实际API文档调整模型名称
            "messages": [
                {
                    "role": "system",
                    "content": "你是一个专业的多模态文档解析引擎"
                },
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "file",
                            "file": {
                                "data": base64.b64encode(chunk_data).decode("utf-8"),
                                "file_name": os.path.basename(self.pdf_path),
                                "mime_type": "application/pdf"
                            }
                        }
                    ]
                }
            ],
            "settings": {
                "output_format": "json",
                "max_tokens": 4096
            }
        }

    def _send_request(self, payload):
        """发送带熔断保护的API请求"""
        with httpx.Client(
                timeout=60,
                limits=httpx.Limits(max_connections=5, max_keepalive_connections=2)
        ) as client:
            response = client.post(
                self.API_ENDPOINT,
                headers={
                    "Authorization": f"Bearer {self.API_KEY}",
                    "X-Request-ID": os.urandom(16).hex(),
                    "Content-Type": "application/json"
                },
                json=payload
            )
            response.raise_for_status()
            return response.json()

    def process_pdf(self):
        """带流量整形功能的处理流程"""
        try:
            with open(self.pdf_path, "rb") as f:
                pdf_data = f.read()

            # 智能分块策略
            chunk_size = self._adaptive_chunk_size(len(pdf_data))
            results = []

            for i in range(0, len(pdf_data), chunk_size):
                chunk_number = (i // chunk_size) + 1
                chunk = pdf_data[i:i + chunk_size]

                logger.info(f"处理第 {chunk_number} 块 (大小: {len(chunk) / 1024:.2f}KB)")
                try:
                    results.append(self.process_pdf_chunk(chunk, chunk_number))
                    self._rate_limit_delay(chunk_number)
                except Exception as e:
                    logger.error(f"块 {chunk_number} 处理失败: {str(e)}")
                    continue

            return "\n---\n".join(results) if results else None

        except Exception as e:
            logger.critical(f"处理流程失败: {str(e)}")
            return None

    def _adaptive_chunk_size(self, total_size):
        """根据文件大小自动调整分块策略"""
        if total_size <= 5 * 1024 * 1024:  # <5MB不分割
            return total_size
        return min(self.chunk_size, 2 * 1024 * 1024)  # 最大2MB/块

    def _rate_limit_delay(self, chunk_number):
        """智能速率控制"""
        base_delay = 5
        if chunk_number % 5 == 0:  # 每5个请求增加延迟
            time.sleep(base_delay * 1.5)
        else:
            time.sleep(base_delay)


def main():
    pdf_path = r"Y:\产品中心2023\智研创新部\政策知识库\部分品牌近半年政策\政策文件原件\奥迪-202411-A-奥迪A6L 25年型发票支持政策--经销商通知.pdf"
    processor = PDFProcessor(pdf_path=pdf_path)

    try:
        result = processor.process_pdf()
        if result:
            output_dir = os.path.dirname(os.path.abspath(__file__))
            output_path = os.path.join(output_dir, "output.txt")
            os.makedirs(output_dir, exist_ok=True)

            with open(output_path, "w", encoding="utf-8") as f:
                f.write(result)
            print(f"处理完成，输出文件保存在：{output_path}")
        else:
            print("处理失败，未能获取结果")
    except Exception as e:
        print(f"程序执行出错: {str(e)}")


if __name__ == "__main__":
    main()