#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的Gemini Pro对话程序
"""

import sys
import os
sys.path.append('.')

try:
    from Gemini.gemini_pro_chat import GeminiProChat
    
    print("✅ 成功导入 GeminiProChat")
    
    # 创建实例
    chat = GeminiProChat()
    print("✅ 成功创建 GeminiProChat 实例")
    
    # 测试一个简单的问题
    print("\n🔄 测试API调用...")
    response = chat.send_message("你好，请简单介绍一下自己")
    
    print(f"\n📤 响应: {response}")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
except Exception as e:
    print(f"❌ 运行错误: {e}")
    import traceback
    traceback.print_exc() 