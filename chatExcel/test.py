import asyncio
from mcp.client import Mcp<PERSON><PERSON>
from langgraph.prebuilt import create_react_agent

async def main():
    client = McpClient(
        url="http://mcp.chatexcel.com/sse",
        api_key="${YoUR_API_KEY}"
    )

    agent = create_react_agent(
        "anthropic:claude-3-7-sonnet-latest",
        client.get_tools()
    )
    response = await agent.ainvoke({
        "messages": [
            {"role": "user", "content": "Could you analyze the OpenAI 2024 Financial Report?"}
        ]
    })
    print(response)

if __name__ == "__main__":
    asyncio.run(main())
