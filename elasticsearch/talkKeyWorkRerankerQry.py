from elasticsearch import Elasticsearch
import dashscope
from dashscope.aigc.generation import Generation
from dashscope.rerank.text_rerank import TextReRank
import json

# 配置信息
ES_URL = "https://my-elasticsearch-project-a8031b.es.us-east-1.aws.elastic.cloud:443"
ES_API_KEY = "T0dyMHFaWUJfWFdDekFGWTRaV2c6XzhMMlRSZnNFQkpaS01tQ1hlQzE0QQ=="
INDEX_NAME = "search-nffp"

QWEN_API_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
QWEN_API_KEY = "sk-7b9067ef70794d7286da8432be28f18c"
dashscope.base_http_url = QWEN_API_URL

# 元数据字段映射
KEY_MAPPING = {
    '发布时间': 'publish_date',
    '厂商': 'manufacturer',
    '适用车型': 'applicable_model',
    '适用型号': 'applicable_version',
    '场景分类': 'scenario_category',
    '子场景': 'scenario_subcategory',
    '关键字': 'keywords',
    '参考话术': 'reference_speech',
    '来源文件': 'source_document',
    '页码': 'page_number'
}

# 初始化 Elasticsearch 客户端
es = Elasticsearch(
    hosts=[ES_URL],
    api_key=ES_API_KEY
)

# 初始化 DashScope API
TextReRank.api_key = QWEN_API_KEY
dashscope.api_key = QWEN_API_KEY
dashscope.base_http_url = QWEN_API_URL

def search_keywords(keyword):
    query_body = {
        "query": {
            "multi_match": {
                "query": keyword,
                "fields": [
                    "keywords",
                    "manufacturer",
                    "applicable_model",
                    "applicable_version",
                    "scenario_category",
                    "scenario_subcategory",
                    "reference_speech"
                ],
                "type": "best_fields",
                "tie_breaker": 0.3
            }
        },
        "size": 50
    }

    try:
        response = es.search(index=INDEX_NAME, body=query_body)
    except Exception as e:
        return f"Elasticsearch 查询失败，错误信息：{str(e)}"

    hits = response['hits']['hits']

    # 过滤低相关性得分
    filtered_hits = [hit for hit in hits if hit.get('_score', 0) >= 5]

    if not filtered_hits:
        return "未找到与关键词相关的任何结果，请尝试其他关键词或检查索引配置。"

    results = []
    for idx, hit in enumerate(filtered_hits, 1):
        source = hit['_source']
        result = {
            "score": hit['_score'],
            "data": {
                "发布时间": source.get('publish_date'),
                "厂商": source.get('manufacturer'),
                "适用车型": source.get('applicable_model'),
                "适用型号": source.get('applicable_version'),
                "场景大类": source.get('scenario_category'),
                "场景小类": source.get('scenario_subcategory'),
                "关键字": source.get('keywords'),
                "参考话术": source.get('reference_speech'),
                "来源文件": source.get('source_document'),
                "涉及页码": source.get('page_number')
            }
        }
        results.append(result)

    return results


def extract_query_info(query):
    """
    使用 Qwen 模型提取查询中的关键信息
    """
    prompt = f"""
    请从以下查询中提取关键信息，并以JSON格式返回。如果某项信息不存在，对应值设为空字符串：
    查询内容：{query}
    
    仅返回如下格式的JSON（不要其他任何内容）：
    {{
        "适用车型": ""
    }}
    """

    try:
        response = Generation.call(
            model="qwen-max-2025-01-25",
            prompt=prompt
        )
        import json
        return json.loads(response.output.text)
    except Exception as e:
        print(f"提取查询信息失败：{str(e)}")
        return None

def filter_results(results, query_info):
    """
    根据提取的查询信息过滤结果
    """
    filtered_results = []

    for result in results:
        match_score = 0
        data = result['data']

        # 对每个非空字段进行匹配度评分
        for field, query_value in query_info.items():
            if not query_value:
                continue

            result_value = data.get(field, '').lower()
            if query_value.lower() in result_value:
                match_score += 1

        # 如果至少匹配一个字段，保留该结果
        if match_score > 0:
            result['filter_score'] = match_score
            filtered_results.append(result)

    # 按匹配分数排序
    filtered_results.sort(key=lambda x: x['filter_score'], reverse=True)
    return filtered_results

def rerank_results(query, results):
    """
    先提取信息过滤，再进行重排序
    """
    # 提取查询信息
    query_info = extract_query_info(query)
    if query_info:
        print("\n提取到的查询信息：")
        for k, v in query_info.items():
            if v:
                print(f"{k}: {v}")

        # 预过滤结果
        filtered_results = filter_results(results, query_info)
        if not filtered_results:
            print("预过滤后没有匹配结果，使用原始结果进行重排序")
            filtered_results = results
    else:
        filtered_results = results

    # 准备重排序文档
    documents = []
    for res in filtered_results:
        text = (f"{res['data']['厂商']} {res['data']['适用车型']} {res['data']['适用型号']} "
                f"{res['data']['关键字']} {res['data']['参考话术']} {res['data']['场景大类']} "
                f"{res['data']['场景小类']}")
        documents.append(text)

    try:
        response = TextReRank.call(
            model="gte-rerank-v2",
            query=query,
            documents=documents
        )

        if response.status_code == 200:
            ranked_results = []
            if 'results' not in response.output:
                return filtered_results

            for result in response.output['results']:
                if 'relevance_score' not in result:
                    continue

                doc_idx = result.get('index', len(ranked_results))
                score = result['relevance_score']
                ranked_results.append((doc_idx, score))

            if not ranked_results:
                return filtered_results

            ranked_results.sort(key=lambda x: x[1], reverse=True)

            reranked_results = []
            for doc_idx, score in ranked_results:
                if doc_idx < len(filtered_results):
                    result_copy = filtered_results[doc_idx].copy()
                    result_copy['rerank_score'] = score
                    result_copy['original_score'] = result_copy['score']
                    result_copy["score"] = score
                    if 'filter_score' in filtered_results[doc_idx]:
                        result_copy['filter_score'] = filtered_results[doc_idx]['filter_score']
                    reranked_results.append(result_copy)

            return reranked_results or filtered_results

        else:
            print(f"重排序调用失败：{response.message}")
            return filtered_results

    except Exception as e:
        print(f"重排序过程出错：{str(e)}")
        return filtered_results

def generate_qwen_response(query,results):
    context = "\n".join([
        f"【匹配度：{res['score']}】\n"
        f"发布时间：{res['data']['发布时间']}\n"
        f"厂商：{res['data']['厂商']}\n"
        f"适用车型：{res['data']['适用车型']}\n"
        f"适用型号：{res['data']['适用型号']}\n"
        f"场景大类：{res['data']['场景大类']}\n"
        f"场景小类：{res['data']['场景小类']}\n"
        f"关键字：{res['data']['关键字']}\n"
        f"参考话术：{res['data']['参考话术']}\n"
        f"来源文件：{res['data']['来源文件']}\n"
        f"涉及页码：{res['data']['涉及页码']}\n"
        for res in results
    ])

    prompt = f"""
                你是一个智能助手，请根据以下内容回答用户的问题：
                
                以下是与关键词最相关的资料条目：
                {context}
                
                用户问题：
                {query}
                
                注意：资料条目中可能存在与用户问题不相关的内容，该部分内容可以忽略。
            """

    response = Generation.call(
        model="qwen-max-2025-01-25",
        prompt=prompt
    )

    return response.output.text


if __name__ == "__main__":
    user_input = input("请输入你要搜索的关键字：")

    # applicable_model = extract_query_info(user_input)
    # if applicable_model:
    #     applicable_model = applicable_model.get('适用车型', None)
    #     if applicable_model:
    #         print(f"提取到的适用车型：{applicable_model}")
    #     else:
    #         print("未提取到适用车型信息，将使用默认查询。")
    # else:
    #     print("提取适用车型信息失败，将使用默认查询。")

    search_results = search_keywords(user_input)

    if isinstance(search_results, str):
        print(search_results)
    else:
        # # 使用 rerank 进行语义排序
        # print("正在进行语义排序...")
        # reranked_results = rerank_results(user_input, search_results)
        #
        # # 打印 rerank 后的结果
        # print("\n ========== Rerank 后的结果 ==========\n")
        # for idx, res in enumerate(reranked_results, 1):
        #     print(f"\n结果 {idx} 原始相关性: {res['original_score']:.2f} 重排序得分：{res['rerank_score']:.2f}:")
        #     for key, value in res['data'].items():
        #         if value:
        #             print(f"{key}: {value}")


        # 使用 Qwen 生成自然语言回答
        # answer = generate_qwen_response(user_input,reranked_results)
        answer = generate_qwen_response(user_input,search_results)
        print("\n ============ Qwen-Max AI 回答 ============\n")
        print(answer)