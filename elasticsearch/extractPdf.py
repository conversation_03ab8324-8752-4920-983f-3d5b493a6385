import pdfplumber
from PIL import Image
import pytesseract


def extract_pdf_content(pdf_path):
    content = {"text": "", "tables": [], "images": []}

    with pdfplumber.open(pdf_path) as pdf:
        for page in pdf.pages:
            # 提取文本
            page_text = page.extract_text()
            if page_text:
                content["text"] += page_text + "\n"

            # 提取表格
            try:
                tables = page.extract_tables()
                if tables:
                    content["tables"].extend(tables)
            except Exception as e:
                print(f"提取表格时出错：{str(e)}")

            # 提取图片
            try:
                for img in page.images:
                    # 获取图片对象并转换为PIL格式
                    page_image = page.to_image()
                    img_pil = page_image.annotated.convert('RGB')

                    # 计算图片区域
                    x0, y0, x1, y1 = img['x0'], img['y0'], img['x1'], img['y1']
                    # 转换坐标（pdfplumber使用底部为原点，PIL使用顶部为原点）
                    height = img_pil.height
                    y0, y1 = height - y1, height - y0

                    # 裁剪图片
                    cropped = img_pil.crop((x0, y0, x1, y1))

                    # 执行OCR
                    ocr_text = pytesseract.image_to_string(cropped, lang='chi_sim')
                    if ocr_text.strip():  # 只保存有文字的图片结果
                        content["images"].append({
                            "bbox": (x0, y0, x1, y1),
                            "ocr_text": ocr_text.strip()
                        })
            except Exception as e:
                print(f"处理图片时出错：{str(e)}")

    return content

def convert_to_markdown(pdf_content):
    """
    将PDF提取的内容转换为Markdown格式
    """
    markdown = []

    # 添加文本内容
    if pdf_content["text"]:
        markdown.append("## 文本内容\n")
        markdown.append(pdf_content["text"].strip())
        markdown.append("\n")

    # 添加表格内容
    if pdf_content["tables"]:
        markdown.append("## 表格内容\n")
        for idx, table in enumerate(pdf_content["tables"], 1):
            markdown.append(f"\n### 表格 {idx}\n")
            # 创建表头分隔符
            if table:
                headers = [""] * len(table[0])
                separators = ["|---"] * len(table[0])

                # 生成表格内容
                markdown.append("|" + "|".join(str(cell or '') for cell in table[0]) + "|")
                markdown.append("|" + "|".join(separators) + "|")

                # 添加表格数据行
                for row in table[1:]:
                    markdown.append("|" + "|".join(str(cell or '') for cell in row) + "|")
            markdown.append("\n")

    # 添加图片OCR内容
    if pdf_content["images"]:
        markdown.append("## 图片文字内容\n")
        for idx, img in enumerate(pdf_content["images"], 1):
            markdown.append(f"\n### 图片 {idx}\n")
            markdown.append(f"位置: {img['bbox']}\n")
            markdown.append(f"文字内容:\n{img['ocr_text']}\n")

    return "\n".join(markdown)

# 修改主函数
if __name__ == "__main__":
    user_input = input("请输入你要提取PDF的路径：")

    pdf_content = extract_pdf_content(user_input)
    markdown_content = convert_to_markdown(pdf_content)
    print("===============PDF Markdown格式内容：===============")
    print(markdown_content)
