import os
import json
import datetime
from typing import List, Dict
import dashscope
from dashscope import Generation
from openai import OpenAI

class MarkdownProcessor:
    def __init__(self, api_key: str):
        self.api_key = api_key
        dashscope.api_key = api_key

    def read_markdown_file(self, file_path: str) -> str:
        """读取Markdown文件内容"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()

    def process_markdown(self, markdown_content: str, file_path: str) -> List[Dict]:
        """处理Markdown内容并返回结构化数据"""

        # 构建提示词
        prompt = self._build_prompt(markdown_content, file_path)

        # print("prompt:",prompt)

        try:
            # 调用Qwen-Max模型
            client = OpenAI(
                # 若没有配置环境变量，请用百炼API Key将下行替换为：api_key="sk-xxx",
                api_key=self.api_key,
                base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
            )

            completion = client.chat.completions.create(
                # 模型列表：https://help.aliyun.com/zh/model-studio/getting-started/models
                model="qwen-max-2025-01-25",
                messages=[
                    {"role": "system", "content": "你是一个高级文档解析与数据提取助手，专门处理包含文本、复杂表格和图片的Markdown数据。"},
                    {"role": "user", "content": prompt},
                ]
            )

            # response = Generation.call(
            #     model='qwen-max-2025-01-25',
            #     prompt=prompt,
            #     result_format='message',
            #     temperature=0.1,
            #     max_tokens=8000  # 增加最大token数
            # )

            # 使用点号语法获取响应内容
            if completion.choices and completion.choices[0].message.content:
                return completion.choices[0].message.content
            else:
                print("模型返回空响应")
                return []

        except Exception as e:
            print(f"处理过程中出现错误: {str(e)}")
            if 'response' in locals():
                print(f"API响应内容: {completion if completion else 'None'}")
            return []

    def _build_prompt(self, content: str, file_path: str) -> str:
        """构建模型提示词"""
        file_name = os.path.basename(file_path)
        current_time = datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ")

        # 这里是您提供的提示词模板
        prompt_template = f"""
        # **角色**
                    你是一个高级文档解析与数据提取助手，专门处理包含文本、复杂表格和图片的Markdown数据。

                    # **任务**
                    你的任务是精确地解析提供的Markdown数据，识别并提取其中的文本块、表格和图片。对于每一种识别出的内容块（chunk），你都需要按照下面定义的严格JSON结构和字段要求进行格式化输出。最终结果应该是一个包含所有提取内容块的JSON对象数组。

                    # **输出格式与要求**
                    对于Markdown中的每一个内容块（文本、表格、图片），都必须生成一个独立的JSON对象。请严格遵循以下针对不同内容类型的字段结构和说明：

                    ## **1. 文本内容 (分块类型: "文本")**
                    当内容块是纯文本时，使用以下JSON结构：
                    {{
                      "基础元数据": {{
                        "分块ID": "[当前文本块的唯一标识符，例如 'text_chunk_001']",
                        "分块类型": "文本",
                        "原文件名称": {file_name},
                        "原文件地址": {file_path},
                        "发布时间": "[文档发布日期，格式 YYYY-MM-DD，若无则留空]",
                        "发布厂商": "[文档发布机构或公司，若无则留空]",
                        "适用车型": "[文档适用的车型信息，若无则留空或使用数组如 ['车型A', '车型B']]"
                      }},
                      "上下文元数据": {{
                        "涉及页码": "[当前文本块所在的页码，例如 5 或 [5, 6] 表示跨页]",
                        "分块顺序": "[当前文本块在整个文档中的逻辑顺序号，从1开始计数]",
                        "章节标题": "[当前文本块所属的章节标题，若无法明确判断则留空]"
                      }},
                      "内容类型元数据": {{
                        "内容数据格式": "Markdown"
                      }},
                      "业务元数据": {{
                        "标签": "[与文本内容相关的业务标签，可为空数组 []]"
                      }},
                      "分块内容": {{
                        "分块内容": "[提取到的实际文本内容，使用Markdown格式，保留原始换行和基本格式]"
                      }},
                      "系统管理元数据": {{
                        "修改时间": "[当前处理时间，ISO 8601格式，例如 '2023-10-27T10:30:00Z']",
                        "版本号": "[解析脚本或模型的版本号，例如 'v1.0']"
                      }}
                    }}

                    ## **2. 表格内容 (分块类型: "表格")**
                    {{
                      "基础元数据": {{
                        "分块ID": "[当前表格块的唯一标识符，例如 'table_chunk_001']",
                        "分块类型": "表格",
                        "原文件名称": {file_name},
                        "原文件地址": {file_path},
                        "发布时间": "[文档发布日期，格式 YYYY-MM-DD，若无则留空]",
                        "发布厂商": "[文档发布机构或公司，若无则留空]",
                        "适用车型": "[文档适用的车型信息，若无则留空或使用数组如 ['车型A', '车型B']]"
                      }},
                      "上下文元数据": {{
                        "涉及页码": "[当前表格所在的页码，例如 7]",
                        "分块顺序": "[当前表格在整个文档中的逻辑顺序号，从1开始计数]",
                        "章节标题": "[当前表格所属的章节标题或表格标题，若无法明确判断则留空]"

                      }},
                      "内容类型元数据": {{
                        "内容数据格式": "HTML" // 指的是这个表格块的元数据是Markdown格式
                      }},
                      "业务元数据": {{
                        "标签": "[与表格内容相关的业务标签，可为空数组 []]"
                      }},
                      "表格元数据": {{
                        "行数": "[表格内容的总行数 (不含表头)]",
                        "表格备注"： "[表格的备注或描述性文字，若无法明确判断则留空]"
                      }},
                        "分块内容": {{ // 注意：根据文件中的表格内容实际存储为HTML格式。
                      //   "分块内容": "[此处为HTML内容]"
                      // }},
                      "系统管理元数据": {{
                        "修改时间": "[当前处理时间，ISO 8601格式]",
                        "版本号": "[解析脚本或模型的版本号]"
                      }}
                    }}

                 ## **3. 图片内容 (分块类型: "图片")**
                 {{
                  "基础元数据": {{
                    "分块ID": "[当前图片块的唯一标识符，例如 'image_chunk_001']",
                    "分块类型": "图片",
                    "原文件名称": {file_name},
                    "原文件地址": {file_path},
                    "发布时间": "[文档发布日期，格式 YYYY-MM-DD，若无则留空]",
                    "发布厂商": "[文档发布机构或公司，若无则留空]",
                    "适用车型": "[文档适用的车型信息，若无则留空或使用数组如 ['车型A', '车型B']]"
                  }},
                  "上下文元数据": {{
                    "涉及页码": "[当前图片所在的页码，例如 8]",
                    "分块顺序": "[当前图片在整个文档中的逻辑顺序号，从1开始计数]",
                    "章节标题": "[当前图片所属的章节标题或图片的描述性标题/图注，若无法明确判断则留空]"
                  }},
                  "内容类型元数据": {{
                    "内容数据格式": "JSON" // 指的是这个图片块的元数据是JSON格式
                  }},
                  "业务元数据": {{
                    "标签": "[与图片内容相关的业务标签，例如 '示意图', '电路图', 可为空数组 []]"
                  }},
                  "分块内容": {{
                    "图片内容": "[图片的描述或OCR提取的文本。如果图片本身需要存储，此处可为图片的文件名或Base64占位符，例如 'extracted_images/image_001.png' 或 'base64_placeholder']"
                  }},
                  "图片元数据": {{
                    "图片坐标": "[图片在页面中的坐标 [x1, y1, x2, y2]，左上角和右下角]",
                    "图片地址": "[图片被提取后保存的相对路径或标识符，例如 'images/image_chunk_001.png']",
                    "OCR文本": "[对图片进行OCR识别提取的文本内容，若无文本或无法识别则为空字符串]",
                    "OCR置信度": "[OCR识别文本的平均置信度，0到1之间的小数，例如 0.95，若无OCR则为0]"
                  }},
                  "系统管理元数据": {{
                    "修改时间": "[当前处理时间，ISO 8601格式]",
                    "版本号": "[解析脚本或模型的版本号]"
                  }}
                }}

                注意：
                1、页眉、页脚、品牌LOGO的内容忽略，不需要做任何处理。
                2、如果文本内容是针对图片的描述，则文本不需要单独分块处理，直接放在图片的分块内容中。
                3、如果文本内容仅是一个标题或小标题，则不需要单独分块处理，直接放在上下文元数据的章节标题中。
                4、如果文件最后一页的内容是“THANKS”或者“谢谢”等，则不需要处理。
                5、如果文件中的表格内容是空的，则不需要处理。
                6、如果文件中的表格跨页，则需要将表格的所有页码都列出，并且合并到一起。
                7、如果文本内容是针对表格的描述，则文本不需要单独分块处理，直接放在表格的分块内容中。
                8、如果文本内容跨页，则需要将所有页码都列出，并且合并到一起。
                9、如果文本内容仅是一个日期，则不需要做任何处理。
                10、只输出纯JSON，```json和```不需要输出

                请解析以下Markdown内容并按照上述格式输出:
                {content}
"""
        # print(prompt_template)
        return prompt_template

def main():
    # 设置API密钥
    api_key = "sk-7b9067ef70794d7286da8432be28f18c"
    processor = MarkdownProcessor(api_key)

    # 指定要处理的文件夹路径
    folder_path = r"Y:\产品中心2023\智研创新部\政策知识库\部分品牌近半年政策\待处理文件"

    # 创建输出目录（如果不存在）
    output_dir = ".\\OtherMD"
    os.makedirs(output_dir, exist_ok=True)

    # 遍历文件夹中的所有 .md 文件
    for filename in os.listdir(folder_path):
        if filename.endswith(".md"):
            file_path = os.path.join(folder_path, filename)

            print(f"正在处理文件：{file_path}")

            try:
                # 读取Markdown文件内容
                markdown_content = processor.read_markdown_file(file_path)

                # 处理Markdown内容，获取结构化数据
                structured_data = processor.process_markdown(markdown_content, file_path)

                # 如果是字符串，尝试转换为JSON对象
                if isinstance(structured_data, str):
                    try:
                        structured_data = json.loads(structured_data)
                    except json.JSONDecodeError as e:
                        print(f"无法解析模型返回的内容（{filename}）: {e}")
                        continue

                # 构建输出文件路径
                file_name_noext = os.path.splitext(filename)[0]
                output_file = os.path.join(output_dir, f"{file_name_noext}.txt")

                # 保存结构化数据到文件
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(structured_data, f, ensure_ascii=False, indent=2)

                print(f"已保存至：{output_file}")

            except Exception as e:
                print(f"处理文件 {filename} 时出错：{str(e)}")
                continue

    print("所有文件处理完成。")

if __name__ == "__main__":
    main()