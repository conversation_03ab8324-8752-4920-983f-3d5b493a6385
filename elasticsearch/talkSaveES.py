from elasticsearch import Elasticsearch, helpers
import os
import re

# --- Configuration ---
ELASTICSEARCH_URL = "https://my-elasticsearch-project-a8031b.es.us-east-1.aws.elastic.cloud:443"
API_KEY = "T0dyMHFaWUJfWFdDekFGWTRaV2c6XzhMMlRSZnNFQkpaS01tQ1hlQzE0QQ=="
INDEX_NAME = "search-nffp"

# TXT 文件路径
TXT_FILE_PATH = r"C:\Users\<USER>\Desktop\政策文件-提取内容框架-0506.txt"

# 元数据字段映射（中文键 → 英文字段名）
KEY_MAPPING = {
    '发布时间': 'publish_date',
    '厂商': 'manufacturer',
    '适用车型': 'applicable_model',
    '适用型号': 'applicable_version',
    '场景大类': 'scenario_category',
    '场景小类': 'scenario_subcategory',
    '关键字': 'keywords',
    '参考话术': 'reference_speech',
    '来源文件': 'source_document',
    '涉及页码': 'page_number'
}

# --- Elasticsearch Client Initialization ---
try:
    client = Elasticsearch(
        ELASTICSEARCH_URL,
        api_key=API_KEY
    )
    if not client.ping():
        raise ConnectionError("Failed to connect to Elasticsearch!")
    print("Successfully connected to Elasticsearch.")
except Exception as e:
    print(f"Error connecting to Elasticsearch: {e}")
    exit()


# --- 解析元数据核心逻辑 ---
def parse_block_metadata(block_content):
    """解析单个文本块内容，提取结构化元数据"""
    metadata = {}
    current_key = None
    current_value = []

    for line in block_content.split('\n'):
        line = line.strip()
        if not line:
            continue

        # 匹配键值对（支持中英文冒号）
        match = re.match(r'^(.+?)[：:]\s*(.*)$', line)
        if match:
            # 处理之前的字段
            if current_key is not None:
                en_key = KEY_MAPPING.get(current_key)
                if en_key:
                    metadata[en_key] = '\n'.join(current_value).strip()

            # 解析新字段
            current_key = match.group(1).strip()
            current_value = [match.group(2).strip()]
        else:
            # 多行内容追加到当前字段
            if current_key is not None:
                current_value.append(line)

    # 处理最后一个字段
    if current_key is not None:
        en_key = KEY_MAPPING.get(current_key)
        if en_key:
            metadata[en_key] = '\n'.join(current_value).strip()

    return metadata


# --- 文件解析主函数 ---
def parse_txt_by_blocks(file_path):
    """解析TXT文件并按****分割块，返回结构化数据"""
    blocks = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            raw_blocks = re.split(r'\s*[*]{4}\s*', f.read())

        for idx, block in enumerate(raw_blocks):
            if stripped_block := block.strip():
                blocks.append({
                    "block_number": idx + 1,
                    **parse_block_metadata(stripped_block)
                })

        print(f"解析到 {len(blocks)} 个有效数据块")
        return blocks

    except Exception as e:
        print(f"文件解析失败: {e}")
        return []


# --- Main Execution ---
if __name__ == "__main__":
    # 解析文件
    blocks_data = parse_txt_by_blocks(TXT_FILE_PATH)
    if not blocks_data:
        print("未找到有效数据块，程序退出")
        exit()

    # 准备批量导入数据
    file_basename = os.path.basename(TXT_FILE_PATH)
    actions = [{
        "_index": INDEX_NAME,
        "_source": {
            **block,
            "source_filename": file_basename,
            "source_filepath": TXT_FILE_PATH
        }
    } for block in blocks_data]

    # 执行批量写入
    print(f"准备导入 {len(actions)} 条数据到索引 {INDEX_NAME}...")
    try:
        with client.options(request_timeout=60):
            successes, errors = helpers.bulk(client, actions)
        print(f"成功导入 {successes} 条数据")
        if errors:
            print(f"发现 {len(errors)} 条错误:")
            for error in errors[:3]:  # 打印前3个错误
                print(f"类型: {error.get('type', '未知')}")
                print(f"原因: {error.get('reason', '未知')}")
    except Exception as e:
        print(f"批量导入失败: {e}")