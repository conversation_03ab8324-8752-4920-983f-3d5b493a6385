from elasticsearch import Elasticsearch
import ssl
import certifi
import dashscope
from dashscope import Generation

# 配置信息
ELASTICSEARCH_URL = "https://my-elasticsearch-project-a8031b.es.us-east-1.aws.elastic.cloud:443"
API_KEY = "T0dyMHFaWUJfWFdDekFGWTRaV2c6XzhMMlRSZnNFQkpaS01tQ1hlQzE0QQ=="
INDEX_NAME = "search-5vj3"   # 5vj3:列名和表名合在一起；byd1:比亚迪

# 大模型配置
QWEN_API_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
QWEN_API_KEY = "sk-882e296067b744289acf27e6e20f3ec0" #sk-7b9067ef70794d7286da8432be28f18c
dashscope.base_http_url = QWEN_API_URL

def extract_keywords(text):
    """使用大模型提取搜索关键词"""
    prompt = f"""请从以下汽车销售政策问题中提取3-5个最关键的搜索关键词，优先提取车型名称、指标名称（如：奖励系数、支持点位、完成率）、时间等实体。用空格分隔关键词。
    问题：{text}
    仅返回关键词，不要其他内容。
    """

    try:
        response = Generation.call(
            model="qwen-max",
            prompt=prompt,
            api_key=QWEN_API_KEY
        )
        keywords = response.output.text.strip()
        return keywords
    except Exception as e:
        print(f"提取关键词出错: {str(e)}")
        return text

def search_documents(query):
    try:
        # 提取关键词
        keywords = extract_keywords(query)
        print(f"提取的搜索关键词: {keywords}") # 调试：检查提取的关键词

        # 创建SSL上下文
        ssl_context = ssl.create_default_context(cafile=certifi.where())

        # 连接到Elasticsearch
        es = Elasticsearch(
            ELASTICSEARCH_URL,
            api_key=API_KEY,
            ssl_context=ssl_context,
            verify_certs=True
        )

        # 构建查询 - 修改版
        query_body = {
            "query": {
                "multi_match": {
                    "query": keywords,
                    "fields": [
                        # 提升包含特定实体的字段权重
                        "base_metadata.applicable_model^5",  # 车型名称高度相关
                        "table_metadata.table_values^4",   # 对于指标名称如"月度奖励系数"很重要
                        # 包含原始字段并调整权重
                        "chunk_content^3", # 如果实施方案1，此字段仍然非常有用
                        "context_metadata.section_title^2",
                        "business_metadata.tags^2",
                        "table_metadata.table_notes^2", # 表格备注可能包含关键词
                        "base_metadata.manufacturer^1",
                        "base_metadata.original_filename^1",
                        "image_metadata.ocr_text^1",
                        # 降低不太相关的字段权重或移除
                        # "base_metadata.chunk_type^1",
                        # "content_type_metadata.content_format^1"
                    ],
                    # 尝试 "most_fields"，因为关键词可能分散在不同字段
                    "type": "most_fields",
                    "tie_breaker": 0.3,
                    # 稍微放宽匹配要求
                    "minimum_should_match": "50%" # 或者试试 "2<75%" (如果关键词多于2个，则至少匹配2个，否则匹配75%)
                }
            },
            "size": 20,
            # 请求 table_metadata 以便后续格式化或直接检查
            "_source": ["base_metadata", "context_metadata", "business_metadata", "chunk_content", "table_metadata"]
        }
        # 可选：打印查询体用于调试
        # import json
        # print("发送给 Elasticsearch 的查询体:")
        # print(json.dumps(query_body, indent=2, ensure_ascii=False))

        response = es.search(index=INDEX_NAME, body=query_body)
        return response['hits']['hits']

    except Exception as e:
        print(f"查询出错: {str(e)}")
        return None

def format_context(hits):
    """格式化搜索结果为上下文"""
    context = []
    for idx, hit in enumerate(hits, 1):
        source = hit['_source']
        base_meta = source['base_metadata']
        context_meta = source['context_metadata']

        # 构建格式化的文本块
        text_block = f"""
相关度：{hit['_score']:.2f}
发布厂商：{base_meta.get('manufacturer', 'N/A')}
适用车型：{base_meta.get('applicable_model', 'N/A')}
分块类型：{base_meta.get('chunk_type', 'N/A')}
章节标题：{context_meta.get('section_title', 'N/A')}
内容：{source.get('chunk_content', 'N/A')}
涉及页码：{context_meta.get('page_number', 'N/A')}
原始文件：{base_meta.get('original_filename', 'N/A')}
---"""
        context.append(text_block)

        # 打印格式化结果
        print(f"\n结果 {idx} (相关性: {hit['_score']:.2f}):")
        print(f"发布厂商：{base_meta.get('manufacturer', 'N/A')}")
        print(f"适用车型：{base_meta.get('applicable_model', 'N/A')}")
        print(f"分块类型：{base_meta.get('chunk_type', 'N/A')}")
        print(f"章节标题：{context_meta.get('section_title', 'N/A')}")
        print(f"内容：{source.get('chunk_content', 'N/A')}")
        print(f"涉及页码：{context_meta.get('page_number', 'N/A')}")
        print(f"原始文件：{base_meta.get('original_filename', 'N/A')}")
        print("-" * 200)

    return "\n".join(context)

def get_llm_response(query, context):
    """获取大模型回复"""
    prompt = f"""请基于以下参考信息回答用户的问题。
                如果参考信息不足以回答问题，请明确告知。
                如果信息充分，请给出详细的回答。
                
                参考信息：
                {context}
                
                用户问题：{query}
                
                请给出专业、准确的回答："""

    try:
        response = Generation.call(
            model="qwen-max",
            prompt=prompt,
            api_key=QWEN_API_KEY
        )
        return response.output.text
    except Exception as e:
        print(f"调用大模型出错: {str(e)}")
        return "抱歉，生成回答时出现错误。"

def main():
    while True:
        query = input("\n请输入您的问题（输入'q'退出）: ")
        if query.lower() == 'q':
            break

        print("\n正在搜索相关信息...")
        # 1. 搜索文档
        hits = search_documents(query)
        if not hits:
            print("未找到相关信息")
            continue

        # 2. 格式化上下文
        context = format_context(hits)

        # 3. 获取大模型回复
        print("\n正在生成回答...")
        answer = get_llm_response(query, context)

        # 4. 输出回答
        print("\n=============== AI 回答 ===============")
        print(answer)
        print("=" * 50)

if __name__ == "__main__":
    main()