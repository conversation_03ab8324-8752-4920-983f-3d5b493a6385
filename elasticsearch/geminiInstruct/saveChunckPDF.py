from elasticsearch import Elasticsearch
import json
import ssl
import certifi
from datetime import datetime
import os

# Elasticsearch 配置
ELASTICSEARCH_URL = "https://my-elasticsearch-project-a8031b.es.us-east-1.aws.elastic.cloud:443"
API_KEY = "T0dyMHFaWUJfWFdDekFGWTRaV2c6XzhMMlRSZnNFQkpaS01tQ1hlQzE0QQ=="
INDEX_NAME = "search-byd1"

# 文件路径
FILE_PATH = r'/elasticsearch/比亚迪-202503-B-关于第二代元PLUS智驾版上市营销指导的通知_20250512_100555.txt'

def validate_date(date_str):
    """验证并格式化日期字符串"""
    if not date_str:
        return None
    try:
        # 尝试解析多种可能的日期格式
        for fmt in ['%Y-%m-%d', '%Y/%m/%d', '%Y%m%d']:
            try:
                return datetime.strptime(date_str, fmt).strftime('%Y-%m-%d')
            except ValueError:
                continue
        return None
    except Exception:
        return None


def convert_to_english_fields(item):
    """将中文字段名转换为英文"""
    english_data = {}

    # 基础元数据转换
    publish_date = validate_date(item.get("基础元数据", {}).get("发布时间"))
    base_metadata = {
        "chunk_id": item.get("基础元数据", {}).get("分块ID"),
        "chunk_type": item.get("基础元数据", {}).get("分块类型"),
        "original_filename": item.get("基础元数据", {}).get("原文件名称"),
        "original_filepath": item.get("基础元数据", {}).get("原文件地址"),
        "publish_date": publish_date,  # 使用处理后的日期
        "manufacturer": item.get("基础元数据", {}).get("发布厂商"),
        "applicable_model": item.get("基础元数据", {}).get("适用车型")
    }

    # 移除空值
    base_metadata = {k: v for k, v in base_metadata.items() if v is not None}
    english_data["base_metadata"] = base_metadata if base_metadata else None

    # 上下文元数据转换
    context_metadata = {
        "page_number": item.get("上下文元数据", {}).get("涉及页码"),
        "chunk_order": item.get("上下文元数据", {}).get("分块顺序"),
        "section_title": item.get("上下文元数据", {}).get("章节标题")
    }
    english_data["context_metadata"] = context_metadata

    # 内容类型元数据转换
    content_type_metadata = {
        "content_format": item.get("内容类型元数据", {}).get("内容数据格式")
    }
    english_data["content_type_metadata"] = content_type_metadata

    # 业务元数据转换
    business_metadata = {
        "tags": item.get("业务元数据", {}).get("标签", [])
    }
    english_data["business_metadata"] = business_metadata

    # 分块内容转换 - 使用安全的获取方式
    chunk_content = item.get("分块内容", {})
    if isinstance(chunk_content, dict):
        english_data["chunk_content"] = chunk_content.get("分块内容")
    else:
        english_data["chunk_content"] = None

    # 表格元数据转换
    table_metadata = item.get("表格元数据", {})
    if table_metadata:
        english_data["table_metadata"] = {
            "table_values": table_metadata.get("表格内容", []),
            "row_count": table_metadata.get("行数"),
            "table_notes": table_metadata.get("表格备注")
        }

    # 图片元数据转换
    image_metadata = item.get("图片元数据", {})
    if image_metadata:
        english_data["image_metadata"] = {
            "coordinates": image_metadata.get("图片坐标"),
            "image_path": image_metadata.get("图片地址"),
            "ocr_text": image_metadata.get("OCR文本"),
            "ocr_confidence": image_metadata.get("OCR置信度")
        }

    # 系统管理元数据转换
    system_metadata = {
        "modified_time": item.get("系统管理元数据", {}).get("修改时间"),
        "version": item.get("系统管理元数据", {}).get("版本号")
    }
    english_data["system_metadata"] = system_metadata

    # 移除所有None值的字段
    english_data = {k: v for k, v in english_data.items() if v is not None}

    return english_data


def main():
    try:
        # 创建 SSL 上下文
        ssl_context = ssl.create_default_context(cafile=certifi.where())

        # 连接到 Elasticsearch
        es = Elasticsearch(
            ELASTICSEARCH_URL,
            api_key=API_KEY,
            ssl_context=ssl_context,  # 使用SSL上下文
            verify_certs=True  # 启用证书验证
        )

        # 设置文件夹路径
        folder_path = r"/elasticsearch/BYD"

        success_count = 0
        error_count = 0

        # 遍历文件夹下的所有文件
        for filename in os.listdir(folder_path):
            file_path = os.path.join(folder_path, filename)

            # 跳过子目录
            if not os.path.isfile(file_path):
                continue

            try:
                # 读取文件
                with open(file_path, 'r', encoding='utf-8') as file:
                    try:
                        data = json.load(file)
                    except json.JSONDecodeError:
                        print(f"文件 {filename} 不是有效的JSON格式，已跳过")
                        error_count += 1
                        continue

                # 处理文件内容
                for item in data:
                    try:
                        # 转换字段名
                        converted_item = convert_to_english_fields(item)
                        if not converted_item:
                            print(f"警告：文件 {filename} 中存在空文档，已跳过")
                            error_count += 1
                            continue

                        # 导入到 Elasticsearch
                        result = es.index(
                            index=INDEX_NAME,
                            document=converted_item
                        )

                        if result['result'] == 'created':
                            success_count += 1
                            print(f"成功导入文档 ID: {result['_id']} (来自文件: {filename})")
                        else:
                            error_count += 1
                            print(f"导入失败，返回结果: {result} (来自文件: {filename})")

                    except Exception as e:
                        print(f"处理文件 {filename} 中的文档时出错: {str(e)}")
                        error_count += 1

            except Exception as e:
                print(f"读取文件 {filename} 时发生错误: {str(e)}")
                error_count += 1

        print(f"\n数据导入完成！总成功数: {success_count}, 总失败数: {error_count}")

    except Exception as e:
        print(f"程序执行出错: {str(e)}")

if __name__ == "__main__":
    main()