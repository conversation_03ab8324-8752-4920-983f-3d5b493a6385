import google.generativeai as genai
import os
import time

# 初始化客户端并设置API密钥
API_KEY = "AIzaSyAtHtoUQLT1sgqmYZDK8KZp6pvQ12wLkMM"
genai.configure(api_key=API_KEY)

# 本地PDF文件路径
file_path = r"Y:\产品中心2023\智研创新部\政策知识库\部分品牌近半年政策\政策文件原件\奥迪-202412-A-奥迪品牌2024年12月销售措施--经销商通知.pdf"
file_name = os.path.basename(file_path)
file_name_noext = os.path.splitext(file_name)[0]

# 在文件开头添加时间戳，用于生成唯一的输出文件名
timestamp = time.strftime("%Y%m%d_%H%M%S")
output_file = f"{file_name_noext}_{timestamp}.txt"

# 读取本地PDF文件
try:
    with open(file_path, "rb") as f:
        doc_data = f.read()
except FileNotFoundError:
    print(f"错误：文件未找到 - {file_path}")
    exit()
except Exception as e:
    print(f"读取文件时发生错误: {str(e)}")
    exit()

# 创建生成请求
prompt = f"""
                    # **角色**
                    你是一个高级文档解析与数据提取助手，专门处理包含文本、复杂表格和图片的PDF文件。

                    # **任务**
                    你的任务是精确地解析提供的PDF文件，识别并提取其中的文本块、表格和图片。对于每一种识别出的内容块（chunk），你都需要按照下面定义的严格JSON结构和字段要求进行格式化输出。最终结果应该是一个包含所有提取内容块的JSON对象数组。

                    # **输出格式与要求**
                    对于PDF中的每一个内容块（文本、表格、图片），都必须生成一个独立的JSON对象。请严格遵循以下针对不同内容类型的字段结构和说明：

                    ## **1. 文本内容 (分块类型: "文本")**
                    当内容块是纯文本时，使用以下JSON结构：
                    {{
                      "基础元数据": {{
                        "分块ID": "[当前文本块的唯一标识符，例如 'text_chunk_001']",
                        "分块类型": "文本",
                        "原文件名称": {file_name},
                        "原文件地址": {file_path},
                        "发布时间": "[文档发布日期，格式 YYYY-MM-DD，若无则留空]",
                        "发布厂商": "[文档发布机构或公司，若无则留空]",
                        "适用车型": "[文档适用的车型信息，若无则留空或使用数组如 ['车型A', '车型B']]"
                      }},
                      "上下文元数据": {{
                        "涉及页码": "[当前文本块所在的页码，例如 5 或 [5, 6] 表示跨页]",
                        "分块顺序": "[当前文本块在整个文档中的逻辑顺序号，从1开始计数]",
                        "章节标题": "[当前文本块所属的章节标题，若无法明确判断则留空]"
                      }},
                      "内容类型元数据": {{
                        "内容数据格式": "Markdown"
                      }},
                      "业务元数据": {{
                        "标签": "[与文本内容相关的业务标签，可为空数组 []]"
                      }},
                      "分块内容": {{
                        "分块内容": "[提取到的实际文本内容，使用Markdown格式，保留原始换行和基本格式]"
                      }},
                      "系统管理元数据": {{
                        "修改时间": "[当前处理时间，ISO 8601格式，例如 '2023-10-27T10:30:00Z']",
                        "版本号": "[解析脚本或模型的版本号，例如 'v1.0']"
                      }}
                    }}

                    ## **2. 表格内容 (分块类型: "表格")**
                    {{
                      "基础元数据": {{
                        "分块ID": "[当前表格块的唯一标识符，例如 'table_chunk_001']",
                        "分块类型": "表格",
                        "原文件名称": {file_name},
                        "原文件地址": {file_path},
                        "发布时间": "[文档发布日期，格式 YYYY-MM-DD，若无则留空]",
                        "发布厂商": "[文档发布机构或公司，若无则留空]",
                        "适用车型": "[文档适用的车型信息，若无则留空或使用数组如 ['车型A', '车型B']]"
                      }},
                      "上下文元数据": {{
                        "涉及页码": "[当前表格所在的页码，例如 7]",
                        "分块顺序": "[当前表格在整个文档中的逻辑顺序号，从1开始计数]",
                        "章节标题": "[当前表格所属的章节标题或表格标题，若无法明确判断则留空]"

                      }},
                      "内容类型元数据": {{
                        "内容数据格式": "HTML"
                      }},
                      "业务元数据": {{
                        "标签": "[与表格内容相关的业务标签，可为空数组 []]"
                      }},
                      "表格元数据": {{
                        "列名": ["[表格的第一列名", "表格的第二列名", "... （提取所有列标题）]"],
                        "列值": [
                          // 每一行是一个数组，对应列名的值
                          ["[行1列1的值]", "[行1列2的值]", "..."],
                          ["[行2列1的值]", "[行2列2的值]", "..."]
                          // ... 更多行
                        ],
                        "行数": "[表格内容的总行数 (不含表头)]",
                        "表格备注"： "[表格的备注或描述性文字，若无法明确判断则留空]"
                      }},
                        "分块内容": {{ // 注意：根据您的原始表格描述，表格的实际内容在“表格元数据”中。
                      //   "分块内容": "[此处存储表格的HTML表示形式]" // 如果需要HTML，则如此。否则根据您的定义，列名列值已足够。
                      // }},
                      "系统管理元数据": {{
                        "修改时间": "[当前处理时间，ISO 8601格式]",
                        "版本号": "[解析脚本或模型的版本号]"
                      }}
                    }}

                 ## **3. 图片内容 (分块类型: "图片")**
                 {{
                  "基础元数据": {{
                    "分块ID": "[当前图片块的唯一标识符，例如 'image_chunk_001']",
                    "分块类型": "图片",
                    "原文件名称": {file_name},
                    "原文件地址": {file_path},
                    "发布时间": "[文档发布日期，格式 YYYY-MM-DD，若无则留空]",
                    "发布厂商": "[文档发布机构或公司，若无则留空]",
                    "适用车型": "[文档适用的车型信息，若无则留空或使用数组如 ['车型A', '车型B']]"
                  }},
                  "上下文元数据": {{
                    "涉及页码": "[当前图片所在的页码，例如 8]",
                    "分块顺序": "[当前图片在整个文档中的逻辑顺序号，从1开始计数]",
                    "章节标题": "[当前图片所属的章节标题或图片的描述性标题/图注，若无法明确判断则留空]"
                  }},
                  "内容类型元数据": {{
                    "内容数据格式": "JSON" // 指的是这个图片块的元数据是JSON格式
                  }},
                  "业务元数据": {{
                    "标签": "[与图片内容相关的业务标签，例如 '示意图', '电路图', 可为空数组 []]"
                  }},
                  "分块内容": {{
                    "图片内容": "[图片的描述或OCR提取的文本。如果图片本身需要存储，此处可为图片的文件名或Base64占位符，例如 'extracted_images/image_001.png' 或 'base64_placeholder']"
                  }},
                  "图片元数据": {{
                    "图片坐标": "[图片在页面中的坐标 [x1, y1, x2, y2]，左上角和右下角]",
                    "图片地址": "[图片被提取后保存的相对路径或标识符，例如 'images/image_chunk_001.png']",
                    "OCR文本": "[对图片进行OCR识别提取的文本内容，若无文本或无法识别则为空字符串]",
                    "OCR置信度": "[OCR识别文本的平均置信度，0到1之间的小数，例如 0.95，若无OCR则为0]"
                  }},
                  "系统管理元数据": {{
                    "修改时间": "[当前处理时间，ISO 8601格式]",
                    "版本号": "[解析脚本或模型的版本号]"
                  }}
                }}

                注意：
                1、页眉、页脚、品牌LOGO的内容忽略，不需要做任何处理。
                2、如果文本内容是针对图片的描述，则文本不需要单独分块处理，直接放在图片的分块内容中。
                3、如果文本内容仅是一个标题或小标题，则不需要单独分块处理，直接放在上下文元数据的章节标题中。
                4、如果文件最后一页的内容是“THANKS”或者“谢谢”等，则不需要处理。
                5、如果文件中的表格内容是空的，则不需要处理。
                6、如果文件中的表格跨页，则需要将表格的所有页码都列出，并且合并到一起。
                7、如果文本内容是针对表格的描述，则文本不需要单独分块处理，直接放在表格的分块内容中。
                8、如果文本内容跨页，则需要将所有页码都列出，并且合并到一起。
                9、如果文本内容仅是一个日期，则不需要做任何处理。
                10、只输出纯JSON，```json和```不需要输出
"""
try:
    model = genai.GenerativeModel('gemini-2.5-pro-exp-03-25') # gemini-2.5-pro-exp-03-25
    response = model.generate_content(
        contents=[
            {
                'role': 'user',
                'parts': [{'mime_type': 'application/pdf', 'data': doc_data}]
            },
            {
                'role': 'user',
                'parts': [{'text': prompt}]
            }
        ],
        stream=True
    )

    # 实时打印并保存流式输出
    full_response = []
    print("开始接收流式响应：")

    with open(output_file, 'w', encoding='utf-8') as f:
        for chunk in response:
            chunk_text = chunk.text
            full_response.append(chunk_text)
            print(chunk_text, end="", flush=True)  # 实时显示输出内容
            f.write(chunk_text)  # 将内容写入文件
            f.flush()  # 确保内容及时写入

    print(f"\n\n数据已保存到文件：{output_file}")

except Exception as e:
    print(f"\nAPI调用失败: {str(e)}")
    with open(f"error_{timestamp}.txt", 'w', encoding='utf-8') as f:
        f.write(f"错误信息：{str(e)}")