# import os
# import json
#
# # 输入文件路径
# input_file = r'D:\PycahrmProjects\LangChain\elasticsearch\奥迪-202411-A-奥迪A6L 25年型发票支持政策--经销商通知_20250512_112746.txt'
#
# # 输出目录
# output_dir = 'output_chunks'
# os.makedirs(output_dir, exist_ok=True)
#
# # 读取文件内容
# with open(input_file, 'r', encoding='utf-8') as file:
#     data = json.load(file)
#
# # 遍历每块数据并保存为单独文件
# for chunk in data:
#     chunk_id = chunk.get('基础元数据', {}).get('分块ID', 'unknown_chunk')
#     output_file = os.path.join(output_dir, f'{chunk_id}.json')
#
#     # 构建完整的分块数据
#     complete_chunk = {
#         "基础元数据": chunk.get("基础元数据", {}),
#         "上下文元数据": chunk.get("上下文元数据", {}),
#         "内容类型元数据": chunk.get("内容类型元数据", {}),
#         "业务元数据": chunk.get("业务元数据", {}),
#         "分块内容": chunk.get("分块内容", {}),
#         "系统管理元数据": chunk.get("系统管理元数据", {})
#     }
#
#     # 根据类型动态添加图片元数据或表格元数据
#     if chunk.get("基础元数据", {}).get("分块类型") == "图片":
#         complete_chunk["图片元数据"] = chunk.get("图片元数据", {})
#     elif chunk.get("基础元数据", {}).get("分块类型") == "表格":
#         complete_chunk["表格元数据"] = chunk.get("表格元数据", {})
#
#     # 将完整的分块数据写入文件
#     with open(output_file, 'w', encoding='utf-8') as chunk_file:
#         json.dump(complete_chunk, chunk_file, ensure_ascii=False, indent=4)
#
# print(f"分块完成，分块文件保存在目录: {output_dir}")

import os
import json

# 输入文件路径
input_file = r'D:\PycahrmProjects\LangChain\elasticsearch\奥迪-202411-A-附件1-1：2024年一汽奥迪新Q8车型佣金政策_20250512_133906.txt'
# input_file = r'D:\PycahrmProjects\LangChain\elasticsearch\比亚迪-202503-B-关于第二代元PLUS智驾版上市营销指导的通知_20250512_100555.txt'


file_name = os.path.basename(input_file)
file_name_noext = os.path.splitext(file_name)[0]

# 输出文件路径
output_file = f'D:\PycahrmProjects\LangChain\elasticsearch\output_chunks\\{file_name_noext}.txt'

try:
    # 读取文件内容
    with open(input_file, 'r', encoding='utf-8') as file:
        data = json.load(file)
except json.JSONDecodeError as e:
    print(f"JSON 文件解析错误: {e}")
    print(f"错误位置: 行 {e.lineno}, 列 {e.colno}, 字符位置 {e.pos}")
    exit(1)

# 写入到统一的 txt 文件
with open(output_file, 'w', encoding='utf-8') as output:
    for chunk in data:
        for key, value in chunk.items():
            # 将字段名和字段值写入文件
            output.write(f"{key}:\n")
            if isinstance(value, dict) or isinstance(value, list):
                output.write(f"{json.dumps(value, ensure_ascii=False, indent=4)}\n")
            else:
                output.write(f"{value}\n")
        # 添加分隔符
        output.write("*******\n")

print(f"分块数据已保存至: {output_file}")