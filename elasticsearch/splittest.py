import json

input_file = r'D:\PycahrmProjects\LangChain\elasticsearch\奥迪-202411-A-附件1-1：2024年一汽奥迪新Q8车型佣金政策_20250512_133906.txt'

try:
    with open(input_file, 'r', encoding='utf-8') as file:
        content = file.read()
        print("文件内容预览：")
        print(content[:500])  # 打印前 500 个字符
        data = json.loads(content)
except json.JSONDecodeError as e:
    print(f"JSON 文件解析错误: {e}")
    print(f"错误位置: 行 {e.lineno}, 列 {e.colno}, 字符位置 {e.pos}")