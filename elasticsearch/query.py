from elasticsearch import Elasticsearch
from sentence_transformers import SentenceTransformer
import requests

# --- 配置 ---
ES_URL = "https://my-elasticsearch-project-a8031b.es.us-east-1.aws.elastic.cloud:443"
ES_API_KEY = "T0dyMHFaWUJfWFdDekFGWTRaV2c6XzhMMlRSZnNFQkpaS01tQ1hlQzE0QQ=="
INDEX_NAME = "search-3lej"
QWEN_API_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"  # 替换成你的Qwen服务器地址
QWEN_API_KEY = "sk-7b9067ef70794d7286da8432be28f18c"  # 如果需要API密钥

# 初始化 Elasticsearch 客户端
es = Elasticsearch(hosts=[ES_URL],api_key=ES_API_KEY)

# 加载本地向量化模型（可替换为你自己的模型）
embedding_model = SentenceTransformer('all-MiniLM-L6-v2')

def call_qwen_api(prompt):
    headers = {
        'Authorization': f'Bearer {QWEN_API_KEY}',
        'Content-Type': 'application/json'
    }
    data = {
        "model": "qwen-max-2025-01-25",  # 确保这是你想要调用的模型名称
        "messages": [
            {"role": "system", "content": "你是一个知识助手，请基于提供的上下文回答问题。"},
            {"role": "user", "content": prompt}
        ]
    }
    response = requests.post(QWEN_API_URL, json=data, headers=headers)
    if response.status_code == 200:
        return response.json()['choices'][0]['message']['content']
    else:
        raise Exception(f"Error calling Qwen API: {response.text}")

# --- 混合检索函数 ---
def hybrid_search(query, top_k=3):
        query_vector = embedding_model.encode(query).tolist()
        print("query_vector:", query_vector)

        search_body = {
            "query": {
                "bool": {
                    "should": [
                        {
                            "match": {
                                "content": {
                                    "query": query,
                                    "boost": 0.3
                                }
                            }
                        },
                        {
                            "script_score": {
                                "query": {"match_all": {}},
                                "script": {
                                    "source": """
                                    double dotProduct = 0.0;
                                    double magnitude1 = 0.0;
                                    double magnitude2 = 0.0;
                                    for (int i = 0; i < params.query_vector.length; i++) {
                                        dotProduct += params.query_vector[i] * doc['content_vector'][i];
                                        magnitude1 += Math.pow(params.query_vector[i], 2);
                                        magnitude2 += Math.pow(doc['content_vector'][i], 2);
                                    }
                                    magnitude1 = Math.sqrt(magnitude1);
                                    magnitude2 = Math.sqrt(magnitude2);
                                    return dotProduct / (magnitude1 * magnitude2);
                                    """,
                                    "params": {"query_vector": query_vector}
                                }
                            }
                        }
                    ]
                }
            },
            "_source": ["content"],
            "size": top_k
        }

        result = es.search(index=INDEX_NAME, body=search_body)
        hits = result.get("hits", {}).get("hits", [])
        contexts = [hit["_source"]["content"] for hit in hits]

        return contexts

# --- 主流程函数 ---
def answer_user_question(user_query):
    contexts = hybrid_search(user_query, top_k=3)

    if not contexts:
        return "抱歉，我没有找到相关的内容来回答这个问题。"

    context_str = "\n".join([f"{i+1}. {ctx}" for i, ctx in enumerate(contexts)])
    prompt = f"请基于以下信息回答问题：\n\n{context_str}\n\n问题：{user_query}"

    try:
        answer = call_qwen_api(prompt)
    except Exception as e:
        answer = f"调用Qwen API时发生错误: {e}"

    return answer

# --- 示例使用 ---
if __name__ == "__main__":
    user_question = input("请输入你的问题：")
    response = answer_user_question(user_question)
    print("\n回答：\n", response)