from elasticsearch import Elasticsearch
import json
from typing import List, Dict, Any

# 初始化 Elasticsearch 客户端
client = Elasticsearch(
    "https://my-elasticsearch-project-a8031b.es.us-east-1.aws.elastic.cloud:443",
    api_key="T0dyMHFaWUJfWFdDekFGWTRaV2c6XzhMMlRSZnNFQkpaS01tQ1hlQzE0QQ"
)

def semantic_search(query_text: str, index_name: str = "search-jph6", size: int = 5) -> List[Dict[str, Any]]:
    """
    执行语义搜索，支持文本、表格和图片内容检索
    """
    try:
        search_query = {
            "query": {
                "bool": {
                    "should": [
                        # 文本内容匹配
                        {
                            "match": {
                                "分块内容": {
                                    "query": query_text,
                                    "boost": 1.0
                                }
                            }
                        },
                        # 表格内容匹配
                        {
                            "nested": {
                                "path": "表格元数据",
                                "query": {
                                    "bool": {
                                        "should": [
                                            {"match": {"表格元数据.列名": query_text}},
                                            {"match": {"表格元数据.列值": query_text}},
                                            {"match": {"表格元数据.表格备注": query_text}}
                                        ]
                                    }
                                },
                                "boost": 1.5
                            }
                        },
                        # 图片内容匹配
                        {
                            "nested": {
                                "path": "图片元数据",
                                "query": {
                                    "bool": {
                                        "should": [
                                            {"match": {"图片元数据.OCR文本": query_text}},
                                            {"match": {"分块内容.图片内容": query_text}}
                                        ]
                                    }
                                },
                                "boost": 1.2
                            }
                        }
                    ],
                    "minimum_should_match": 1
                }
            },
            "highlight": {
                "fields": {
                    "分块内容": {"pre_tags": ["<em>"], "post_tags": ["</em>"]},
                    "表格元数据.列值": {"pre_tags": ["<em>"], "post_tags": ["</em>"]},
                    "图片元数据.OCR文本": {"pre_tags": ["<em>"], "post_tags": ["</em>"]}
                }
            },
            "_source": ["分块内容", "基础元数据", "上下文元数据", "表格元数据", "图片元数据"],
            "size": size
        }

        response = client.search(index=index_name, body=search_query)

        search_results = []
        for hit in response["hits"]["hits"]:
            source = hit["_source"]
            result = {
                "得分": hit["_score"],
                "文件名": source["基础元数据"]["原文件名称"],
                "页码": source["上下文元数据"]["涉及页码"],
                "分块类型": source["基础元数据"]["分块类型"],
                "高亮": []
            }

            # 根据不同分块类型处理内容
            if "分块类型" in source["基础元数据"]:
                if source["基础元数据"]["分块类型"] == "文本":
                    result["内容"] = source["分块内容"]
                    result["高亮"] = hit.get("highlight", {}).get("分块内容", [])
                elif source["基础元数据"]["分块类型"] == "表格":
                    result["内容"] = {
                        "列名": source["表格元数据"]["列名"],
                        "列值": source["表格元数据"]["列值"],
                        "表格备注": source["表格元数据"].get("表格备注", "")
                    }
                elif source["基础元数据"]["分块类型"] == "图片":
                    result["内容"] = source["分块内容"].get("图片内容", "")
                    result["OCR文本"] = source["图片元数据"].get("OCR文本", "")

            search_results.append(result)

        return search_results

    except Exception as e:
        print(f"搜索出错: {str(e)}")
        return []

def print_results(results: List[Dict[str, Any]]) -> None:
    """
    打印格式化的搜索结果
    """
    if not results:
        print("未找到相关结果")
        return

    for idx, result in enumerate(results, 1):
        print(f"\n结果 {idx}:")
        print(f"得分: {result['得分']:.2f}")
        print(f"文件名: {result['文件名']}")
        print(f"页码: {result['页码']}")
        print(f"类型: {result['分块类型']}")

        if result['分块类型'] == "表格":
            print("表格内容:")
            print("列名:", result['内容']['列名'])
            print("数据:", result['内容']['列值'])
            if result['内容']['表格备注']:
                print("备注:", result['内容']['表格备注'])
        elif result['分块类型'] == "图片":
            print("图片描述:", result['内容'])
            if result.get('OCR文本'):
                print("OCR文本:", result['OCR文本'])
        else:
            print("内容:", result['内容'])

        if result.get('高亮'):
            print("高亮片段:", "\n".join(result['高亮']))
        print("-" * 50)

def main():
    """主函数"""
    search_queries = [
        "元PLUS的核心卖点",
        "车辆安全配置",
        "动力性能参数"
    ]

    for query in search_queries:
        print(f"\n执行搜索: '{query}'")
        results = semantic_search(query)
        print_results(results)

if __name__ == "__main__":
    main()