from elasticsearch import Elasticsearch, helpers
import os
import re

# --- Configuration ---
ELASTICSEARCH_URL = "https://my-elasticsearch-project-a8031b.es.us-east-1.aws.elastic.cloud:443"
API_KEY = "T0dyMHFaWUJfWFdDekFGWTRaV2c6XzhMMlRSZnNFQkpaS01tQ1hlQzE0QQ=="
INDEX_NAME = "search-e77l"

# TXT 文件路径
TXT_FILE_PATH = r"C:\Users\<USER>\Desktop\政策文件-提取内容框架-0506.txt"


# --- Elasticsearch Client Initialization ---
try:
    client = Elasticsearch(
        ELASTICSEARCH_URL,
        api_key=API_KEY
    )
    if not client.ping():
        raise ConnectionError("Failed to connect to Elasticsearch!")
    print("Successfully connected to Elasticsearch.")
except Exception as e:
    print(f"Error connecting to Elasticsearch: {e}")
    exit()


# --- Function to parse TXT file by **** sections ---
def parse_txt_by_blocks(file_path):
    """
    Reads a .txt file and splits it into blocks separated by lines of '****'.
    Returns a list of dicts with 'block_number', 'content'.
    """
    blocks = []

    if not os.path.exists(file_path):
        print(f"Error: File not found at {file_path}")
        return blocks

    print(f"Parsing TXT file: {os.path.basename(file_path)}")

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            full_text = f.read()

        # 使用 '****' 作为分隔符切分文本块
        raw_blocks = re.split(r'\s*[*]{4}\s*', full_text)

        # 去除前后空白，并过滤掉空块
        for idx, block in enumerate(raw_blocks):
            stripped_block = block.strip()
            if stripped_block:
                blocks.append({
                    "block_number": idx + 1,
                    "content": stripped_block
                })

        print(f"  Found {len(blocks)} non-empty blocks.")

    except Exception as e:
        print(f"Error reading or parsing TXT file '{file_path}': {e}")

    return blocks


# --- Main Execution ---
if __name__ == "__main__":
    # 解析文件
    blocks_data = parse_txt_by_blocks(TXT_FILE_PATH)

    if not blocks_data:
        print("No blocks found in the TXT file. Exiting.")
        exit()

    # 构建 bulk 数据
    all_actions_for_bulk = []
    file_basename = os.path.basename(TXT_FILE_PATH)

    for block in blocks_data:
        doc = {
            "_index": INDEX_NAME,
            "_source": {
                "block_number": block["block_number"],
                "content": block["content"],
                "source_filename": file_basename,
                "source_filepath": TXT_FILE_PATH
            }
        }
        all_actions_for_bulk.append(doc)

    # 批量写入 Elasticsearch
    print(f"\nPreparing to index {len(all_actions_for_bulk)} documents to index '{INDEX_NAME}'...")
    try:
        print("Sending data to Elasticsearch...")
        with client.options(request_timeout=60):
            successes, errors = helpers.bulk(client, all_actions_for_bulk, raise_on_error=False)

        print(f"Bulk indexing complete.")
        print(f"Successfully indexed: {successes}")
        if errors:
            print(f"Errors encountered during indexing: {len(errors)}")
            for i, error_info in enumerate(errors):
                print(f"  Error {i + 1}:")
                if 'index' in error_info and isinstance(error_info['index'], dict):
                    print(f"    Status: {error_info['index'].get('status')}")
                    print(f"    Reason: {error_info['index'].get('error', {}).get('reason', 'N/A')}")

    except Exception as e:
        print(f"An error occurred during bulk indexing: {e}")