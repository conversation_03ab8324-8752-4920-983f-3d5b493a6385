from elasticsearch import Elasticsearch
from elasticsearch.exceptions import AuthenticationException, ConnectionError
import dashscope
from dashscope.aigc.generation import Generation

# 配置信息
ES_URL = "https://my-elasticsearch-project-a8031b.es.us-east-1.aws.elastic.cloud:443"
ES_API_KEY = "T0dyMHFaWUJfWFdDekFGWTRaV2c6XzhMMlRSZnNFQkpaS01tQ1hlQzE0QQ=="
INDEX_NAME = "search-e77l"
QWEN_API_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
QWEN_API_KEY = "sk-7b9067ef70794d7286da8432be28f18c"


def es_keyword_search(keyword):
    """
    Elasticsearch关键词搜索函数
    """
    try:
        # 创建Elasticsearch客户端
        es = Elasticsearch(
            hosts=[ES_URL],
            api_key=ES_API_KEY,
            verify_certs=True  # 生产环境建议保持True
        )

        # 构建查询语句
        query = {
            "query": {
                "match": {
                    "content": keyword  # 根据实际字段名修改
                }
            },
            "size": 10  # 返回前5条结果
        }

        # 执行搜索
        response = es.search(
            index=INDEX_NAME,
            body=query
        )

        # 处理结果
        results = []
        for hit in response['hits']['hits']:
            source = hit['_source']
            results.append({
                "id": hit['_id'],
                "score": hit['_score'],
                "content": source.get('content', ''),
                # 添加其他需要返回的字段
            })

        return results

    except AuthenticationException as e:
        print(f"认证失败: {e.info}")
    except ConnectionError as e:
        print(f"连接错误: {e.info}")
    except Exception as e:
        print(f"搜索异常: {str(e)}")
    return []


def qwen_generate_response(prompt):
    """
    调用Qwen生成响应（可选）
    """
    dashscope.api_key = QWEN_API_KEY
    dashscope.base_http_url = QWEN_API_URL

    try:
        response = Generation.call(
            model="qwen-max-2025-01-25",
            prompt=prompt,
            max_length=500
        )
        return response.output.text
    except Exception as e:
        print(f"Qwen调用失败: {str(e)}")
        return None


if __name__ == "__main__":
    # 示例搜索关键词
    search_term = "比亚迪元PLUS销售话术"

    # 执行Elasticsearch搜索
    search_results = es_keyword_search(search_term)

    print(f"找到 {len(search_results)} 条结果:")
    for idx, result in enumerate(search_results, 1):
        print(f"\n结果 {idx} (ID: {result['id']}, 相关性: {result['score']:.2f}):")
        print(result['content'][:200] + "...")  # 显示前200字符

    # 可选：调用Qwen处理结果
    if search_results:
        qwen_prompt = f"基于以下搜索结果，回复'{search_term}'\n" + "\n".join(
            [res['content'][:300] for res in search_results])
        generated_text = qwen_generate_response(qwen_prompt)

        if generated_text:
            print("\n=== Qwen生成总结 ===")
            print(generated_text)
    else:
        print(f"没有找到相关{search_term}，无法生成总结。")