import pandas as pd
import numpy as np
from datetime import datetime

def merge_excel_sheets(input_file, output_file):
    # 读取所有相关sheet
    sheets = ['均值', '标准差', '中位数', '方差']
    dfs = {}

    # 读取每个sheet的数据
    for sheet in sheets:
        dfs[sheet] = pd.read_excel(input_file, sheet_name=sheet)

    # 创建一个空的DataFrame来存储合并后的数据
    merged_data = pd.DataFrame()

    # 对每个sheet的数据进行处理
    for sheet_name, df in dfs.items():
        # 添加数据类型列
        df['数据类型'] = sheet_name

        # 如果是第一个sheet，直接添加到merged_data
        if merged_data.empty:
            merged_data = df
        else:
            # 将当前sheet的数据追加到merged_data
            merged_data = pd.concat([merged_data, df], ignore_index=True)

    # 按功能项和数据类型排序
    merged_data = merged_data.sort_values(['功能项', '数据类型'])

    # 重置索引
    merged_data = merged_data.reset_index(drop=True)

    # 生成带时间戳的输出文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"数据汇总结果_{timestamp}.xlsx"

    # 保存到新的Excel文件
    merged_data.to_excel(output_file, index=False, sheet_name='合并数据')
    print(f"数据已成功合并并保存到 {output_file}")

if __name__ == "__main__":
    input_file = "数据示例.xlsx"
    output_file = "数据汇总结果.xlsx"
    merge_excel_sheets(input_file, output_file)
